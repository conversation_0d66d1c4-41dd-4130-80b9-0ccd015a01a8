import { PrismaClient } from '@prisma/client';
export class GpsService {
    prisma;
    baseUrl;
    credentials;
    authToken = null;
    tokenExpiry = null;
    constructor(prisma) {
        this.prisma = prisma;
        this.baseUrl = process.env.GPS_API_BASE_URL || 'http://smartelock.net:8088';
        this.credentials = {
            username: process.env.GPS_API_USERNAME || '',
            password: process.env.GPS_API_PASSWORD || ''
        };
        if (!this.credentials.username || !this.credentials.password) {
            console.warn('⚠️ GPS API credentials not found in environment variables');
            console.warn('💡 Please set GPS_API_USERNAME and GPS_API_PASSWORD in .env file');
        }
    }
    /**
     * Login to GPS API and get authentication token with retry mechanism
     */
    async login(retryCount = 0) {
        const maxRetries = 3;
        console.log(`🔐 Logging in to GPS API... (attempt ${retryCount + 1}/${maxRetries + 1})`);
        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout
            const response = await fetch(`${this.baseUrl}/user/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify({
                    username: this.credentials.username,
                    password: this.credentials.password
                }),
                signal: controller.signal
            });
            clearTimeout(timeoutId);
            if (!response.ok) {
                throw new Error(`GPS API login failed: ${response.status} ${response.statusText}`);
            }
            const result = await response.json();
            // GPS API returns code 200 for success, not 0
            if (result.code !== 200 || !result.data.token) {
                throw new Error(`GPS API login failed: ${result.message}`);
            }
            this.authToken = result.data.token;
            // Set token expiry to 1 hour from now (adjust based on actual API behavior)
            this.tokenExpiry = new Date(Date.now() + 60 * 60 * 1000);
            console.log('✅ GPS API login successful');
            return this.authToken;
        }
        catch (error) {
            console.error(`❌ GPS API login error (attempt ${retryCount + 1}):`, error);
            // Retry logic
            if (retryCount < maxRetries && error instanceof Error) {
                const isRetryableError = error.name === 'AbortError' ||
                    error.message.includes('ECONNRESET') ||
                    error.message.includes('ETIMEDOUT') ||
                    error.message.includes('500') ||
                    error.message.includes('502') ||
                    error.message.includes('503');
                if (isRetryableError) {
                    const delay = Math.pow(2, retryCount) * 1000; // Exponential backoff
                    console.log(`⏳ Retrying GPS login in ${delay}ms...`);
                    await new Promise(resolve => setTimeout(resolve, delay));
                    return this.login(retryCount + 1);
                }
            }
            throw error;
        }
    }
    /**
     * Get valid authentication token (login if needed)
     */
    async getValidToken() {
        // Check if we have a valid token
        if (this.authToken && this.tokenExpiry && this.tokenExpiry > new Date()) {
            return this.authToken;
        }
        // Login to get new token
        return await this.login();
    }
    /**
     * Get all devices from GPS API
     */
    async getAllDevices() {
        console.log('📍 Fetching all devices from GPS API...');
        try {
            const token = await this.getValidToken();
            const response = await fetch(`${this.baseUrl}/device/list_all`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify({
                    timeOffset: 0
                })
            });
            if (!response.ok) {
                throw new Error(`GPS API request failed: ${response.status} ${response.statusText}`);
            }
            const result = await response.json();
            // GPS API returns code 200 for success
            if (result.code !== 200) {
                throw new Error(`GPS API error: ${result.message}`);
            }
            console.log(`✅ Retrieved ${result.data.length} devices from GPS API`);
            return result.data;
        }
        catch (error) {
            console.error('❌ Error fetching devices from GPS API:', error);
            throw error;
        }
    }
    /**
     * Get GPS position data for cronjob - simplified version
     */
    async getGpsPositionData() {
        console.log('📍 Fetching GPS position data for cronjob...');
        try {
            const devices = await this.getAllDevices();
            console.log(`✅ Retrieved ${devices.length} GPS devices for position update`);
            return devices;
        }
        catch (error) {
            console.error('❌ Error fetching GPS position data:', error);
            throw error;
        }
    }
    /**
     * Get GPS position data for E-Seal devices and format for Beacukai API
     */
    async getESealPositionData(esealId) {
        try {
            // Get E-Seal from database to find device mapping
            const eseal = await this.prisma.eSeal.findUnique({
                where: { id: esealId }
            });
            if (!eseal) {
                console.error(`❌ E-Seal not found: ${esealId}`);
                return null;
            }
            // Get GPS data for this device
            const devices = await this.getAllDevices();
            if (devices.length === 0) {
                console.warn(`⚠️ No GPS data found for device: ${eseal.noImei}`);
                return null;
            }
            const device = devices[0];
            if (!device) {
                console.warn(`⚠️ No device data found for: ${eseal.noImei}`);
                return null;
            }
            // Format data for Beacukai API
            return {
                latitude: device.lat.toString(),
                longitude: device.lng.toString(),
                address: `GPS Location: ${device.lat}, ${device.lng}`, // TODO: Reverse geocoding
                altitude: '0', // GPS API doesn't provide altitude
                speed: device.speed.toString(),
                battery: device.devBatteryPCT.toString(),
                event: device.online ? '0' : '1' // 0 = Normal, 1 = Offline
            };
        }
        catch (error) {
            console.error('❌ Error getting E-Seal position data:', error);
            return null;
        }
    }
    /**
     * Test GPS API connection
     */
    async testConnection() {
        try {
            console.log('🧪 Testing GPS API connection...');
            const devices = await this.getAllDevices();
            console.log(`✅ GPS API connection test successful - found ${devices.length} devices`);
            return true;
        }
        catch (error) {
            console.error('❌ GPS API connection test failed:', error);
            return false;
        }
    }
}
