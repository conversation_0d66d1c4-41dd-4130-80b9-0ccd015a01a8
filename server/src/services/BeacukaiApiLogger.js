import { PrismaClient } from '@prisma/client';
import { randomUUID } from 'crypto';
export class BeacukaiApiLogger {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    /**
     * Log API call to database
     */
    async logApiCall(data) {
        try {
            await this.prisma.requestLog.create({
                data: {
                    id: randomUUID(),
                    requestUrl: data.requestUrl,
                    requestBody: data.requestBody || null,
                    requestHeader: data.requestHeader || undefined,
                    status: data.status,
                    responseBody: data.responseBody || null,
                    isSync: data.isSync || false,
                },
            });
            console.log('📝 API call logged to database:', {
                url: data.requestUrl,
                status: data.status,
                isSync: data.isSync,
            });
        }
        catch (error) {
            console.error('❌ Failed to log API call to database:', error);
        }
    }
    /**
     * Get recent API logs
     */
    async getRecentLogs(limit = 50) {
        return await this.prisma.requestLog.findMany({
            orderBy: {
                createdAt: 'desc',
            },
            take: limit,
        });
    }
    /**
     * Get logs by URL pattern
     */
    async getLogsByUrl(urlPattern, limit = 20) {
        return await this.prisma.requestLog.findMany({
            where: {
                requestUrl: {
                    contains: urlPattern,
                },
            },
            orderBy: {
                createdAt: 'desc',
            },
            take: limit,
        });
    }
    /**
     * Get failed API calls (status >= 400)
     */
    async getFailedLogs(limit = 20) {
        return await this.prisma.requestLog.findMany({
            where: {
                status: {
                    gte: 400,
                },
            },
            orderBy: {
                createdAt: 'desc',
            },
            take: limit,
        });
    }
    /**
     * Get API call statistics
     */
    async getApiStats() {
        const [total, successful, failed] = await Promise.all([
            this.prisma.requestLog.count(),
            this.prisma.requestLog.count({ where: { status: { lt: 400 } } }),
            this.prisma.requestLog.count({ where: { status: { gte: 400 } } }),
        ]);
        return {
            total,
            successful,
            failed,
            successRate: total > 0 ? (successful / total) * 100 : 0,
        };
    }
}
