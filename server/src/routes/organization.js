import { Hono } from "hono";
import { auth } from "../auth";
import { PrismaClient } from "@prisma/client";
import { randomUUID } from "crypto";
const prisma = new PrismaClient();
const app = new Hono();
// Get all organizations (for dropdowns, etc.)
app.get('/', async (c) => {
    try {
        const organizations = await prisma.organization.findMany({
            select: {
                id: true,
                name: true,
            },
            orderBy: {
                name: 'asc',
            },
        });
        return c.json({ success: true, data: organizations });
    }
    catch (error) {
        console.error('Error fetching organizations:', error);
        return c.json({ success: false, error: 'Failed to fetch organizations' }, 500);
    }
});
/**
 * Custom organization routes for superadmin operations
 * These routes bypass the default organization plugin restrictions
 */
// Update organization - allow superadmin to update any organization
app.post("/update", async (c) => {
    try {
        const session = await auth.api.getSession({ headers: c.req.header() });
        if (!session?.user) {
            return c.json({ error: "Unauthorized" }, 401);
        }
        const body = await c.req.json();
        const { organizationId, data } = body;
        if (!organizationId || !data) {
            return c.json({ error: "Organization ID and data are required" }, 400);
        }
        // Check if user is superadmin or organization member with proper role
        const userRole = session.user.role;
        let canUpdate = false;
        if (userRole === "superadmin") {
            canUpdate = true;
        }
        else {
            // Check if user is member of the organization with proper permissions
            const member = await prisma.member.findFirst({
                where: {
                    organizationId,
                    userId: session.user.id,
                    role: { in: ["owner", "admin"] }
                }
            });
            canUpdate = !!member;
        }
        if (!canUpdate) {
            return c.json({ error: "Insufficient permissions" }, 403);
        }
        // Update the organization
        const updatedOrg = await prisma.organization.update({
            where: { id: organizationId },
            data: {
                name: data.name,
                slug: data.slug,
                logo: data.logo,
                metadata: data.metadata
            }
        });
        return c.json({ success: true, data: updatedOrg });
    }
    catch (error) {
        console.error("Error updating organization:", error);
        return c.json({ error: "Failed to update organization" }, 500);
    }
});
// Create organization - allow superadmin to create organization without being added as member
app.post("/create", async (c) => {
    try {
        const session = await auth.api.getSession({ headers: c.req.header() });
        if (!session?.user) {
            return c.json({ error: "Unauthorized" }, 401);
        }
        const body = await c.req.json();
        const { name, slug, logo, metadata } = body;
        if (!name || !slug) {
            return c.json({ error: "Name and slug are required" }, 400);
        }
        // Check if user is superadmin
        const userRole = session.user.role;
        if (userRole !== "superadmin") {
            return c.json({ error: "Only superadmin can use this endpoint" }, 403);
        }
        // Check if slug is already taken
        const existingOrg = await prisma.organization.findUnique({
            where: { slug }
        });
        if (existingOrg) {
            return c.json({ error: "Organization slug already exists" }, 400);
        }
        // Create organization without adding creator as member
        const newOrg = await prisma.organization.create({
            data: {
                id: randomUUID(),
                name,
                slug,
                logo: logo || null,
                metadata: metadata ? metadata : undefined,
                createdAt: new Date()
            }
        });
        return c.json({ success: true, data: newOrg });
    }
    catch (error) {
        console.error("Error creating organization:", error);
        return c.json({ error: "Failed to create organization" }, 500);
    }
});
// Delete organization - allow superadmin to delete any organization
app.post("/delete", async (c) => {
    try {
        const session = await auth.api.getSession({ headers: c.req.header() });
        if (!session?.user) {
            return c.json({ error: "Unauthorized" }, 401);
        }
        const body = await c.req.json();
        const { organizationId } = body;
        if (!organizationId) {
            return c.json({ error: "Organization ID is required" }, 400);
        }
        // Check if user is superadmin or organization owner
        const userRole = session.user.role;
        let canDelete = false;
        if (userRole === "superadmin") {
            canDelete = true;
        }
        else {
            // Check if user is owner of the organization
            const member = await prisma.member.findFirst({
                where: {
                    organizationId,
                    userId: session.user.id,
                    role: "owner"
                }
            });
            canDelete = !!member;
        }
        if (!canDelete) {
            return c.json({ error: "Insufficient permissions" }, 403);
        }
        // Delete all related data first
        await prisma.$transaction(async (tx) => {
            // Delete invitations
            await tx.invitation.deleteMany({
                where: { organizationId }
            });
            // Delete members
            await tx.member.deleteMany({
                where: { organizationId }
            });
            // Update sessions to remove activeOrganizationId
            await tx.session.updateMany({
                where: { activeOrganizationId: organizationId },
                data: { activeOrganizationId: null }
            });
            // Delete the organization
            await tx.organization.delete({
                where: { id: organizationId }
            });
        });
        return c.json({ success: true, message: "Organization deleted successfully" });
    }
    catch (error) {
        console.error("Error deleting organization:", error);
        return c.json({ error: "Failed to delete organization" }, 500);
    }
});
// List all organizations - for superadmin to see all organizations
app.get("/list-all", async (c) => {
    try {
        const session = await auth.api.getSession({ headers: c.req.header() });
        if (!session?.user) {
            return c.json({ error: "Unauthorized" }, 401);
        }
        const userRole = session.user.role;
        if (userRole !== "superadmin") {
            return c.json({ error: "Only superadmin can access this endpoint" }, 403);
        }
        const page = parseInt(c.req.query("page") || "1");
        const limit = parseInt(c.req.query("limit") || "10");
        const search = c.req.query("search") || "";
        const offset = (page - 1) * limit;
        const where = search ? {
            OR: [
                { name: { contains: search, mode: "insensitive" } },
                { slug: { contains: search, mode: "insensitive" } }
            ]
        } : {};
        const [organizations, total] = await Promise.all([
            prisma.organization.findMany({
                where,
                include: {
                    members: {
                        include: {
                            user: {
                                select: {
                                    id: true,
                                    name: true,
                                    email: true
                                }
                            }
                        }
                    },
                    _count: {
                        select: {
                            members: true,
                            invites: true
                        }
                    }
                },
                skip: offset,
                take: limit,
                orderBy: { createdAt: "desc" }
            }),
            prisma.organization.count({ where })
        ]);
        return c.json({
            success: true,
            data: organizations,
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit)
            }
        });
    }
    catch (error) {
        console.error("Error listing organizations:", error);
        return c.json({ error: "Failed to list organizations" }, 500);
    }
});
// Add member to organization - for superadmin to manually add users to organizations
app.post("/add-member", async (c) => {
    try {
        const session = await auth.api.getSession({ headers: c.req.header() });
        if (!session?.user) {
            return c.json({ error: "Unauthorized" }, 401);
        }
        const body = await c.req.json();
        const { userId, organizationId, role = "admin" } = body;
        if (!userId || !organizationId) {
            return c.json({ error: "User ID and Organization ID are required" }, 400);
        }
        // Check if user is superadmin
        const userRole = session.user.role;
        if (userRole !== "superadmin") {
            return c.json({ error: "Only superadmin can use this endpoint" }, 403);
        }
        // Check if user exists
        const user = await prisma.user.findUnique({
            where: { id: userId }
        });
        if (!user) {
            return c.json({ error: "User not found" }, 404);
        }
        // Check if organization exists
        const organization = await prisma.organization.findUnique({
            where: { id: organizationId }
        });
        if (!organization) {
            return c.json({ error: "Organization not found" }, 404);
        }
        // Check if user is already a member
        const existingMember = await prisma.member.findFirst({
            where: {
                userId,
                organizationId
            }
        });
        if (existingMember) {
            return c.json({ error: "User is already a member of this organization" }, 400);
        }
        // Add user as member
        const newMember = await prisma.member.create({
            data: {
                id: randomUUID(),
                userId,
                organizationId,
                role
            },
            include: {
                user: {
                    select: {
                        id: true,
                        name: true,
                        email: true
                    }
                },
                organization: {
                    select: {
                        id: true,
                        name: true,
                        slug: true
                    }
                }
            }
        });
        return c.json({ success: true, data: newMember });
    }
    catch (error) {
        console.error("Error adding member to organization:", error);
        return c.json({ error: "Failed to add member to organization" }, 500);
    }
});
export default app;
