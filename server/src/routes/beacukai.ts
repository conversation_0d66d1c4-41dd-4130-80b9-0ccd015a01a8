import { Hono } from 'hono';
import { PrismaClient } from '@prisma/client';
import { BeacukaiApiService } from '../services/BeacukaiApiService';
import { BeacukaiSessionService } from '../services/BeacukaiSessionService';
import { randomUUID } from 'crypto';
import { auth } from '../auth';

const prisma = new PrismaClient();
const beacukaiService = new BeacukaiApiService(prisma);
const sessionService = new BeacukaiSessionService(prisma);

export const beacukaiRoutes = new Hono()

// Basic authentication endpoint
.post('/auth', async (c) => {
  try {
    const body = await c.req.json();
    // Simple auth response for now
    return c.json({ success: true, message: 'Authentication endpoint available' });
  } catch (error) {
    console.error('❌ Auth error:', error);
    return c.json({ error: 'Authentication failed' }, 500);
  }
})

// Get logs endpoint
.get('/logs', async (c) => {
  try {
    const logs = await prisma.requestLog.findMany({
      orderBy: { createdAt: 'desc' },
      take: 50
    });
    return c.json(logs);
  } catch (error) {
    console.error('❌ Error fetching logs:', error);
    return c.json({ error: 'Failed to fetch logs' }, 500);
  }
})

// ===== BEACUKAI API ENDPOINTS (Sesuai Dokumentasi) =====

// 1. E-Seal Add - Pengiriman data e-seal dari sistem vendor e-seal ke sistem eMS
.post('/eseal/add', async (c) => {
  try {
    const body = await c.req.json();
    console.log('🔄 Adding E-Seal to Beacukai:', body);

    const result = await beacukaiService.addESeal({
      idVendor: body.idVendor,
      merk: body.merk || '',
      model: body.model || '',
      noImei: body.noImei,
      tipe: body.tipe || ''
    });

    return c.json({
      success: result.status === 'success',
      data: result,
      message: result.message
    });
  } catch (error) {
    console.error('❌ Error adding E-Seal:', error);
    return c.json({ 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    }, 500);
  }
})

// 2. Get Dokumen Pabean - Permintaan data dokumen kepabeanan
.get('/eseal/get-dok-pabean', async (c) => {
  try {
    const nomorAju = c.req.query('nomor_aju');
    
    if (!nomorAju) {
      return c.json({ 
        success: false, 
        error: 'Parameter nomor_aju is required' 
      }, 400);
    }

    console.log('📋 Getting dokumen pabean for AJU:', nomorAju);
    const result = await beacukaiService.getDokumenPabean(nomorAju);

    return c.json({
      success: result.status === 'success',
      data: result,
      message: result.message
    });
  } catch (error) {
    console.error('❌ Error getting dokumen pabean:', error);
    return c.json({ 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    }, 500);
  }
})

// 3. Tracking Start - Pengiriman data tracking dari sistem vendor e-seal ke sistem eMS
.post('/tracking/start', async (c) => {
  try {
    const body = await c.req.json();
    console.log('🚀 Starting tracking:', body.noEseal);

    const result = await beacukaiService.startTracking({
      alamatAsal: body.alamatAsal,
      alamatTujuan: body.alamatTujuan,
      idVendor: body.idVendor,
      jnsKontainer: body.jnsKontainer,
      latitudeAsal: body.latitudeAsal,
      latitudeTujuan: body.latitudeTujuan,
      lokasiAsal: body.lokasiAsal,
      lokasiTujuan: body.lokasiTujuan,
      longitudeAsal: body.longitudeAsal,
      longitudeTujuan: body.longitudeTujuan,
      noImei: body.noImei,
      noEseal: body.noEseal,
      noKontainer: body.noKontainer,
      noPolisi: body.noPolisi,
      ukKontainer: body.ukKontainer,
      namaDriver: body.namaDriver,
      nomorTeleponDriver: body.nomorTeleponDriver,
      dokumen: body.dokumen || []
    });

    return c.json({
      success: result.status === 'success',
      data: result,
      message: result.message
    });
  } catch (error) {
    console.error('❌ Error starting tracking:', error);
    return c.json({ 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    }, 500);
  }
})

// 4. Update Position - Pengiriman data posisi e-seal secara periodik
.post('/eseal/update-position', async (c) => {
  try {
    const body = await c.req.json();
    console.log('📍 Updating E-Seal position:', body.noEseal);

    const result = await beacukaiService.updatePosition({
      address: body.address || '',
      altitude: body.altitude || '',
      battery: body.battery || '',
      dayaAki: body.dayaAki || body.battery || '',
      event: body.event || '0',
      idVendor: body.idVendor,
      kota: body.kota || '',
      latitude: body.latitude,
      longitude: body.longitude,
      noImei: body.noImei,
      noEseal: body.noEseal,
      provinsi: body.provinsi || '',
      speed: body.speed || '0'
    });

    return c.json({
      success: result.status === 'success',
      data: result,
      message: result.message
    });
  } catch (error) {
    console.error('❌ Error updating position:', error);
    return c.json({ 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    }, 500);
  }
})

// 5. Tracking Stop - Pengiriman data tracking stop
.post('/tracking/stop', async (c) => {
  try {
    const body = await c.req.json();
    console.log('🛑 Stopping tracking:', body.noEseal);

    const result = await beacukaiService.stopTracking({
      alamatStop: body.alamatStop,
      idVendor: body.idVendor,
      latitudeStop: body.latitudeStop,
      longitudeStop: body.longitudeStop,
      noImei: body.noImei,
      noEseal: body.noEseal
    });

    return c.json({
      success: result.status === 'success',
      data: result,
      message: result.message
    });
  } catch (error) {
    console.error('❌ Error stopping tracking:', error);
    return c.json({ 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    }, 500);
  }
})

// 6. Update Status Device - Pengiriman data perubahan status e-seal
.post('/eseal/update-status-device', async (c) => {
  try {
    const body = await c.req.json();
    console.log('🔄 Updating device status:', body.noImei);

    const result = await beacukaiService.updateStatusDevice({
      idVendor: body.idVendor,
      noImei: body.noImei,
      status: body.status
    });

    return c.json({
      success: result.status === 'success',
      data: result,
      message: result.message
    });
  } catch (error) {
    console.error('❌ Error updating device status:', error);
    return c.json({ 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    }, 500);
  }
})

// 7. Tracking Status - Permintaan data status tracking
.get('/tracking/status', async (c) => {
  try {
    const idVendor = c.req.query('idVendor');
    const noEseal = c.req.query('noEseal');
    
    if (!idVendor || !noEseal) {
      return c.json({ 
        success: false, 
        error: 'Parameters idVendor and noEseal are required' 
      }, 400);
    }

    console.log('📊 Getting tracking status:', noEseal);
    const result = await beacukaiService.getTrackingStatus(idVendor, noEseal);

    return c.json({
      success: result.status === 'success',
      data: result,
      message: result.message
    });
  } catch (error) {
    console.error('❌ Error getting tracking status:', error);
    return c.json({ 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    }, 500);
  }
})

// ===== ADDITIONAL ENDPOINTS =====

// Dokumen Pabean endpoint (untuk frontend validation)
.get('/dokumen/get-dok-pabean', async (c) => {
  try {
    const nomorAju = c.req.query('nomor_aju');
    
    if (!nomorAju) {
      return c.json({ 
        success: false, 
        error: 'Parameter nomor_aju is required' 
      }, 400);
    }

    console.log('📋 Frontend requesting dokumen pabean for AJU:', nomorAju);
    const result = await beacukaiService.getDokumenPabean(nomorAju);

    return c.json({
      success: result.status === 'success',
      data: result,
      message: result.message
    });
  } catch (error) {
    console.error('❌ Error getting dokumen pabean:', error);
    return c.json({ 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    }, 500);
  }
})

// Create E-Seal endpoint (untuk frontend)
.post('/eseal/create', async (c) => {
  try {
    const body = await c.req.json();
    console.log('🔄 Creating new E-Seal:', body);

    // 1. First add E-Seal to Beacukai
    const addResult = await beacukaiService.addESeal({
      idVendor: body.idVendor,
      merk: body.merk || '',
      model: body.model || '',
      noImei: body.noImei,
      tipe: body.tipeESeal || ''
    });

    if (addResult.status !== 'success') {
      return c.json({
        success: false,
        error: `Failed to add E-Seal to Beacukai: ${addResult.message}`
      }, 400);
    }

    // 2. Get current user ID from session
    const session = await auth.api.getSession({ headers: c.req.header() });
    if (!session?.user) {
      return c.json({
        success: false,
        error: 'User authentication required'
      }, 401);
    }
    const userId = session.user.id;

    // 3. Generate E-Seal number if not provided
    let noEseal = body.noEseal;
    if (!noEseal) {
      // Get the next sequence number for this IMEI
      const existingCount = await prisma.eSeal.count({
        where: {
          noImei: body.noImei
        }
      });
      const sequenceNumber = existingCount + 1;
      noEseal = `E-Seal ${body.noImei} - ${sequenceNumber}`;
    }

    // 4. Save E-Seal to local database after successful Beacukai API call
    const localESeal = await prisma.eSeal.create({
      data: {
        id: randomUUID(),
        noEseal: noEseal, // Use generated or provided noEseal
        noImei: body.noImei,
        merk: body.merk || '',
        model: body.model || '',
        tipe: body.tipeESeal || '',
        idVendor: body.idVendor,
        status: 'INACTIVE',
        
        // Location Info (required fields)
        alamatAsal: body.alamatAsal || 'TBD',
        alamatTujuan: body.alamatTujuan || 'TBD',
        lokasiAsal: body.lokasiAsal || 'TBD',
        lokasiTujuan: body.lokasiTujuan || 'TBD',
        latitudeAsal: body.latitudeAsal || '0',
        longitudeAsal: body.longitudeAsal || '0',
        latitudeTujuan: body.latitudeTujuan || '0',
        longitudeTujuan: body.longitudeTujuan || '0',
        
        // Vehicle & Driver Info (required fields)
        noPolisi: body.noPolisi || 'TBD',
        ukKontainer: body.ukKontainer || '20',
        jnsKontainer: body.jnsKontainer || '1',
        noKontainer: body.noKontainer || 'TBD',
        namaDriver: body.namaDriver || 'TBD',
        nomorTeleponDriver: body.nomorTeleponDriver || 'TBD',
        
        // AJU & Document Info
        nomorAJU: body.nomorAJU || null,
        
        // GPS Integration
        gpsDeviceId: body.noImei, // Use IMEI as GPS device ID
        
        // Beacukai Sync Status
        isSync: true, // Mark as synced since we successfully sent to Beacukai
        beacukaiId: addResult.item?.idEseal || null, // Store Beacukai's idEseal
        idEseal: addResult.item?.idEseal || null, // Store in idEseal field as well
        beacukaiResponseLog: addResult as any, // Store the full response for reference
        
        // User relation - use authenticated user ID
        userId: userId,

        // Organization relation
        organizationId: body.organizationId,
        
        // Timestamps
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });

    console.log('✅ E-Seal saved to local database:', localESeal.id);

    // 2. Then start tracking if location data is provided
    if (body.alamatAsal && body.alamatTujuan) {
      const trackingResult = await beacukaiService.startTracking({
        alamatAsal: body.alamatAsal,
        alamatTujuan: body.alamatTujuan,
        idVendor: body.idVendor,
        jnsKontainer: body.jnsKontainer || '1',
        latitudeAsal: body.latitudeAsal,
        latitudeTujuan: body.latitudeTujuan,
        lokasiAsal: body.lokasiAsal,
        lokasiTujuan: body.lokasiTujuan,
        longitudeAsal: body.longitudeAsal,
        longitudeTujuan: body.longitudeTujuan,
        noImei: body.noImei,
        noEseal: body.noEseal,
        noKontainer: body.noKontainer || '',
        noPolisi: body.noPolisi || '',
        ukKontainer: body.ukKontainer || '20',
        namaDriver: body.namaDriver || '',
        nomorTeleponDriver: body.nomorTeleponDriver || '',
        dokumen: body.dokumen || [{
          jenisMuat: 'FCL',
          jumlahKontainer: '1',
          kodeDokumen: 'PLP',
          kodeKantor: '050100',
          nomorAju: body.nomorAJU || '',
          nomorDokumen: '',
          tanggalDokumen: new Date().toISOString().split('T')[0]
        }]
      });

      return c.json({
        success: true,
        data: {
          ...addResult,
          local: localESeal.id,
          trackingStart: trackingResult
        },
        message: 'E-Seal created and tracking started successfully'
      });
    }

    return c.json({
      success: true,
      data: {
        ...addResult,
        local: localESeal.id,
      },
      message: 'E-Seal created successfully'
    });
  } catch (error) {
    console.error('❌ Error creating E-Seal:', error);
    return c.json({ 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    }, 500);
  }
});
