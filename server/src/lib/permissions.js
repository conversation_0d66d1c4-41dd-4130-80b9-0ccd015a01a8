import { createAccessControl } from "better-auth/plugins/access";
import { defaultStatements, adminAc, ownerAc, memberAc } from "better-auth/plugins/organization/access";
const statement = {
    ...defaultStatements,
    user: ["create", "list", "update", "set-role", "ban", "impersonate", "delete", "set-password"],
    session: ["list", "revoke", "delete"],
};
export const ac = createAccessControl(statement);
export const superadmin = ac.newRole({
    ...adminAc.statements,
    ...ownerAc.statements,
    user: ["create", "list", "update", "set-role", "ban", "impersonate", "delete", "set-password"],
    session: ["list", "revoke", "delete"],
});
export const admin = ac.newRole({
    ...adminAc.statements,
    user: ["list"],
});
export const member = ac.newRole({
    ...memberAc.statements,
});
export const roles = {
    superadmin,
    admin,
    member,
};
