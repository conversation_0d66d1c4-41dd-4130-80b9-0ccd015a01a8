import { Hono } from "hono";
import { cors } from "hono/cors";
import { serveStatic } from "hono/bun";
import { auth } from "./auth";
import { beacukaiRoutes } from "./routes/beacukai";
import { esealRoutes } from "./routes/eseal";
import { adminRoutes } from "./routes/admin";
import { role as roleRoutes } from "./routes/role";
import organizationRoutes from "./routes/organization";
import { startAllCronJobs } from "./jobs/positionUpdateJob";
// Mock data untuk E-Seal
const mockESealData = [
    {
        id: 1,
        perusahaan: "Perusahaan A",
        idVendor: "884GKEL637",
        merk: "BRAND A",
        model: "Model 2020",
        nomorIMEI: "875298572967967922",
        tipeESeal: "TIPE 123",
        token: "12356788",
        jarakTempuh: ">50 - 250 Km",
        status: "Unlocked"
    },
    {
        id: 2,
        perusahaan: "Perusahaan A",
        idVendor: "584GKEL637",
        merk: "BRAND B",
        model: "Model 2021",
        nomorIMEI: "252598572967967123",
        tipeESeal: "TIPE 456",
        token: "62466788",
        jarakTempuh: "<5 Km",
        status: "Locked"
    },
    {
        id: 3,
        perusahaan: "Perusahaan B",
        idVendor: "123GKEL789",
        merk: "BRAND C",
        model: "Model 2022",
        nomorIMEI: "987654321098765432",
        tipeESeal: "TIPE 789",
        token: "98765432",
        jarakTempuh: "250 - 500 Km",
        status: "Unlocked"
    },
    {
        id: 4,
        perusahaan: "Perusahaan C",
        idVendor: "456GKEL012",
        merk: "BRAND D",
        model: "Model 2023",
        nomorIMEI: "111222333444555666",
        tipeESeal: "TIPE 012",
        token: "11223344",
        jarakTempuh: ">500 Km",
        status: "Locked"
    },
    {
        id: 5,
        perusahaan: "Perusahaan D",
        idVendor: "789GKEL345",
        merk: "BRAND E",
        model: "Model 2024",
        nomorIMEI: "777888999000111222",
        tipeESeal: "TIPE 345",
        token: "77889900",
        jarakTempuh: "10 - 50 Km",
        status: "Unlocked"
    }
];
export const app = new Hono()
    // CORS is optional for single origin deployment
    // Keep for development flexibility, remove for production if desired
    .use((c, next) => {
    if (process.env.NODE_ENV !== "production") {
        return cors()(c, next);
    }
    return next();
})
    // Better Auth routes - handle all auth endpoints
    .all("/api/auth/*", async (c) => {
    console.log('🔐 Auth request:', c.req.method, c.req.url);
    const response = await auth.handler(c.req.raw);
    return new Response(response.body, {
        status: response.status,
        headers: response.headers,
    });
})
    // Beacukai API routes
    .route("/api/beacukai", beacukaiRoutes)
    // E-Seal CRUD routes
    .route("/api/eseal", esealRoutes)
    // Admin routes
    .route("/api/admin", adminRoutes)
    // Organization routes
    .route("/api/organization", organizationRoutes)
    // Role routes
    .route("/api/role", roleRoutes)
    // Test endpoint to create a user
    .post("/api/create-test-user", async (c) => {
    try {
        const result = await auth.api.signUpEmail({
            body: {
                email: "<EMAIL>",
                password: "password123",
                name: "Admin User"
            }
        });
        return c.json({ success: true, message: "Test user created", data: result });
    }
    catch (error) {
        return c.json({ success: false, error: error instanceof Error ? error.message : 'Unknown error' }, 400);
    }
})
    // Your existing API routes - keep the /api prefix for clarity
    .get("/api", (c) => {
    return c.text("Hello E-Seal Monitor API!");
})
    .get("/api/hello", async (c) => {
    const data = {
        message: "Hello E-Seal Monitor!",
        success: true,
    };
    return c.json(data, { status: 200 });
})
    // Serve static files for everything else
    .use("*", serveStatic({ root: "./static" }))
    // SPA fallback - serve index.html for all non-API routes
    .get("*", async (c, next) => {
    return serveStatic({ root: "./static", path: "index.html" })(c, next);
});
const port = parseInt(process.env.PORT || "3000");
// Initialize cron jobs
if (process.env.NODE_ENV !== "test") {
    console.log('🚀 Initializing cron jobs...');
    startAllCronJobs();
}
// Graceful shutdown handling
let isShuttingDown = false;
const gracefulShutdown = async (signal) => {
    if (isShuttingDown) {
        console.log('🛑 Shutdown already in progress...');
        return;
    }
    isShuttingDown = true;
    console.log(`🛑 Received ${signal}. Shutting down gracefully...`);
    try {
        // Import prisma here to avoid circular dependencies
        const { PrismaClient } = await import('@prisma/client');
        const prisma = new PrismaClient();
        await prisma.$disconnect();
        console.log('✅ Database connections closed');
    }
    catch (error) {
        console.error('❌ Error during shutdown:', error);
    }
    process.exit(0);
};
process.on('SIGINT', () => gracefulShutdown('SIGINT'));
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
    console.error('💥 Uncaught Exception:', error);
    gracefulShutdown('uncaughtException');
});
process.on('unhandledRejection', (reason, promise) => {
    console.error('💥 Unhandled Rejection at:', promise, 'reason:', reason);
    gracefulShutdown('unhandledRejection');
});
export default {
    port,
    fetch: app.fetch,
};
console.log(`🔒 E-Seal Monitor server running on port ${port}`);
