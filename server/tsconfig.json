{
  "extends": "../tsconfig.json",
  "compilerOptions": {
    // Environment settings
    "lib": ["ESNext"],
    "target": "ESNext",
    "module": "ESNext",
    "jsx": "react-jsx",
    "jsxImportSource": "hono/jsx",

    // Types
    "types": ["bun-types"],

    // Output settings - flat structure
    "declaration": true,
    "outDir": "dist",
    "noEmit": false,
    "emitDecoratorMetadata": true,
    "skipLibCheck": true,

    // Module resolution
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": false,

    // Path resolution
    "baseUrl": "../",
    "paths": {
      "shared": ["./shared/src"],
      "shared/*": ["./shared/src/*"]
    }
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist"]
}
