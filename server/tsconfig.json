{
  "extends": "../tsconfig.json",
  "compilerOptions": {
    // Environment settings
    "lib": ["ESNext"],
    "target": "ESNext",
    "module": "ESNext",
    "jsx": "react-jsx",
    "jsxImportSource": "hono/jsx",

    // Types
    "types": ["bun-types"],

    // Output settings - flat structure
    "declaration": false,  // Disable .d.ts generation for dev
    "outDir": "dist",
    "noEmit": true,  // Don't emit files in dev mode
    "emitDecoratorMetadata": true,
    "skipLibCheck": true,

    // Module resolution
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": false,

    // Path resolution
    "baseUrl": "../",
    "paths": {
      "shared": ["./shared/src"],
      "shared/*": ["./shared/src/*"]
    }
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist"]
}
