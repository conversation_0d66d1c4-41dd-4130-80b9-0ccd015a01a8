# E-Seal Monitor Integration Guide

## Overview

This document describes the complete integration of the E-Seal monitoring system with GPS tracking and Beacukai API according to the specifications in the documentation files.

## System Architecture

The system consists of:

1. **GPS Service Integration** - Connects to GPS API (smartelock.net:8088) to fetch real-time device locations
2. **Beacukai API Integration** - Complete 7-step API flow for customs compliance
3. **Automated Position Updates** - Cron job that runs every 5 minutes to sync GPS data with Beacukai
4. **Device Registration System** - Manages E-Seal device registration and status
5. **Tracking Management** - Handles tracking session start/stop operations
6. **Real-time Dashboard** - Client interface for monitoring and control

## API Integration Flow (Sesuai Dokumentasi Beacukai)

Berdasarkan dokumentasi resmi Beacukai dan arahan mentor, flow integrasi adalah:

### 1. Vendor e-seal mengirimkan data e-seal
- **Endpoint**: `eseal/add`
- **Purpose**: Register e-Seal device ke sistem eMS Beacukai
- **When**: Sekali per device sebelum tracking dimulai
- **Implementation**: `DeviceRegistrationService.registerDevice()`
- **Data**: idVendor, merk, model, noImei, tipe

### 2. Vendor e-seal mengirimkan request data dokumen kepabeanan
- **Endpoint**: `eseal/get-dok-pabean`
- **Purpose**: Mendapatkan data dokumen kepabeanan berdasarkan parameter tertentu
- **When**: Saat registrasi device jika ada nomor AJU
- **Implementation**: `BeacukaiApiService.getDokumenPabean()`
- **Parameter**: nomorAju

### 3. Vendor e-seal mengirimkan data tracking e-seal
- **Endpoint**: `tracking/start`
- **Purpose**: Mengirim data tracking e-seal (SEKALI SAJA saat mulai perjalanan)
- **When**: Saat container mulai perjalanan
- **Implementation**: `TrackingManagementService.startTracking()`
- **Data**: alamat asal/tujuan, koordinat, info kontainer, driver, dokumen

### 4. Vendor e-seal mengirimkan lokasi dan status e-seal secara periodik
- **Endpoint**: `eseal/update-position`
- **Purpose**: Mengirim koordinat GPS dan status device secara berkala
- **When**: Setiap 5 menit via cron job (sesuai arahan mentor)
- **Implementation**: `CronJobService.runPositionUpdateJob()`
- **Flow**: Ambil data dari GPS API → Simpan ke database → Hit API Beacukai

### Flow Tambahan (Opsional):

### 5. Update Device Status
- **Endpoint**: `eseal/update-status-device`
- **Purpose**: Update status device (tampering, battery, dll)
- **When**: Saat ada perubahan status
- **Implementation**: `TrackingManagementService.updateDeviceStatus()`

### 6. Stop Tracking
- **Endpoint**: `tracking/stop`
- **Purpose**: Menghentikan tracking saat sampai tujuan
- **When**: Saat perjalanan berakhir
- **Implementation**: `TrackingManagementService.stopTracking()`

### 7. Check Tracking Status
- **Endpoint**: `tracking/status`
- **Purpose**: Query status tracking terkini
- **When**: On-demand untuk monitoring
- **Implementation**: `TrackingManagementService.getTrackingStatus()`

## Authentication & Environment Configuration

### Session-Based Authentication (Beacukai)
Sistem menggunakan session-based authentication dengan JWT token yang didapat dari:
```
GET https://apisdev-gw.beacukai.go.id/rest/pub/apigateway/jwt/getJsonWebToken?app_id=56c566b2-53ab-4335-8f13-efdbe144ba52
Authorization: Basic dmlydHVzRGV2OmFSNyNwTDlxWkAxbQ==
```

### Environment Variables
Add these variables to your `.env` file:

```env
# Beacukai API Configuration (Session-based)
BEACUKAI_BASE_URL=https://apisdev-gw.beacukai.go.id
BEACUKAI_USERNAME=virtusDev
BEACUKAI_PASSWORD=aR7#pL9qZ@1m
BEACUKAI_APP_ID=56c566b2-53ab-4335-8f13-efdbe144ba52

# GPS API Configuration
GPS_API_BASE_URL=http://smartelock.net:8088
GPS_API_USERNAME=your-gps-username
GPS_API_PASSWORD=your-gps-password

# Cron Job Configuration
POSITION_UPDATE_CRON=*/5 * * * *
```

## Database Schema

The integration adds several new tables:

- **tracking_session** - Manages tracking sessions
- **device_status** - Current device status and GPS connectivity
- **cron_job_log** - Execution history of automated jobs
- Enhanced **tracking_log** with GPS data fields

## API Endpoints

### Device Management
- `POST /api/tracking/register-device` - Register new E-Seal device
- `GET /api/tracking/devices` - Get all registered devices
- `GET /api/tracking/device-status/:esealId` - Get device registration status
- `POST /api/tracking/test-gps/:esealId` - Test GPS connectivity

### Tracking Operations
- `POST /api/tracking/start` - Start tracking session
- `POST /api/tracking/stop` - Stop tracking session
- `GET /api/tracking/status/:esealId` - Get tracking status
- `GET /api/tracking/active` - Get all active tracking sessions
- `POST /api/tracking/update-status` - Update device status

### System Operations
- `POST /api/tracking/trigger-position-update` - Manual position update
- `GET /api/tracking/job-history` - Get cron job execution history

## Cron Job System

The system runs automated jobs:

### Position Update Job (Every 5 minutes)
1. Finds all active tracking sessions
2. Fetches GPS data for each device
3. Saves position data to database
4. Sends updates to Beacukai API
5. Updates device status
6. Logs execution results

### Cleanup Job (Daily at midnight)
- Removes old job logs (keeps 30 days)
- Maintains database performance

## Error Handling & Reliability

### Retry Mechanisms
- **GPS API**: 3 retries with exponential backoff
- **Beacukai API**: 3 retries with exponential backoff
- **Timeout handling**: 10s for GPS, 30s for Beacukai

### Error Recovery
- Failed API calls are logged with full context
- System continues processing other devices on individual failures
- Comprehensive error logging for debugging

### Monitoring
- Real-time dashboard shows device status
- Job execution history tracking
- GPS connectivity monitoring
- Beacukai registration status

## Usage Examples

### Register a New Device
```javascript
const deviceData = {
  noEseal: "ESEAL-001",
  noImei: "123456789012345",
  idVendor: "VENDOR-001",
  merk: "Brand A",
  model: "Model 2024",
  tipe: "Type 1",
  userId: "user-id",
  nomorAju: "16021600008120160415000002" // Optional
};

const result = await fetch('/api/tracking/register-device', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(deviceData)
});
```

### Start Tracking
```javascript
const trackingData = {
  esealId: "device-id",
  alamatAsal: "Jakarta Port",
  alamatTujuan: "Surabaya Port",
  latitudeAsal: "-6.1944",
  longitudeAsal: "106.8229",
  latitudeTujuan: "-7.2504",
  longitudeTujuan: "112.7688",
  // ... other required fields
};

const result = await fetch('/api/tracking/start', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(trackingData)
});
```

## Troubleshooting

### Common Issues

1. **GPS API Connection Failed**
   - Check GPS_API_USERNAME and GPS_API_PASSWORD
   - Verify network connectivity to smartelock.net:8088
   - Check device IMEI mapping

2. **Beacukai API Errors**
   - Verify JWT token is valid
   - Check API endpoint URLs in configuration
   - Review request payload format

3. **Cron Job Not Running**
   - Check server logs for initialization errors
   - Verify cron schedule format
   - Ensure database connectivity

### Monitoring Dashboard

Access the tracking dashboard at `/tracking-dashboard` to:
- View all registered devices
- Monitor GPS connectivity status
- Check Beacukai registration status
- View active tracking sessions
- Monitor cron job execution

## Security Considerations

- GPS API credentials stored securely in environment variables
- JWT tokens automatically refreshed
- API request/response logging for audit trail
- Timeout protection against hanging requests
- Input validation on all API endpoints

## Performance Optimization

- Batch processing of position updates
- Efficient database queries with proper indexing
- Connection pooling for external APIs
- Configurable cron job intervals
- Automatic cleanup of old logs
