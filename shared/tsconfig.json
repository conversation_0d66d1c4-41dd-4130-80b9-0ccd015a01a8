{
  "extends": "../tsconfig.json",
  "compilerOptions": {
    // Environment setup
    "lib": ["ESNext"],
    "target": "ESNext",
    "module": "ESNext",

    // Output configuration
    "declaration": false,  // Disable .d.ts generation for dev
    "outDir": "./dist",
    "rootDir": "./src",
    "noEmit": true,  // Don't emit files in dev mode

    // Type checking
    "strict": true,
    "skipLibCheck": true,

    // Additional checks
    "noFallthroughCasesInSwitch": true,
    "noUncheckedIndexedAccess": true
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "**/*.test.ts", "dist"]
}
