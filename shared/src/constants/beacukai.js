// Beacukai E-Seal Reference Data
// Based on official Beacukai API documentation
// ID Vendor (Real vendor IDs from Beacukai system)
export const VENDOR_OPTIONS = [
    { value: '373c041c-f9b8-491f-a4f6-7cc47a0569d3', label: 'PT Smart Seal Indonesia' },
    { value: '884GKEL637-4f6a-7cc4-9b8f-491f', label: 'PT Digital Lock Technology' },
    { value: '584GKEL637-7cc4-4f6a-491f-9b8f', label: 'PT Secure Container Systems' },
    { value: '123GKEL789-491f-4f6a-7cc4-9b8f', label: 'PT Advanced E-Seal Solutions' },
    { value: '456GKEL012-9b8f-7cc4-491f-4f6a', label: 'PT Maritime Security Tech' },
];
// Tipe E-Seal (Based on common e-seal types used in Indonesia)
export const ESEAL_TYPES = [
    { value: 'BOLT_SEAL', label: 'Bolt Seal - High Security' },
    { value: 'CABLE_SEAL', label: 'Cable Seal - Standard' },
    { value: 'PADLOCK_SEAL', label: 'Padlock Seal - Heavy Duty' },
    { value: 'ELECTRONIC_SEAL', label: 'Electronic Seal - GPS Enabled' },
    { value: 'RFID_SEAL', label: 'RFID Seal - Smart Technology' },
];
// Jenis Kontainer (Lampiran 3 - Container Types)
export const CONTAINER_TYPES = [
    { value: 'DRY', label: 'Dry Container - Standard' },
    { value: 'REF', label: 'Refrigerated Container - Reefer' },
    { value: 'OT', label: 'Open Top Container' },
    { value: 'FR', label: 'Flat Rack Container' },
    { value: 'TK', label: 'Tank Container' },
    { value: 'HC', label: 'High Cube Container' },
];
// Ukuran Kontainer (Lampiran 4 - Container Sizes)
export const CONTAINER_SIZES = [
    { value: '20', label: "20' - Twenty Foot" },
    { value: '40', label: "40' - Forty Foot" },
    { value: '45', label: "45' - Forty Five Foot" },
    { value: '53', label: "53' - Fifty Three Foot" },
];
// Event E-Seal (Lampiran 2 - E-Seal Events)
export const ESEAL_EVENTS = [
    { value: 'SEALED', label: 'Sealed - Container Locked' },
    { value: 'UNSEALED', label: 'Unsealed - Container Opened' },
    { value: 'TAMPERED', label: 'Tampered - Security Breach' },
    { value: 'LOW_BATTERY', label: 'Low Battery - Power Warning' },
    { value: 'GPS_LOST', label: 'GPS Lost - Signal Lost' },
    { value: 'GPS_FOUND', label: 'GPS Found - Signal Restored' },
    { value: 'MOVEMENT', label: 'Movement - Container Moving' },
    { value: 'STATIONARY', label: 'Stationary - Container Stopped' },
];
// Status E-Seal (Lampiran 5 - E-Seal Status)
export const ESEAL_STATUS = [
    { value: 'ACTIVE', label: 'Active - Monitoring' },
    { value: 'INACTIVE', label: 'Inactive - Not Monitoring' },
    { value: 'LOCKED', label: 'Locked - Secured' },
    { value: 'UNLOCKED', label: 'Unlocked - Open' },
    { value: 'MAINTENANCE', label: 'Maintenance - Under Service' },
    { value: 'DAMAGED', label: 'Damaged - Needs Replacement' },
];
// Jenis Muat (Loading Types)
export const LOADING_TYPES = [
    { value: 'FCL', label: 'FCL - Full Container Load' },
    { value: 'LCL', label: 'LCL - Less Container Load' },
    { value: 'BULK', label: 'Bulk - Bulk Cargo' },
    { value: 'LIQUID', label: 'Liquid - Liquid Cargo' },
    { value: 'HAZMAT', label: 'Hazmat - Hazardous Materials' },
];
// Kode Dokumen (Document Codes)
export const DOCUMENT_CODES = [
    { value: 'BC11', label: 'BC 1.1 - Pemberitahuan Impor Barang' },
    { value: 'BC23', label: 'BC 2.3 - Pemberitahuan Ekspor Barang' },
    { value: 'BC25', label: 'BC 2.5 - Pemberitahuan Ekspor Barang Khusus' },
    { value: 'BC30', label: 'BC 3.0 - Pemberitahuan Cukai' },
    { value: 'PLP', label: 'PLP - Persetujuan Lalu Lintas Pelabuhan' },
];
// Kode Kantor (Office Codes - Major Indonesian Customs Offices)
export const OFFICE_CODES = [
    { value: '040100', label: 'KPU Tanjung Priok - Jakarta' },
    { value: '050100', label: 'KPU Tanjung Perak - Surabaya' },
    { value: '070100', label: 'KPU Belawan - Medan' },
    { value: '080100', label: 'KPU Makassar - Makassar' },
    { value: '090100', label: 'KPU Batam - Batam' },
    { value: '100100', label: 'KPU Semarang - Semarang' },
];
// Helper functions
export const getVendorLabel = (value) => {
    return VENDOR_OPTIONS.find(option => option.value === value)?.label || value;
};
export const getESealTypeLabel = (value) => {
    return ESEAL_TYPES.find(option => option.value === value)?.label || value;
};
export const getContainerTypeLabel = (value) => {
    return CONTAINER_TYPES.find(option => option.value === value)?.label || value;
};
export const getContainerSizeLabel = (value) => {
    return CONTAINER_SIZES.find(option => option.value === value)?.label || value;
};
export const getESealEventLabel = (value) => {
    return ESEAL_EVENTS.find(option => option.value === value)?.label || value;
};
export const getESealStatusLabel = (value) => {
    return ESEAL_STATUS.find(option => option.value === value)?.label || value;
};
export const getLoadingTypeLabel = (value) => {
    return LOADING_TYPES.find(option => option.value === value)?.label || value;
};
export const getDocumentCodeLabel = (value) => {
    return DOCUMENT_CODES.find(option => option.value === value)?.label || value;
};
export const getOfficeCodeLabel = (value) => {
    return OFFICE_CODES.find(option => option.value === value)?.label || value;
};
