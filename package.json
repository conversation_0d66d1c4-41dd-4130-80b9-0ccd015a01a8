{"name": ".", "version": "0.3.1", "description": "A monorepo template built with Bun, Hono, Vite, and React", "author": "<PERSON>", "license": "MIT", "homepage": "https://github.com/steved<PERSON>ev/bhvr", "workspaces": ["./server", "./client", "./shared"], "scripts": {"dev:client": "cd client && bun run dev", "dev:server": "cd server && bun run dev", "dev:shared": "cd shared && bun run dev", "dev": "concurrently \"bun run dev:shared\" \"bun run dev:server\" \"bun run dev:client\"", "build:client": "cd client && ../node_modules/.bin/tsc -b && vite build", "build:shared": "cd shared && bun run build", "build:server": "cd server && bun run build", "build": "bun run build:shared && bun run build:client", "build:single": "bun run clean && bun run build && bun run copy:static && bun run build:server", "copy:static": "rm -rf server/static && cp -r client/dist server/static", "start:single": "cd server && bun run dist/index.js", "postinstall": "bun run build:shared && bun run build:server", "clean": "find . -type f -name '*.js' -not -path './node_modules/*' -delete", "clean:all": "find . -type f \\( -name '*.js' -o -name '*.d.ts' \\) -not -path './node_modules/*' -not -name 'images.d.ts' -delete && rm -rf */dist", "prebuild": "bun run clean"}, "keywords": ["bun", "hono", "react", "vite", "monorepo"], "devDependencies": {"bun-types": "latest", "concurrently": "^9.1.2"}, "peerDependencies": {"typescript": "^5.7.3"}, "dependencies": {"hono": "^4.8.5"}}