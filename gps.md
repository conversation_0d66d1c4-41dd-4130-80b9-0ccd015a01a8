# 2.X


**Description**:2.X


**HOST**:smartelock.net:8088


**Contacts**:


**Version**:1.0


**URL**:/v2/api-docs?group=2.X


[TOC]






# User Management


## api_forgot_password


**url**:`/user/forgot_password`


**method**:`POST`


**produces**:`application/json`


**consumes**:`*/*`


**Note**:


**Example**:


```javascript
{
  "code": "",
  "email": "",
  "username": ""
}
```


**Params**:


| name | description | in    | require | type | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|emsForgotPasswordDto|emsForgotPasswordDto|body|true|EmsForgotPasswordDto|EmsForgotPasswordDto|
|&emsp;&emsp;code|||false|string||
|&emsp;&emsp;email|||false|string||
|&emsp;&emsp;username|||false|string||


**Status**:


| code | description | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResult|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**Response Params**:


| name | description | type | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int64)|integer(int64)|
|data||object||
|message||string||


**Response Example**:
```javascript
{
	"code": 0,
	"data": {},
	"message": ""
}
```


## api_reset_password


**url**:`/user/log_out`


**method**:`POST`


**produces**:`application/json`


**consumes**:`*/*`


**Note**:


**Params**:


暂无


**Status**:


| code | description | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResult|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**Response Params**:


| name | description | type | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int64)|integer(int64)|
|data||object||
|message||string||


**Response Example**:
```javascript
{
	"code": 0,
	"data": {},
	"message": ""
}
```


## log in


**url**:`/user/login`


**method**:`POST`


**produces**:`application/json`


**consumes**:`*/*`


**Note**:


**Example**:


```javascript
{
  "password": "",
  "username": ""
}
```


**Params**:


| name | description | in    | require | type | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|userLoginParam|userLoginParam|body|true|UmsUserLoginDto|UmsUserLoginDto|
|&emsp;&emsp;password|password||true|string||
|&emsp;&emsp;username|user name||true|string||


**Status**:


| code | description | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResult|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**Response Params**:


| name | description | type | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int64)|integer(int64)|
|data||object||
|message||string||


**Response Example**:
```javascript
{
	"code": 0,
	"data": {},
	"message": ""
}
```


## Save Aurora Push Id


**url**:`/user/post_jiguang_id`


**method**:`POST`


**produces**:`application/json`


**consumes**:`*/*`


**Note**:


**Example**:


```javascript
{
  "jgId": ""
}
```


**Params**:


| name | description | in    | require | type | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|jgDto|jgDto|body|true|UmsJgDto|UmsJgDto|
|&emsp;&emsp;jgId|||false|string||


**Status**:


| code | description | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResult|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**Response Params**:


| name | description | type | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int64)|integer(int64)|
|data||object||
|message||string||


**Response Example**:
```javascript
{
	"code": 0,
	"data": {},
	"message": ""
}
```


## api_reset_password


**url**:`/user/reset_password`


**method**:`POST`


**produces**:`application/json`


**consumes**:`*/*`


**Note**:


**Example**:


```javascript
{
  "code": "",
  "email": "",
  "username": ""
}
```


**Params**:


| name | description | in    | require | type | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|emsForgotPasswordDto|emsForgotPasswordDto|body|true|EmsForgotPasswordDto|EmsForgotPasswordDto|
|&emsp;&emsp;code|||false|string||
|&emsp;&emsp;email|||false|string||
|&emsp;&emsp;username|||false|string||


**Status**:


| code | description | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResult|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**Response Params**:


| name | description | type | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int64)|integer(int64)|
|data||object||
|message||string||


**Response Example**:
```javascript
{
	"code": 0,
	"data": {},
	"message": ""
}
```


## api_send_email


**url**:`/user/send_email`


**method**:`POST`


**produces**:`application/json`


**consumes**:`*/*`


**Note**:


**Example**:


```javascript
{
  "code": "",
  "email": "",
  "username": ""
}
```


**Params**:


| name | description | in    | require | type | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|emsForgotPasswordDto|emsForgotPasswordDto|body|true|EmsForgotPasswordDto|EmsForgotPasswordDto|
|&emsp;&emsp;code|||false|string||
|&emsp;&emsp;email|||false|string||
|&emsp;&emsp;username|||false|string||


**Status**:


| code | description | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResult|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**Response Params**:


| name | description | type | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int64)|integer(int64)|
|data||object||
|message||string||


**Response Example**:
```javascript
{
	"code": 0,
	"data": {},
	"message": ""
}
```


## Change password


**url**:`/user/update_password`


**method**:`POST`


**produces**:`application/json`


**consumes**:`*/*`


**Note**:


**Example**:


```javascript
{
  "confirmPassword": "",
  "newPassword": "",
  "oldPassword": ""
}
```


**Params**:


| name | description | in    | require | type | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|userPasswordDto|userPasswordDto|body|true|UmsUserPasswordDto|UmsUserPasswordDto|
|&emsp;&emsp;confirmPassword|Confirm Password||true|string||
|&emsp;&emsp;newPassword|Password||true|string||
|&emsp;&emsp;oldPassword|Old password||true|string||


**Status**:


| code | description | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResult|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**Response Params**:


| name | description | type | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int64)|integer(int64)|
|data||object||
|message||string||


**Response Example**:
```javascript
{
	"code": 0,
	"data": {},
	"message": ""
}
```


# equipment management


## Vehicle binding equipment


**url**:`/device/bind`


**method**:`POST`


**produces**:`application/json`


**consumes**:`*/*`


**Note**:


**Example**:


```javascript
{
  "deviceId": "",
  "vehicleId": 0
}
```


**Params**:


| name | description | in    | require | type | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|bindDeviceDto|bindDeviceDto|body|true|EmsBindDeviceDto|EmsBindDeviceDto|
|&emsp;&emsp;deviceId|device ID||false|string||
|&emsp;&emsp;vehicleId|vehicle Id||false|integer(int32)||


**Status**:


| code | description | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResult|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**Response Params**:


| name | description | type | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int64)|integer(int64)|
|data||object||
|message||string||


**Response Example**:
```javascript
{
	"code": 0,
	"data": {},
	"message": ""
}
```


## api_add_device


**url**:`/device/device_add`


**method**:`POST`


**produces**:`application/json`


**consumes**:`*/*`


**Note**:


**Example**:


```javascript
{
  "comment": "",
  "deviceId": "",
  "deviceType": "",
  "expireTime": 0,
  "imei": "",
  "simCode": "",
  "status": 0,
  "unlockPassword": ""
}
```


**Params**:


| name | description | in    | require | type | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|deviceTypeParamDto|deviceTypeParamDto|body|true|EmsDeviceTypeParamDto|EmsDeviceTypeParamDto|
|&emsp;&emsp;comment|||false|string||
|&emsp;&emsp;deviceId|||false|string||
|&emsp;&emsp;deviceType|||false|string||
|&emsp;&emsp;expireTime|||false|integer(int32)||
|&emsp;&emsp;imei|||false|string||
|&emsp;&emsp;simCode|||false|string||
|&emsp;&emsp;status|||false|integer(int32)||
|&emsp;&emsp;unlockPassword|||false|string||


**Status**:


| code | description | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResult|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**Response Params**:


| name | description | type | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int64)|integer(int64)|
|data||object||
|message||string||


**Response Example**:
```javascript
{
	"code": 0,
	"data": {},
	"message": ""
}
```


## api_get_device_types


**url**:`/device/device_types`


**method**:`POST`


**produces**:`application/json`


**consumes**:`*/*`


**Note**:


**Params**:


暂无


**Status**:


| code | description | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResult|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**Response Params**:


| name | description | type | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int64)|integer(int64)|
|data||object||
|message||string||


**Response Example**:
```javascript
{
	"code": 0,
	"data": {},
	"message": ""
}
```


## Obtain all devices of the user


**url**:`/device/list_all`


**method**:`POST`


**produces**:`application/json`


**consumes**:`*/*`


**Note**:


**Example**:


```javascript
{
  "timeOffset": 0
}
```


**Params**:


| name | description | in    | require | type | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|offsetTime|offsetTime|body|true|OffsetTime|OffsetTime|
|&emsp;&emsp;timeOffset|Standard time difference (seconds)||false|integer(int32)||


**Status**:


| code | description | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResult«List«EmsSimpleDeviceDto»»|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**Response Params**:


| name | description | type | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int64)|integer(int64)|
|data||array|EmsSimpleDeviceDto|
|&emsp;&emsp;acc||integer(int32)||
|&emsp;&emsp;backCapStatus||integer(int32)||
|&emsp;&emsp;devBatteryPCT||integer(int32)||
|&emsp;&emsp;devTemp||integer(int32)||
|&emsp;&emsp;devType||integer(int32)||
|&emsp;&emsp;devTypeName||string||
|&emsp;&emsp;deviceId|device ID|string||
|&emsp;&emsp;direction||string||
|&emsp;&emsp;event||string||
|&emsp;&emsp;exBytes||string||
|&emsp;&emsp;gpsTime||string||
|&emsp;&emsp;groupName||string||
|&emsp;&emsp;gsmMode||string||
|&emsp;&emsp;lat||number(double)||
|&emsp;&emsp;lng||number(double)||
|&emsp;&emsp;locate||integer(int32)||
|&emsp;&emsp;lockStatus||integer(int32)||
|&emsp;&emsp;mileage||integer(int32)||
|&emsp;&emsp;motorLockStatus||integer(int32)||
|&emsp;&emsp;online||boolean||
|&emsp;&emsp;speed||integer(int32)||
|&emsp;&emsp;status||string||
|&emsp;&emsp;steelStringStatus||integer(int32)||
|&emsp;&emsp;todayIdlingSeconds||integer(int64)||
|&emsp;&emsp;todayMaxSpeed||integer(int32)||
|&emsp;&emsp;updateTime||string||
|&emsp;&emsp;vehicleId|Car ID|integer(int32)||
|&emsp;&emsp;vehicleName|Car name|string||
|message||string||


**Response Example**:
```javascript
{
	"code": 0,
	"data": [
		{
			"acc": 0,
			"backCapStatus": 0,
			"devBatteryPCT": 0,
			"devTemp": 0,
			"devType": 0,
			"devTypeName": "",
			"deviceId": "",
			"direction": "",
			"event": "",
			"exBytes": "",
			"gpsTime": "",
			"groupName": "",
			"gsmMode": "",
			"lat": 0,
			"lng": 0,
			"locate": 0,
			"lockStatus": 0,
			"mileage": 0,
			"motorLockStatus": 0,
			"online": true,
			"speed": 0,
			"status": "",
			"steelStringStatus": 0,
			"todayIdlingSeconds": 0,
			"todayMaxSpeed": 0,
			"updateTime": "",
			"vehicleId": 0,
			"vehicleName": ""
		}
	],
	"message": ""
}
```


## Get the latest location of the device


**url**:`/device/list_car_new_position`


**method**:`POST`


**produces**:`application/json`


**consumes**:`*/*`


**Note**:


**Example**:


```javascript
{
  "carId": [],
  "timeOffset": 0
}
```


**Params**:


| name | description | in    | require | type | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|newPointsParamDto|newPointsParamDto|body|true|EmsTrackNewPointsParamDto|EmsTrackNewPointsParamDto|
|&emsp;&emsp;carId|||false|array|integer(int32)|
|&emsp;&emsp;timeOffset|Standard time difference (seconds)||false|integer(int32)||


**Status**:


| code | description | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResult«List«EmsTrackDto»»|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**Response Params**:


| name | description | type | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int64)|integer(int64)|
|data||array|EmsTrackDto|
|&emsp;&emsp;acc||integer(int32)||
|&emsp;&emsp;carId||integer(int32)||
|&emsp;&emsp;devBatteryPCT||integer(int32)||
|&emsp;&emsp;devType||integer(int32)||
|&emsp;&emsp;deviceId||string||
|&emsp;&emsp;direction||string||
|&emsp;&emsp;event||string||
|&emsp;&emsp;exBytes||string||
|&emsp;&emsp;gpsCount||integer(int32)||
|&emsp;&emsp;gpsTime||string||
|&emsp;&emsp;gpsTimeSecond||integer(int64)||
|&emsp;&emsp;gsmMode||string||
|&emsp;&emsp;gsmSignal||integer(int32)||
|&emsp;&emsp;lat||number(double)||
|&emsp;&emsp;lng||number(double)||
|&emsp;&emsp;locate||integer(int32)||
|&emsp;&emsp;mileage||integer(int32)||
|&emsp;&emsp;speed||integer(int32)||
|&emsp;&emsp;status||string||
|&emsp;&emsp;todayMaxSpeed||integer(int32)||
|&emsp;&emsp;updateTime||string||
|message||string||


**Response Example**:
```javascript
{
	"code": 0,
	"data": [
		{
			"acc": 0,
			"carId": 0,
			"devBatteryPCT": 0,
			"devType": 0,
			"deviceId": "",
			"direction": "",
			"event": "",
			"exBytes": "",
			"gpsCount": 0,
			"gpsTime": "",
			"gpsTimeSecond": 0,
			"gsmMode": "",
			"gsmSignal": 0,
			"lat": 0,
			"lng": 0,
			"locate": 0,
			"mileage": 0,
			"speed": 0,
			"status": "",
			"todayMaxSpeed": 0,
			"updateTime": ""
		}
	],
	"message": ""
}
```


## Obtain device history trajectory


**url**:`/device/list_car_track`


**method**:`POST`


**produces**:`application/json`


**consumes**:`*/*`


**Note**:


**Example**:


```javascript
{
  "endTime": "",
  "startTime": "",
  "timeOffset": 0,
  "vehicleId": 0
}
```


**Params**:


| name | description | in    | require | type | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|emsCarTrack|emsCarTrack|body|true|EmsCarTrackDto|EmsCarTrackDto|
|&emsp;&emsp;endTime|End Time(yyyy-MM-dd HH:mm:ss)||false|string||
|&emsp;&emsp;startTime|Start time(yyyy-MM-dd HH:mm:ss)||false|string||
|&emsp;&emsp;timeOffset|Standard time difference (seconds)||false|integer(int32)||
|&emsp;&emsp;vehicleId|vehicle ID||true|integer(int32)||


**Status**:


| code | description | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResult|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**Response Params**:


| name | description | type | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int64)|integer(int64)|
|data||object||
|message||string||


**Response Example**:
```javascript
{
	"code": 0,
	"data": {},
	"message": ""
}
```


## Obtain device history trajectory


**url**:`/device/list_car_track_token`


**method**:`POST`


**produces**:`application/json`


**consumes**:`*/*`


**Note**:


**Example**:


```javascript
{
  "endTime": "",
  "startTime": "",
  "timeOffset": 0,
  "token": "",
  "vehicleId": 0
}
```


**Params**:


| name | description | in    | require | type | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|emsCarTrack|emsCarTrack|body|true|EmsCarTrackTokenDto|EmsCarTrackTokenDto|
|&emsp;&emsp;endTime|End Time(yyyy-MM-dd HH:mm:ss)||false|string||
|&emsp;&emsp;startTime|Start time(yyyy-MM-dd HH:mm:ss)||false|string||
|&emsp;&emsp;timeOffset|Standard time difference (seconds)||false|integer(int32)||
|&emsp;&emsp;token|||false|string||
|&emsp;&emsp;vehicleId|vehicle ID||true|integer(int32)||


**Status**:


| code | description | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResult|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**Response Params**:


| name | description | type | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int64)|integer(int64)|
|data||object||
|message||string||


**Response Example**:
```javascript
{
	"code": 0,
	"data": {},
	"message": ""
}
```


## Vehicle unbinding equipment


**url**:`/device/un_bind`


**method**:`POST`


**produces**:`application/json`


**consumes**:`*/*`


**Note**:


**Example**:


```javascript
{
  "deviceId": "",
  "vehicleId": 0
}
```


**Params**:


| name | description | in    | require | type | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|bindDeviceDto|bindDeviceDto|body|true|EmsBindDeviceDto|EmsBindDeviceDto|
|&emsp;&emsp;deviceId|device ID||false|string||
|&emsp;&emsp;vehicleId|vehicle Id||false|integer(int32)||


**Status**:


| code | description | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResult|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**Response Params**:


| name | description | type | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int64)|integer(int64)|
|data||object||
|message||string||


**Response Example**:
```javascript
{
	"code": 0,
	"data": {},
	"message": ""
}
```


## Obtain device status prompt


**url**:`/device/vehicle_status`


**method**:`POST`


**produces**:`application/json`


**consumes**:`*/*`


**Note**:


**Params**:


暂无


**Status**:


| code | description | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResult|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**Response Params**:


| name | description | type | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int64)|integer(int64)|
|data||object||
|message||string||


**Response Example**:
```javascript
{
	"code": 0,
	"data": {},
	"message": ""
}
```


## Obtain device status prompt icon


**url**:`/device/vehicle_status_icons`


**method**:`POST`


**produces**:`application/json`


**consumes**:`*/*`


**Note**:


**Params**:


暂无


**Status**:


| code | description | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResult|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**Response Params**:


| name | description | type | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int64)|integer(int64)|
|data||object||
|message||string||


**Response Example**:
```javascript
{
	"code": 0,
	"data": {},
	"message": ""
}
```


# Command Management


## Remote command operation


**url**:`/command/remote`


**method**:`POST`


**produces**:`application/json`


**consumes**:`*/*`


**Note**:


**Example**:


```javascript
{
  "commandType": 0,
  "deviceId": "",
  "second": 0,
  "speed": 0
}
```


**Params**:


| name | description | in    | require | type | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|commandDto|commandDto|body|true|EmsRemoteCommandDto|EmsRemoteCommandDto|
|&emsp;&emsp;commandType|Command type, 1: Remote unlocking; 2: Remote disconnection of oil and electricity; 3: Remote recovery of oil and electricity;4: set speed||true|integer(int32)||
|&emsp;&emsp;deviceId|device ID||true|string||
|&emsp;&emsp;second|||false|integer(int32)||
|&emsp;&emsp;speed|||false|integer(int32)||


**Status**:


| code | description | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResult«int»|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**Response Params**:


| name | description | type | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int64)|integer(int64)|
|data||integer(int32)|integer(int32)|
|message||string||


**Response Example**:
```javascript
{
	"code": 0,
	"data": 0,
	"message": ""
}
```


## Password unlocking


**url**:`/command/password_unlock`


**method**:`POST`


**produces**:`application/json`


**consumes**:`*/*`


**Note**:


**Example**:


```javascript
{
  "deviceId": "",
  "password": ""
}
```


**Params**:


| name | description | in    | require | type | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|passwordUnlockDto|passwordUnlockDto|body|true|EmsPasswordUnlockDto|EmsPasswordUnlockDto|
|&emsp;&emsp;deviceId|device ID||true|string||
|&emsp;&emsp;password|Lock code||true|string||


**Status**:


| code | description | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResult«int»|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**Response Params**:


| name | description | type | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int64)|integer(int64)|
|data||integer(int32)|integer(int32)|
|message||string||


**Response Example**:
```javascript
{
	"code": 0,
	"data": 0,
	"message": ""
}
```


## Remote operation&password unlocking result confirmation


**url**:`/command/remote_result`


**method**:`POST`


**produces**:`application/json`


**consumes**:`*/*`


**Note**:


**Example**:


```javascript
{
  "commandType": 0,
  "deviceId": "",
  "id": 0
}
```


**Params**:


| name | description | in    | require | type | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|remoteResultDto|remoteResultDto|body|true|EmsRemoteResultDto|EmsRemoteResultDto|
|&emsp;&emsp;commandType|Command type, 1: Remote unlocking; 2: Remote disconnection of oil and electricity; 3: Remote recovery of oil and electricity4: set speed||true|integer(int32)||
|&emsp;&emsp;deviceId|device ID||true|string||
|&emsp;&emsp;id|Instruction Id||true|integer(int32)||


**Status**:


| code | description | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResult«int»|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**Response Params**:


| name | description | type | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int64)|integer(int64)|
|data||integer(int32)|integer(int32)|
|message||string||


**Response Example**:
```javascript
{
	"code": 0,
	"data": 0,
	"message": ""
}
```


## Input instructions


**url**:`/command/send`


**method**:`POST`


**produces**:`application/json`


**consumes**:`*/*`


**Note**:


**Example**:


```javascript
{
  "command": "",
  "commandType": 0,
  "deviceIds": []
}
```


**Params**:


| name | description | in    | require | type | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|sendCommandDto|sendCommandDto|body|true|EmsSendCommandDto|EmsSendCommandDto|
|&emsp;&emsp;command|command||true|string||
|&emsp;&emsp;commandType|Instruction type, 1: Text type; 2: Hex type (hexadecimal type)||true|integer(int32)||
|&emsp;&emsp;deviceIds|Device Id Collection||true|array|string|


**Status**:


| code | description | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResult«EmsSendCommandResultDto»|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**Response Params**:


| name | description | type | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int64)|integer(int64)|
|data||EmsSendCommandResultDto|EmsSendCommandResultDto|
|&emsp;&emsp;deviceIds|Supported deviceIds|array|string|
|&emsp;&emsp;id|ID, which is used to obtain the response of the instruction|integer(int32)||
|message||string||


**Response Example**:
```javascript
{
	"code": 0,
	"data": {
		"deviceIds": [],
		"id": 0
	},
	"message": ""
}
```


## Obtain the latest instruction based on Id


**url**:`/command/send_result`


**method**:`POST`


**produces**:`application/json`


**consumes**:`*/*`


**Note**:


**Example**:


```javascript
{
  "deviceIds": [],
  "id": 0
}
```


**Params**:


| name | description | in    | require | type | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|sendCommandResultDto|sendCommandResultDto|body|true|EmsSendCommandResultDto|EmsSendCommandResultDto|
|&emsp;&emsp;deviceIds|Supported deviceIds||true|array|string|
|&emsp;&emsp;id|ID, which is used to obtain the response of the instruction||true|integer(int32)||


**Status**:


| code | description | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResult|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**Response Params**:


| name | description | type | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int64)|integer(int64)|
|data||object||
|message||string||


**Response Example**:
```javascript
{
	"code": 0,
	"data": {},
	"message": ""
}
```


## Get Historical Instructions


**url**:`/command/more`


**method**:`POST`


**produces**:`application/json`


**consumes**:`*/*`


**Note**:


**Params**:


| name | description | in    | require | type | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|deviceId|device ID|query|true|string||
|lastTime|The last time point, if left blank, defaults to 0, and only the latest pageSize data can be obtained|query|false|integer(int32)||
|pageSize|How many pieces of data per page, default to 10 pieces|query|false|integer(int32)||


**Status**:


| code | description | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResult|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**Response Params**:


| name | description | type | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int64)|integer(int64)|
|data||object||
|message||string||


**Response Example**:
```javascript
{
	"code": 0,
	"data": {},
	"message": ""
}
```


# Alarm management


## Obtain device alarms


**url**:`/alarm/last_alarm_message`


**method**:`POST`


**produces**:`application/json`


**consumes**:`*/*`


**Note**:


**Example**:


```javascript
{
  "alarmTypes": [],
  "autoIndex": 0
}
```


**Params**:


| name | description | in    | require | type | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|alarmMessageParamDto|alarmMessageParamDto|body|true|EmsAlarmMessageParamDto|EmsAlarmMessageParamDto|
|&emsp;&emsp;alarmTypes|||false|array|string|
|&emsp;&emsp;autoIndex|||false|integer(int64)||


**Status**:


| code | description | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResult|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**Response Params**:


| name | description | type | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int64)|integer(int64)|
|data||object||
|message||string||


**Response Example**:
```javascript
{
	"code": 0,
	"data": {},
	"message": ""
}
```


## Obtain alarm type


**url**:`/alarm/list_aram_type`


**method**:`POST`


**produces**:`application/json`


**consumes**:`*/*`


**Note**:


**Params**:


暂无


**Status**:


| code | description | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResult|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**Response Params**:


| name | description | type | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int64)|integer(int64)|
|data||object||
|message||string||


**Response Example**:
```javascript
{
	"code": 0,
	"data": {},
	"message": ""
}
```


## Obtain device alarms


**url**:`/alarm/list_car_alarm`


**method**:`POST`


**produces**:`application/json`


**consumes**:`*/*`


**Note**:


**Example**:


```javascript
{
  "carId": [],
  "endTime": "",
  "startTime": "",
  "timeOffset": 0
}
```


**Params**:


| name | description | in    | require | type | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|emsCarTrack|emsCarTrack|body|true|EmsTrackParamDto|EmsTrackParamDto|
|&emsp;&emsp;carId|||false|array|integer(int32)|
|&emsp;&emsp;endTime|End Time(yyyy-MM-dd HH:mm:ss)||false|string||
|&emsp;&emsp;startTime|Start time(yyyy-MM-dd HH:mm:ss)||false|string||
|&emsp;&emsp;timeOffset|Standard time difference (seconds)||false|integer(int32)||


**Status**:


| code | description | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResult|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**Response Params**:


| name | description | type | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int64)|integer(int64)|
|data||object||
|message||string||


**Response Example**:
```javascript
{
	"code": 0,
	"data": {},
	"message": ""
}
```


# api_geofence_management


## api_add_geofence


**url**:`/geofence/add`


**method**:`POST`


**produces**:`application/json`


**consumes**:`*/*`


**Note**:


**Example**:


```javascript
{
  "carId": 0,
  "color": 0,
  "distance": 0,
  "geofenceId": 0,
  "geofenceName": "",
  "geofenceType": 0,
  "latlngs": ""
}
```


**Params**:


| name | description | in    | require | type | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|geofenceDto|geofenceDto|body|true|EmsGeofenceDto|EmsGeofenceDto|
|&emsp;&emsp;carId|||false|integer(int32)||
|&emsp;&emsp;color|||false|integer(int32)||
|&emsp;&emsp;distance|||false|integer(int32)||
|&emsp;&emsp;geofenceId|||false|integer(int32)||
|&emsp;&emsp;geofenceName|||false|string||
|&emsp;&emsp;geofenceType|||false|integer(int32)||
|&emsp;&emsp;latlngs|||false|string||


**Status**:


| code | description | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResult«EmsGeofence对象»|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**Response Params**:


| name | description | type | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int64)|integer(int64)|
|data||EmsGeofence对象|EmsGeofence对象|
|&emsp;&emsp;clientId||integer(int32)||
|&emsp;&emsp;color||integer(int32)||
|&emsp;&emsp;distance||integer(int32)||
|&emsp;&emsp;geofenceId||integer(int32)||
|&emsp;&emsp;geofenceName||string||
|&emsp;&emsp;geofenceType||integer(int32)||
|&emsp;&emsp;path||string||
|&emsp;&emsp;test||string||
|&emsp;&emsp;userId||integer(int32)||
|message||string||


**Response Example**:
```javascript
{
	"code": 0,
	"data": {
		"clientId": 0,
		"color": 0,
		"distance": 0,
		"geofenceId": 0,
		"geofenceName": "",
		"geofenceType": 0,
		"path": "",
		"test": "",
		"userId": 0
	},
	"message": ""
}
```


## api_add_geofence


**url**:`/geofence/add_and_join_car`


**method**:`POST`


**produces**:`application/json`


**consumes**:`*/*`


**Note**:


**Example**:


```javascript
{
  "carId": 0,
  "color": 0,
  "distance": 0,
  "geofenceId": 0,
  "geofenceName": "",
  "geofenceType": 0,
  "latlngs": ""
}
```


**Params**:


| name | description | in    | require | type | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|geofenceDto|geofenceDto|body|true|EmsGeofenceDto|EmsGeofenceDto|
|&emsp;&emsp;carId|||false|integer(int32)||
|&emsp;&emsp;color|||false|integer(int32)||
|&emsp;&emsp;distance|||false|integer(int32)||
|&emsp;&emsp;geofenceId|||false|integer(int32)||
|&emsp;&emsp;geofenceName|||false|string||
|&emsp;&emsp;geofenceType|||false|integer(int32)||
|&emsp;&emsp;latlngs|||false|string||


**Status**:


| code | description | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResult«EmsGeofence对象»|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**Response Params**:


| name | description | type | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int64)|integer(int64)|
|data||EmsGeofence对象|EmsGeofence对象|
|&emsp;&emsp;clientId||integer(int32)||
|&emsp;&emsp;color||integer(int32)||
|&emsp;&emsp;distance||integer(int32)||
|&emsp;&emsp;geofenceId||integer(int32)||
|&emsp;&emsp;geofenceName||string||
|&emsp;&emsp;geofenceType||integer(int32)||
|&emsp;&emsp;path||string||
|&emsp;&emsp;test||string||
|&emsp;&emsp;userId||integer(int32)||
|message||string||


**Response Example**:
```javascript
{
	"code": 0,
	"data": {
		"clientId": 0,
		"color": 0,
		"distance": 0,
		"geofenceId": 0,
		"geofenceName": "",
		"geofenceType": 0,
		"path": "",
		"test": "",
		"userId": 0
	},
	"message": ""
}
```


## api_del_geofence


**url**:`/geofence/delete`


**method**:`POST`


**produces**:`application/json`


**consumes**:`*/*`


**Note**:


**Example**:


```javascript
{
  "carId": 0,
  "color": 0,
  "distance": 0,
  "geofenceId": 0,
  "geofenceName": "",
  "geofenceType": 0,
  "latlngs": ""
}
```


**Params**:


| name | description | in    | require | type | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|geofenceDto|geofenceDto|body|true|EmsGeofenceDto|EmsGeofenceDto|
|&emsp;&emsp;carId|||false|integer(int32)||
|&emsp;&emsp;color|||false|integer(int32)||
|&emsp;&emsp;distance|||false|integer(int32)||
|&emsp;&emsp;geofenceId|||false|integer(int32)||
|&emsp;&emsp;geofenceName|||false|string||
|&emsp;&emsp;geofenceType|||false|integer(int32)||
|&emsp;&emsp;latlngs|||false|string||


**Status**:


| code | description | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResult|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**Response Params**:


| name | description | type | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int64)|integer(int64)|
|data||object||
|message||string||


**Response Example**:
```javascript
{
	"code": 0,
	"data": {},
	"message": ""
}
```


## api_delete_geofence


**url**:`/geofence/delete_and_delete_car`


**method**:`POST`


**produces**:`application/json`


**consumes**:`*/*`


**Note**:


**Example**:


```javascript
{
  "carId": 0,
  "color": 0,
  "distance": 0,
  "geofenceId": 0,
  "geofenceName": "",
  "geofenceType": 0,
  "latlngs": ""
}
```


**Params**:


| name | description | in    | require | type | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|geofenceDto|geofenceDto|body|true|EmsGeofenceDto|EmsGeofenceDto|
|&emsp;&emsp;carId|||false|integer(int32)||
|&emsp;&emsp;color|||false|integer(int32)||
|&emsp;&emsp;distance|||false|integer(int32)||
|&emsp;&emsp;geofenceId|||false|integer(int32)||
|&emsp;&emsp;geofenceName|||false|string||
|&emsp;&emsp;geofenceType|||false|integer(int32)||
|&emsp;&emsp;latlngs|||false|string||


**Status**:


| code | description | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResult|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**Response Params**:


| name | description | type | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int64)|integer(int64)|
|data||object||
|message||string||


**Response Example**:
```javascript
{
	"code": 0,
	"data": {},
	"message": ""
}
```


## api_get_list_for_car


**url**:`/geofence/list_and_car`


**method**:`POST`


**produces**:`application/json`


**consumes**:`*/*`


**Note**:


**Example**:


```javascript
{
  "carId": 0,
  "color": 0,
  "distance": 0,
  "geofenceId": 0,
  "geofenceName": "",
  "geofenceType": 0,
  "latlngs": ""
}
```


**Params**:


| name | description | in    | require | type | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|geofenceDto|geofenceDto|body|true|EmsGeofenceDto|EmsGeofenceDto|
|&emsp;&emsp;carId|||false|integer(int32)||
|&emsp;&emsp;color|||false|integer(int32)||
|&emsp;&emsp;distance|||false|integer(int32)||
|&emsp;&emsp;geofenceId|||false|integer(int32)||
|&emsp;&emsp;geofenceName|||false|string||
|&emsp;&emsp;geofenceType|||false|integer(int32)||
|&emsp;&emsp;latlngs|||false|string||


**Status**:


| code | description | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResult«List«EmsGeofence对象»»|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**Response Params**:


| name | description | type | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int64)|integer(int64)|
|data||array|EmsGeofence对象|
|&emsp;&emsp;clientId||integer(int32)||
|&emsp;&emsp;color||integer(int32)||
|&emsp;&emsp;distance||integer(int32)||
|&emsp;&emsp;geofenceId||integer(int32)||
|&emsp;&emsp;geofenceName||string||
|&emsp;&emsp;geofenceType||integer(int32)||
|&emsp;&emsp;path||string||
|&emsp;&emsp;test||string||
|&emsp;&emsp;userId||integer(int32)||
|message||string||


**Response Example**:
```javascript
{
	"code": 0,
	"data": [
		{
			"clientId": 0,
			"color": 0,
			"distance": 0,
			"geofenceId": 0,
			"geofenceName": "",
			"geofenceType": 0,
			"path": "",
			"test": "",
			"userId": 0
		}
	],
	"message": ""
}
```


## api_update_geofence


**url**:`/geofence/update`


**method**:`POST`


**produces**:`application/json`


**consumes**:`*/*`


**Note**:


**Example**:


```javascript
{
  "carId": 0,
  "color": 0,
  "distance": 0,
  "geofenceId": 0,
  "geofenceName": "",
  "geofenceType": 0,
  "latlngs": ""
}
```


**Params**:


| name | description | in    | require | type | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|geofenceDto|geofenceDto|body|true|EmsGeofenceDto|EmsGeofenceDto|
|&emsp;&emsp;carId|||false|integer(int32)||
|&emsp;&emsp;color|||false|integer(int32)||
|&emsp;&emsp;distance|||false|integer(int32)||
|&emsp;&emsp;geofenceId|||false|integer(int32)||
|&emsp;&emsp;geofenceName|||false|string||
|&emsp;&emsp;geofenceType|||false|integer(int32)||
|&emsp;&emsp;latlngs|||false|string||


**Status**:


| code | description | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResult«EmsGeofence对象»|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**Response Params**:


| name | description | type | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int64)|integer(int64)|
|data||EmsGeofence对象|EmsGeofence对象|
|&emsp;&emsp;clientId||integer(int32)||
|&emsp;&emsp;color||integer(int32)||
|&emsp;&emsp;distance||integer(int32)||
|&emsp;&emsp;geofenceId||integer(int32)||
|&emsp;&emsp;geofenceName||string||
|&emsp;&emsp;geofenceType||integer(int32)||
|&emsp;&emsp;path||string||
|&emsp;&emsp;test||string||
|&emsp;&emsp;userId||integer(int32)||
|message||string||


**Response Example**:
```javascript
{
	"code": 0,
	"data": {
		"clientId": 0,
		"color": 0,
		"distance": 0,
		"geofenceId": 0,
		"geofenceName": "",
		"geofenceType": 0,
		"path": "",
		"test": "",
		"userId": 0
	},
	"message": ""
}
```


# Report Management


## Daily mileage report


**url**:`/report/daily_mileage_report`


**method**:`POST`


**produces**:`application/json`


**consumes**:`*/*`


**Note**:


**Example**:


```javascript
{
  "data": [],
  "endTime": "",
  "pageNum": 0,
  "pageSize": 0,
  "startTime": "",
  "timeOffset": 0,
  "type": ""
}
```


**Params**:


| name | description | in    | require | type | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|drivingReportDto|drivingReportDto|body|true|DrivingReportDto|DrivingReportDto|
|&emsp;&emsp;data|If the request parameter type=='carId', the carId set will be passed; If type=='RFID', transfer the RFID set ||true|array|string|
|&emsp;&emsp;endTime|End Time(yyyy-MM-dd HH:mm:ss)||false|string||
|&emsp;&emsp;pageNum|||false|integer(int32)||
|&emsp;&emsp;pageSize|||false|integer(int32)||
|&emsp;&emsp;startTime|Start time(yyyy-MM-dd HH:mm:ss)||false|string||
|&emsp;&emsp;timeOffset|Standard time difference (seconds)||false|integer(int32)||
|&emsp;&emsp;type|carId|rfid ||true|string||


**Status**:


| code | description | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResult«List«ResultGroupDrivingDailyMileagesDto«drivingReport对象»»»|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**Response Params**:


| name | description | type | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int64)|integer(int64)|
|data||array|ResultGroupDrivingDailyMileagesDto«drivingReport对象»|
|&emsp;&emsp;dayCount||integer(int32)||
|&emsp;&emsp;detail|drivingReport数据表|array|drivingReport对象|
|&emsp;&emsp;&emsp;&emsp;carId||integer||
|&emsp;&emsp;&emsp;&emsp;carName||string||
|&emsp;&emsp;&emsp;&emsp;endMileage||number||
|&emsp;&emsp;&emsp;&emsp;gpsTime||string||
|&emsp;&emsp;&emsp;&emsp;mileage||number||
|&emsp;&emsp;&emsp;&emsp;startMileage||number||
|&emsp;&emsp;totalMileage||number(double)||
|&emsp;&emsp;vehicleName||string||
|message||string||


**Response Example**:
```javascript
{
	"code": 0,
	"data": [
		{
			"dayCount": 0,
			"detail": [
				{
					"carId": 0,
					"carName": "",
					"endMileage": 0,
					"gpsTime": "",
					"mileage": 0,
					"startMileage": 0
				}
			],
			"totalMileage": 0,
			"vehicleName": ""
		}
	],
	"message": ""
}
```


## Obtain alarm information based on index


**url**:`/report/last_alarms`


**method**:`POST`


**produces**:`application/json`


**consumes**:`*/*`


**Note**:


**Example**:


```javascript
{
  "timeIndex": 0,
  "timeOffset": 0
}
```


**Params**:


| name | description | in    | require | type | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|rmsAlarmParamDto|rmsAlarmParamDto|body|true|RmsAlarmParamDto|RmsAlarmParamDto|
|&emsp;&emsp;timeIndex|||false|integer(int32)||
|&emsp;&emsp;timeOffset|Standard time difference (seconds)||false|integer(int32)||


**Status**:


| code | description | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResult«ResultAlarmListDto»|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**Response Params**:


| name | description | type | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int64)|integer(int64)|
|data||ResultAlarmListDto|ResultAlarmListDto|
|&emsp;&emsp;alarm||array|AlarmListDto|
|&emsp;&emsp;&emsp;&emsp;alarmTypeLangKey||string||
|&emsp;&emsp;&emsp;&emsp;alarmTypeText||string||
|&emsp;&emsp;&emsp;&emsp;almTime||integer||
|&emsp;&emsp;&emsp;&emsp;almTimeStr||string||
|&emsp;&emsp;&emsp;&emsp;almType||integer||
|&emsp;&emsp;&emsp;&emsp;autoIdx||integer||
|&emsp;&emsp;&emsp;&emsp;carId||integer||
|&emsp;&emsp;&emsp;&emsp;carName||string||
|&emsp;&emsp;&emsp;&emsp;deviceId||string||
|&emsp;&emsp;&emsp;&emsp;deviceType||string||
|&emsp;&emsp;&emsp;&emsp;extData||string||
|&emsp;&emsp;&emsp;&emsp;origin||integer||
|&emsp;&emsp;&emsp;&emsp;positionDataDto||PositionDataDto|PositionDataDto|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;acc||integer||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;carId||integer||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;devId||string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;devType||integer||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;direction||integer||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;exBytes||string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;gpsTime||integer||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;gpsTimeStr||string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;lat||string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;lng||string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;locate||integer||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;mileage||integer||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;speed||integer||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;status||string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;updateTime||integer||
|&emsp;&emsp;timeIndex||integer(int64)||
|message||string||


**Response Example**:
```javascript
{
	"code": 0,
	"data": {
		"alarm": [
			{
				"alarmTypeLangKey": "",
				"alarmTypeText": "",
				"almTime": 0,
				"almTimeStr": "",
				"almType": 0,
				"autoIdx": 0,
				"carId": 0,
				"carName": "",
				"deviceId": "",
				"deviceType": "",
				"extData": "",
				"origin": 0,
				"positionDataDto": {
					"acc": 0,
					"carId": 0,
					"devId": "",
					"devType": 0,
					"direction": 0,
					"exBytes": "",
					"gpsTime": 0,
					"gpsTimeStr": "",
					"lat": "",
					"lng": "",
					"locate": 0,
					"mileage": 0,
					"speed": 0,
					"status": "",
					"updateTime": 0
				}
			}
		],
		"timeIndex": 0
	},
	"message": ""
}
```


## Driving Summary Report


**url**:`/report/driving_summary_report`


**method**:`POST`


**produces**:`application/json`


**consumes**:`*/*`


**Note**:


**Example**:


```javascript
{
  "data": [],
  "endTime": "",
  "pageNum": 0,
  "pageSize": 0,
  "startTime": "",
  "timeOffset": 0,
  "type": ""
}
```


**Params**:


| name | description | in    | require | type | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|drivingReportDto|drivingReportDto|body|true|DrivingReportDto|DrivingReportDto|
|&emsp;&emsp;data|If the request parameter type=='carId', the carId set will be passed; If type=='RFID', transfer the RFID set ||true|array|string|
|&emsp;&emsp;endTime|End Time(yyyy-MM-dd HH:mm:ss)||false|string||
|&emsp;&emsp;pageNum|||false|integer(int32)||
|&emsp;&emsp;pageSize|||false|integer(int32)||
|&emsp;&emsp;startTime|Start time(yyyy-MM-dd HH:mm:ss)||false|string||
|&emsp;&emsp;timeOffset|Standard time difference (seconds)||false|integer(int32)||
|&emsp;&emsp;type|carId|rfid ||true|string||


**Status**:


| code | description | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResult«List«ResultGroupDrivingSummaryDto«DrivingSummaryResultDto»»»|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**Response Params**:


| name | description | type | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int64)|integer(int64)|
|data||array|ResultGroupDrivingSummaryDto«DrivingSummaryResultDto»|
|&emsp;&emsp;avgSpeed||number(double)||
|&emsp;&emsp;carName|carName|string||
|&emsp;&emsp;data||array|DrivingSummaryResultDto|
|&emsp;&emsp;&emsp;&emsp;avgSpeed||number||
|&emsp;&emsp;&emsp;&emsp;carName||string||
|&emsp;&emsp;&emsp;&emsp;date||string||
|&emsp;&emsp;&emsp;&emsp;idlingCount||integer||
|&emsp;&emsp;&emsp;&emsp;idlingDuration||integer||
|&emsp;&emsp;&emsp;&emsp;maxSpeed||number||
|&emsp;&emsp;&emsp;&emsp;parkingCount||integer||
|&emsp;&emsp;&emsp;&emsp;parkingDuration||integer||
|&emsp;&emsp;&emsp;&emsp;tripCount||integer||
|&emsp;&emsp;&emsp;&emsp;tripDuration||integer||
|&emsp;&emsp;&emsp;&emsp;tripMileage||integer||
|&emsp;&emsp;idlingCount||integer(int32)||
|&emsp;&emsp;idlingDuration||integer(int64)||
|&emsp;&emsp;maxSpeed||number(double)||
|&emsp;&emsp;parkingCount||integer(int32)||
|&emsp;&emsp;parkingDuration||integer(int64)||
|&emsp;&emsp;tripCount||integer(int32)||
|&emsp;&emsp;tripDuration||integer(int64)||
|&emsp;&emsp;tripMileage||integer(int32)||
|message||string||


**Response Example**:
```javascript
{
	"code": 0,
	"data": [
		{
			"avgSpeed": 0,
			"carName": "",
			"data": [
				{
					"avgSpeed": 0,
					"carName": "",
					"date": "",
					"idlingCount": 0,
					"idlingDuration": 0,
					"maxSpeed": 0,
					"parkingCount": 0,
					"parkingDuration": 0,
					"tripCount": 0,
					"tripDuration": 0,
					"tripMileage": 0
				}
			],
			"idlingCount": 0,
			"idlingDuration": 0,
			"maxSpeed": 0,
			"parkingCount": 0,
			"parkingDuration": 0,
			"tripCount": 0,
			"tripDuration": 0,
			"tripMileage": 0
		}
	],
	"message": ""
}
```


## Trip Report


**url**:`/report/trip_report`


**method**:`POST`


**produces**:`application/json`


**consumes**:`*/*`


**Note**:


**Example**:


```javascript
{
  "data": [],
  "endTime": "",
  "pageNum": 0,
  "pageSize": 0,
  "startTime": "",
  "timeOffset": 0,
  "type": ""
}
```


**Params**:


| name | description | in    | require | type | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|drivingReportDto|drivingReportDto|body|true|DrivingReportDto|DrivingReportDto|
|&emsp;&emsp;data|If the request parameter type=='carId', the carId set will be passed; If type=='RFID', transfer the RFID set ||true|array|string|
|&emsp;&emsp;endTime|End Time(yyyy-MM-dd HH:mm:ss)||false|string||
|&emsp;&emsp;pageNum|||false|integer(int32)||
|&emsp;&emsp;pageSize|||false|integer(int32)||
|&emsp;&emsp;startTime|Start time(yyyy-MM-dd HH:mm:ss)||false|string||
|&emsp;&emsp;timeOffset|Standard time difference (seconds)||false|integer(int32)||
|&emsp;&emsp;type|carId|rfid ||true|string||


**Status**:


| code | description | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResult«List«ResultGroupRunReportDto»»|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**Response Params**:


| name | description | type | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int64)|integer(int64)|
|data||array|ResultGroupRunReportDto|
|&emsp;&emsp;avgSpeed||number(double)||
|&emsp;&emsp;carName|carName|string||
|&emsp;&emsp;count||integer(int32)||
|&emsp;&emsp;data||array|RunReportResultDto|
|&emsp;&emsp;&emsp;&emsp;avgSpeed||number||
|&emsp;&emsp;&emsp;&emsp;carId||integer||
|&emsp;&emsp;&emsp;&emsp;carName|车牌号码|string||
|&emsp;&emsp;&emsp;&emsp;driverName||string||
|&emsp;&emsp;&emsp;&emsp;endDateTime||string||
|&emsp;&emsp;&emsp;&emsp;endLat||number||
|&emsp;&emsp;&emsp;&emsp;endLng||number||
|&emsp;&emsp;&emsp;&emsp;endMileage||integer||
|&emsp;&emsp;&emsp;&emsp;maxSpeed||number||
|&emsp;&emsp;&emsp;&emsp;mileage||integer||
|&emsp;&emsp;&emsp;&emsp;startDateTime||string||
|&emsp;&emsp;&emsp;&emsp;startLat||number||
|&emsp;&emsp;&emsp;&emsp;startLng||number||
|&emsp;&emsp;&emsp;&emsp;startMileage||integer||
|&emsp;&emsp;&emsp;&emsp;tripDuration||integer||
|&emsp;&emsp;duration||integer(int64)||
|&emsp;&emsp;maxSpeed||number(double)||
|&emsp;&emsp;mileage||integer(int64)||
|message||string||


**Response Example**:
```javascript
{
	"code": 0,
	"data": [
		{
			"avgSpeed": 0,
			"carName": "",
			"count": 0,
			"data": [
				{
					"avgSpeed": 0,
					"carId": 0,
					"carName": "",
					"driverName": "",
					"endDateTime": "",
					"endLat": 0,
					"endLng": 0,
					"endMileage": 0,
					"maxSpeed": 0,
					"mileage": 0,
					"startDateTime": "",
					"startLat": 0,
					"startLng": 0,
					"startMileage": 0,
					"tripDuration": 0
				}
			],
			"duration": 0,
			"maxSpeed": 0,
			"mileage": 0
		}
	],
	"message": ""
}
```


## Idling Report


**url**:`/report/idling_report`


**method**:`POST`


**produces**:`application/json`


**consumes**:`*/*`


**Note**:


**Example**:


```javascript
{
  "data": [],
  "endTime": "",
  "pageNum": 0,
  "pageSize": 0,
  "startTime": "",
  "timeOffset": 0,
  "type": ""
}
```


**Params**:


| name | description | in    | require | type | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|drivingReportDto|drivingReportDto|body|true|DrivingReportDto|DrivingReportDto|
|&emsp;&emsp;data|If the request parameter type=='carId', the carId set will be passed; If type=='RFID', transfer the RFID set ||true|array|string|
|&emsp;&emsp;endTime|End Time(yyyy-MM-dd HH:mm:ss)||false|string||
|&emsp;&emsp;pageNum|||false|integer(int32)||
|&emsp;&emsp;pageSize|||false|integer(int32)||
|&emsp;&emsp;startTime|Start time(yyyy-MM-dd HH:mm:ss)||false|string||
|&emsp;&emsp;timeOffset|Standard time difference (seconds)||false|integer(int32)||
|&emsp;&emsp;type|carId|rfid ||true|string||


**Status**:


| code | description | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResult«List«ResultGroupParkingReportDto»»|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**Response Params**:


| name | description | type | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int64)|integer(int64)|
|data||array|ResultGroupParkingReportDto|
|&emsp;&emsp;carId|Car Id|integer(int32)||
|&emsp;&emsp;carName|carName|string||
|&emsp;&emsp;count||integer(int32)||
|&emsp;&emsp;data||array|ResultParkingReportDto|
|&emsp;&emsp;&emsp;&emsp;alarmType|Alarm type|string||
|&emsp;&emsp;&emsp;&emsp;carId|Car Id|integer||
|&emsp;&emsp;&emsp;&emsp;carName|carName|string||
|&emsp;&emsp;&emsp;&emsp;driverName||string||
|&emsp;&emsp;&emsp;&emsp;duration||integer||
|&emsp;&emsp;&emsp;&emsp;endFuel|End fuel quantity|integer||
|&emsp;&emsp;&emsp;&emsp;endTime|End time|string||
|&emsp;&emsp;&emsp;&emsp;fuel|Single fuel consumption|integer||
|&emsp;&emsp;&emsp;&emsp;lat||number||
|&emsp;&emsp;&emsp;&emsp;lng||number||
|&emsp;&emsp;&emsp;&emsp;refuelVolume|This refueling volume|integer||
|&emsp;&emsp;&emsp;&emsp;rfidNum||string||
|&emsp;&emsp;&emsp;&emsp;startFuel|Start fuel quantity|integer||
|&emsp;&emsp;&emsp;&emsp;startTime|Start time|string||
|&emsp;&emsp;driverName|Car Id|string||
|&emsp;&emsp;duration||integer(int64)||
|&emsp;&emsp;rfidNum|RFID Num|string||
|message||string||


**Response Example**:
```javascript
{
	"code": 0,
	"data": [
		{
			"carId": 0,
			"carName": "",
			"count": 0,
			"data": [
				{
					"alarmType": "",
					"carId": 0,
					"carName": "",
					"driverName": "",
					"duration": 0,
					"endFuel": 0,
					"endTime": "",
					"fuel": 0,
					"lat": 0,
					"lng": 0,
					"refuelVolume": 0,
					"rfidNum": "",
					"startFuel": 0,
					"startTime": ""
				}
			],
			"driverName": "",
			"duration": 0,
			"rfidNum": ""
		}
	],
	"message": ""
}
```


## Alarm Report


**url**:`/report/alarm_report`


**method**:`POST`


**produces**:`application/json`


**consumes**:`*/*`


**Note**:


**Example**:


```javascript
{
  "alarmType": [],
  "carIds": [],
  "endTime": "",
  "startTime": "",
  "timeOffset": 0
}
```


**Params**:


| name | description | in    | require | type | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|alarmReportDto|alarmReportDto|body|true|AlarmReportDto|AlarmReportDto|
|&emsp;&emsp;alarmType|||false|array|string|
|&emsp;&emsp;carIds|||false|array|string|
|&emsp;&emsp;endTime|End Time(yyyy-MM-dd HH:mm:ss)||false|string||
|&emsp;&emsp;startTime|Start time(yyyy-MM-dd HH:mm:ss)||false|string||
|&emsp;&emsp;timeOffset|Standard time difference (seconds)||false|integer(int32)||


**Status**:


| code | description | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResult«List«ResultAlarmReportDto»»|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**Response Params**:


| name | description | type | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int64)|integer(int64)|
|data||array|ResultAlarmReportDto|
|&emsp;&emsp;almTime||integer(int64)||
|&emsp;&emsp;almTimeStr||string||
|&emsp;&emsp;almType||integer(int32)||
|&emsp;&emsp;carId||integer(int32)||
|&emsp;&emsp;deviceType||string||
|&emsp;&emsp;extData||string||
|&emsp;&emsp;operation||integer(int32)||
|&emsp;&emsp;origin||integer(int32)||
|&emsp;&emsp;positionData||string||
|&emsp;&emsp;result||string||
|&emsp;&emsp;userName||string||
|message||string||


**Response Example**:
```javascript
{
	"code": 0,
	"data": [
		{
			"almTime": 0,
			"almTimeStr": "",
			"almType": 0,
			"carId": 0,
			"deviceType": "",
			"extData": "",
			"operation": 0,
			"origin": 0,
			"positionData": "",
			"result": "",
			"userName": ""
		}
	],
	"message": ""
}
```


## Driving behavior report


**url**:`/report/driving_behavior_report`


**method**:`POST`


**produces**:`application/json`


**consumes**:`*/*`


**Note**:


**Example**:


```javascript
{
  "data": [],
  "endTime": "",
  "pageNum": 0,
  "pageSize": 0,
  "startTime": "",
  "timeOffset": 0,
  "type": ""
}
```


**Params**:


| name | description | in    | require | type | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|drivingReportDto|drivingReportDto|body|true|DrivingReportDto|DrivingReportDto|
|&emsp;&emsp;data|If the request parameter type=='carId', the carId set will be passed; If type=='RFID', transfer the RFID set ||true|array|string|
|&emsp;&emsp;endTime|End Time(yyyy-MM-dd HH:mm:ss)||false|string||
|&emsp;&emsp;pageNum|||false|integer(int32)||
|&emsp;&emsp;pageSize|||false|integer(int32)||
|&emsp;&emsp;startTime|Start time(yyyy-MM-dd HH:mm:ss)||false|string||
|&emsp;&emsp;timeOffset|Standard time difference (seconds)||false|integer(int32)||
|&emsp;&emsp;type|carId|rfid ||true|string||


**Status**:


| code | description | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResult«List«ResultGroupDrivingBehaviorDto»»|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**Response Params**:


| name | description | type | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int64)|integer(int64)|
|data||array|ResultGroupDrivingBehaviorDto|
|&emsp;&emsp;carId|Car Id|integer(int32)||
|&emsp;&emsp;carName|carName|string||
|&emsp;&emsp;collision|Collision times|integer(int32)||
|&emsp;&emsp;collisions|Collision data|array|DrivingCollisionDto|
|&emsp;&emsp;&emsp;&emsp;alarmType|Alarm type|string||
|&emsp;&emsp;&emsp;&emsp;carName|carName|string||
|&emsp;&emsp;&emsp;&emsp;dateTime|Time|string||
|&emsp;&emsp;&emsp;&emsp;lat|latitude|number||
|&emsp;&emsp;&emsp;&emsp;lng|longitude|number||
|&emsp;&emsp;&emsp;&emsp;rfidNum|driverRFID|string||
|&emsp;&emsp;corneringOverSpeed|Cornering over speed times|integer(int32)||
|&emsp;&emsp;corneringOverSpeeds|Cornering over speed data|array|CorneringOverSpeedDto|
|&emsp;&emsp;&emsp;&emsp;alarmType|Alarm type|string||
|&emsp;&emsp;&emsp;&emsp;carName|carName|string||
|&emsp;&emsp;&emsp;&emsp;dateTime|Time|string||
|&emsp;&emsp;&emsp;&emsp;lat|latitude|number||
|&emsp;&emsp;&emsp;&emsp;lng|longitude|number||
|&emsp;&emsp;&emsp;&emsp;rfidNum|driverRFID|string||
|&emsp;&emsp;driverName|driverName|string||
|&emsp;&emsp;driverRFID|driverRFID|string||
|&emsp;&emsp;fatigueDriving|Fatigue driving times|integer(int32)||
|&emsp;&emsp;fatigueDrivings|Fatigue driving data|array|FatigueDrivingDto|
|&emsp;&emsp;&emsp;&emsp;alarmType|Alarm type|string||
|&emsp;&emsp;&emsp;&emsp;carName|carName|string||
|&emsp;&emsp;&emsp;&emsp;duration|Duration|integer||
|&emsp;&emsp;&emsp;&emsp;endLat|End latitude|number||
|&emsp;&emsp;&emsp;&emsp;endLng|End longitude|number||
|&emsp;&emsp;&emsp;&emsp;endTime|End time|string||
|&emsp;&emsp;&emsp;&emsp;rfidNum|driverRFID|string||
|&emsp;&emsp;&emsp;&emsp;startLat|Start latitude|number||
|&emsp;&emsp;&emsp;&emsp;startLng|Start longitude|number||
|&emsp;&emsp;&emsp;&emsp;startTime|Start time|string||
|&emsp;&emsp;harshAcceleration|Hash acceleration times|integer(int32)||
|&emsp;&emsp;harshAccelerations|Hash acceleration data|array|HarshAccelerationDto|
|&emsp;&emsp;&emsp;&emsp;alarmType|Alarm type|string||
|&emsp;&emsp;&emsp;&emsp;carName|carName|string||
|&emsp;&emsp;&emsp;&emsp;dateTime|Time|string||
|&emsp;&emsp;&emsp;&emsp;lat|latitude|number||
|&emsp;&emsp;&emsp;&emsp;lng|longitude|number||
|&emsp;&emsp;&emsp;&emsp;rfidNum|driverRFID|string||
|&emsp;&emsp;harshBrake|Harsh brake times|integer(int32)||
|&emsp;&emsp;harshBrakes|Harsh brake data|array|HarshBrakeDto|
|&emsp;&emsp;&emsp;&emsp;alarmType|Alarm type|string||
|&emsp;&emsp;&emsp;&emsp;carName|carName|string||
|&emsp;&emsp;&emsp;&emsp;dateTime|Time|string||
|&emsp;&emsp;&emsp;&emsp;lat|latitude|number||
|&emsp;&emsp;&emsp;&emsp;lng|longitude|number||
|&emsp;&emsp;&emsp;&emsp;rfidNum|driverRFID|string||
|&emsp;&emsp;idle|Idle times|integer(int32)||
|&emsp;&emsp;idles|Idle data|array|ResultParkingReportDto|
|&emsp;&emsp;&emsp;&emsp;alarmType|Alarm type|string||
|&emsp;&emsp;&emsp;&emsp;carId|Car Id|integer||
|&emsp;&emsp;&emsp;&emsp;carName|carName|string||
|&emsp;&emsp;&emsp;&emsp;driverName||string||
|&emsp;&emsp;&emsp;&emsp;duration||integer||
|&emsp;&emsp;&emsp;&emsp;endFuel|End fuel quantity|integer||
|&emsp;&emsp;&emsp;&emsp;endTime|End time|string||
|&emsp;&emsp;&emsp;&emsp;fuel|Single fuel consumption|integer||
|&emsp;&emsp;&emsp;&emsp;lat||number||
|&emsp;&emsp;&emsp;&emsp;lng||number||
|&emsp;&emsp;&emsp;&emsp;refuelVolume|This refueling volume|integer||
|&emsp;&emsp;&emsp;&emsp;rfidNum||string||
|&emsp;&emsp;&emsp;&emsp;startFuel|Start fuel quantity|integer||
|&emsp;&emsp;&emsp;&emsp;startTime|Start time|string||
|&emsp;&emsp;overSpeed|Over speed times|integer(int32)||
|&emsp;&emsp;overSpeeds|Over speed data|array|OverSpeedDto|
|&emsp;&emsp;&emsp;&emsp;alarmType|Alarm type|string||
|&emsp;&emsp;&emsp;&emsp;avgSpeed|Average velocity|number||
|&emsp;&emsp;&emsp;&emsp;carName|carName|string||
|&emsp;&emsp;&emsp;&emsp;duration|Duration|integer||
|&emsp;&emsp;&emsp;&emsp;endLat|End latitude|number||
|&emsp;&emsp;&emsp;&emsp;endLng|End longitude|number||
|&emsp;&emsp;&emsp;&emsp;endMileage|End Mileage|integer||
|&emsp;&emsp;&emsp;&emsp;endTime|End time|string||
|&emsp;&emsp;&emsp;&emsp;maxSpeed|Maximum speed|integer||
|&emsp;&emsp;&emsp;&emsp;rfidNum|driverRFID|string||
|&emsp;&emsp;&emsp;&emsp;startLat|Start latitude|number||
|&emsp;&emsp;&emsp;&emsp;startLng|Start longitude|number||
|&emsp;&emsp;&emsp;&emsp;startMileage|Start Mileage|integer||
|&emsp;&emsp;&emsp;&emsp;startTime|Start time|string||
|message||string||


**Response Example**:
```javascript
{
	"code": 0,
	"data": [
		{
			"carId": 0,
			"carName": "",
			"collision": 0,
			"collisions": [
				{
					"alarmType": "",
					"carName": "",
					"dateTime": "",
					"lat": 0,
					"lng": 0,
					"rfidNum": ""
				}
			],
			"corneringOverSpeed": 0,
			"corneringOverSpeeds": [
				{
					"alarmType": "",
					"carName": "",
					"dateTime": "",
					"lat": 0,
					"lng": 0,
					"rfidNum": ""
				}
			],
			"driverName": "",
			"driverRFID": "",
			"fatigueDriving": 0,
			"fatigueDrivings": [
				{
					"alarmType": "",
					"carName": "",
					"duration": 0,
					"endLat": 0,
					"endLng": 0,
					"endTime": "",
					"rfidNum": "",
					"startLat": 0,
					"startLng": 0,
					"startTime": ""
				}
			],
			"harshAcceleration": 0,
			"harshAccelerations": [
				{
					"alarmType": "",
					"carName": "",
					"dateTime": "",
					"lat": 0,
					"lng": 0,
					"rfidNum": ""
				}
			],
			"harshBrake": 0,
			"harshBrakes": [
				{
					"alarmType": "",
					"carName": "",
					"dateTime": "",
					"lat": 0,
					"lng": 0,
					"rfidNum": ""
				}
			],
			"idle": 0,
			"idles": [
				{
					"alarmType": "",
					"carId": 0,
					"carName": "",
					"driverName": "",
					"duration": 0,
					"endFuel": 0,
					"endTime": "",
					"fuel": 0,
					"lat": 0,
					"lng": 0,
					"refuelVolume": 0,
					"rfidNum": "",
					"startFuel": 0,
					"startTime": ""
				}
			],
			"overSpeed": 0,
			"overSpeeds": [
				{
					"alarmType": "",
					"avgSpeed": 0,
					"carName": "",
					"duration": 0,
					"endLat": 0,
					"endLng": 0,
					"endMileage": 0,
					"endTime": "",
					"maxSpeed": 0,
					"rfidNum": "",
					"startLat": 0,
					"startLng": 0,
					"startMileage": 0,
					"startTime": ""
				}
			]
		}
	],
	"message": ""
}
```


## Parking report


**url**:`/report/driving_parking_report`


**method**:`POST`


**produces**:`application/json`


**consumes**:`*/*`


**Note**:


**Example**:


```javascript
{
  "data": [],
  "endTime": "",
  "pageNum": 0,
  "pageSize": 0,
  "startTime": "",
  "timeOffset": 0,
  "type": ""
}
```


**Params**:


| name | description | in    | require | type | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|drivingReportDto|drivingReportDto|body|true|DrivingReportDto|DrivingReportDto|
|&emsp;&emsp;data|If the request parameter type=='carId', the carId set will be passed; If type=='RFID', transfer the RFID set ||true|array|string|
|&emsp;&emsp;endTime|End Time(yyyy-MM-dd HH:mm:ss)||false|string||
|&emsp;&emsp;pageNum|||false|integer(int32)||
|&emsp;&emsp;pageSize|||false|integer(int32)||
|&emsp;&emsp;startTime|Start time(yyyy-MM-dd HH:mm:ss)||false|string||
|&emsp;&emsp;timeOffset|Standard time difference (seconds)||false|integer(int32)||
|&emsp;&emsp;type|carId|rfid ||true|string||


**Status**:


| code | description | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResult«List«ResultGroupParkingReportDto»»|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**Response Params**:


| name | description | type | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int64)|integer(int64)|
|data||array|ResultGroupParkingReportDto|
|&emsp;&emsp;carId|Car Id|integer(int32)||
|&emsp;&emsp;carName|carName|string||
|&emsp;&emsp;count||integer(int32)||
|&emsp;&emsp;data||array|ResultParkingReportDto|
|&emsp;&emsp;&emsp;&emsp;alarmType|Alarm type|string||
|&emsp;&emsp;&emsp;&emsp;carId|Car Id|integer||
|&emsp;&emsp;&emsp;&emsp;carName|carName|string||
|&emsp;&emsp;&emsp;&emsp;driverName||string||
|&emsp;&emsp;&emsp;&emsp;duration||integer||
|&emsp;&emsp;&emsp;&emsp;endFuel|End fuel quantity|integer||
|&emsp;&emsp;&emsp;&emsp;endTime|End time|string||
|&emsp;&emsp;&emsp;&emsp;fuel|Single fuel consumption|integer||
|&emsp;&emsp;&emsp;&emsp;lat||number||
|&emsp;&emsp;&emsp;&emsp;lng||number||
|&emsp;&emsp;&emsp;&emsp;refuelVolume|This refueling volume|integer||
|&emsp;&emsp;&emsp;&emsp;rfidNum||string||
|&emsp;&emsp;&emsp;&emsp;startFuel|Start fuel quantity|integer||
|&emsp;&emsp;&emsp;&emsp;startTime|Start time|string||
|&emsp;&emsp;driverName|Car Id|string||
|&emsp;&emsp;duration||integer(int64)||
|&emsp;&emsp;rfidNum|RFID Num|string||
|message||string||


**Response Example**:
```javascript
{
	"code": 0,
	"data": [
		{
			"carId": 0,
			"carName": "",
			"count": 0,
			"data": [
				{
					"alarmType": "",
					"carId": 0,
					"carName": "",
					"driverName": "",
					"duration": 0,
					"endFuel": 0,
					"endTime": "",
					"fuel": 0,
					"lat": 0,
					"lng": 0,
					"refuelVolume": 0,
					"rfidNum": "",
					"startFuel": 0,
					"startTime": ""
				}
			],
			"driverName": "",
			"duration": 0,
			"rfidNum": ""
		}
	],
	"message": ""
}
```


## Parking report


**url**:`/report/page/driving_day_mileage_report`


**method**:`POST`


**produces**:`application/json`


**consumes**:`*/*`


**Note**:


**Example**:


```javascript
{
  "carId": 0,
  "endTime": "",
  "pageNum": 0,
  "pageSize": 0,
  "startTime": "",
  "timeOffset": 0
}
```


**Params**:


| name | description | in    | require | type | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|drivingReportDto|drivingReportDto|body|true|DrivingPageReportDto|DrivingPageReportDto|
|&emsp;&emsp;carId|||false|integer(int32)||
|&emsp;&emsp;endTime|End Time(yyyy-MM-dd HH:mm:ss)||false|string||
|&emsp;&emsp;pageNum|||false|integer(int32)||
|&emsp;&emsp;pageSize|||false|integer(int32)||
|&emsp;&emsp;startTime|Start time(yyyy-MM-dd HH:mm:ss)||false|string||
|&emsp;&emsp;timeOffset|Standard time difference (seconds)||false|integer(int32)||


**Status**:


| code | description | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResult«CommonPage«drivingReport对象»»|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**Response Params**:


| name | description | type | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int64)|integer(int64)|
|data||CommonPage«drivingReport对象»|CommonPage«drivingReport对象»|
|&emsp;&emsp;list|drivingReport数据表|array|drivingReport对象|
|&emsp;&emsp;&emsp;&emsp;carId||integer||
|&emsp;&emsp;&emsp;&emsp;carName||string||
|&emsp;&emsp;&emsp;&emsp;endMileage||number||
|&emsp;&emsp;&emsp;&emsp;gpsTime||string||
|&emsp;&emsp;&emsp;&emsp;mileage||number||
|&emsp;&emsp;&emsp;&emsp;startMileage||number||
|&emsp;&emsp;pageNum||integer(int32)||
|&emsp;&emsp;pageSize||integer(int32)||
|&emsp;&emsp;total||integer(int64)||
|&emsp;&emsp;totalPage||integer(int32)||
|message||string||


**Response Example**:
```javascript
{
	"code": 0,
	"data": {
		"list": [
			{
				"carId": 0,
				"carName": "",
				"endMileage": 0,
				"gpsTime": "",
				"mileage": 0,
				"startMileage": 0
			}
		],
		"pageNum": 0,
		"pageSize": 0,
		"total": 0,
		"totalPage": 0
	},
	"message": ""
}
```


## Parking report


**url**:`/report/page/driving_history_track_report`


**method**:`POST`


**produces**:`application/json`


**consumes**:`*/*`


**Note**:


**Example**:


```javascript
{
  "carId": 0,
  "endTime": "",
  "pageNum": 0,
  "pageSize": 0,
  "startTime": "",
  "timeOffset": 0
}
```


**Params**:


| name | description | in    | require | type | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|drivingReportDto|drivingReportDto|body|true|DrivingPageReportDto|DrivingPageReportDto|
|&emsp;&emsp;carId|||false|integer(int32)||
|&emsp;&emsp;endTime|End Time(yyyy-MM-dd HH:mm:ss)||false|string||
|&emsp;&emsp;pageNum|||false|integer(int32)||
|&emsp;&emsp;pageSize|||false|integer(int32)||
|&emsp;&emsp;startTime|Start time(yyyy-MM-dd HH:mm:ss)||false|string||
|&emsp;&emsp;timeOffset|Standard time difference (seconds)||false|integer(int32)||


**Status**:


| code | description | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResult«CommonPage«EmsMongoTrackModel»»|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**Response Params**:


| name | description | type | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int64)|integer(int64)|
|data||CommonPage«EmsMongoTrackModel»|CommonPage«EmsMongoTrackModel»|
|&emsp;&emsp;list||array|EmsMongoTrackModel|
|&emsp;&emsp;&emsp;&emsp;acc||integer||
|&emsp;&emsp;&emsp;&emsp;backCapStatus||integer||
|&emsp;&emsp;&emsp;&emsp;carId||integer||
|&emsp;&emsp;&emsp;&emsp;devBatteryPCT||integer||
|&emsp;&emsp;&emsp;&emsp;devId||string||
|&emsp;&emsp;&emsp;&emsp;devTemp||integer||
|&emsp;&emsp;&emsp;&emsp;devType||integer||
|&emsp;&emsp;&emsp;&emsp;direction||string||
|&emsp;&emsp;&emsp;&emsp;event||string||
|&emsp;&emsp;&emsp;&emsp;exBytes||string||
|&emsp;&emsp;&emsp;&emsp;gpsCount||integer||
|&emsp;&emsp;&emsp;&emsp;gpsTime||integer||
|&emsp;&emsp;&emsp;&emsp;gsmMode||string||
|&emsp;&emsp;&emsp;&emsp;gsmSignal||integer||
|&emsp;&emsp;&emsp;&emsp;gtime||string||
|&emsp;&emsp;&emsp;&emsp;lat||number||
|&emsp;&emsp;&emsp;&emsp;lng||number||
|&emsp;&emsp;&emsp;&emsp;locate||integer||
|&emsp;&emsp;&emsp;&emsp;lockStatus||integer||
|&emsp;&emsp;&emsp;&emsp;mileage||integer||
|&emsp;&emsp;&emsp;&emsp;motorLockStatus||integer||
|&emsp;&emsp;&emsp;&emsp;online||boolean||
|&emsp;&emsp;&emsp;&emsp;speed||integer||
|&emsp;&emsp;&emsp;&emsp;status||string||
|&emsp;&emsp;&emsp;&emsp;steelStringStatus||integer||
|&emsp;&emsp;&emsp;&emsp;todayIdlingSeconds||integer||
|&emsp;&emsp;&emsp;&emsp;todayMaxSpeed||integer||
|&emsp;&emsp;&emsp;&emsp;updateTime||integer||
|&emsp;&emsp;&emsp;&emsp;utime||string||
|&emsp;&emsp;pageNum||integer(int32)||
|&emsp;&emsp;pageSize||integer(int32)||
|&emsp;&emsp;total||integer(int64)||
|&emsp;&emsp;totalPage||integer(int32)||
|message||string||


**Response Example**:
```javascript
{
	"code": 0,
	"data": {
		"list": [
			{
				"acc": 0,
				"backCapStatus": 0,
				"carId": 0,
				"devBatteryPCT": 0,
				"devId": "",
				"devTemp": 0,
				"devType": 0,
				"direction": "",
				"event": "",
				"exBytes": "",
				"gpsCount": 0,
				"gpsTime": 0,
				"gsmMode": "",
				"gsmSignal": 0,
				"gtime": "",
				"lat": 0,
				"lng": 0,
				"locate": 0,
				"lockStatus": 0,
				"mileage": 0,
				"motorLockStatus": 0,
				"online": true,
				"speed": 0,
				"status": "",
				"steelStringStatus": 0,
				"todayIdlingSeconds": 0,
				"todayMaxSpeed": 0,
				"updateTime": 0,
				"utime": ""
			}
		],
		"pageNum": 0,
		"pageSize": 0,
		"total": 0,
		"totalPage": 0
	},
	"message": ""
}
```


## Parking report


**url**:`/report/page/driving_iding_report`


**method**:`POST`


**produces**:`application/json`


**consumes**:`*/*`


**Note**:


**Example**:


```javascript
{
  "carId": 0,
  "endTime": "",
  "pageNum": 0,
  "pageSize": 0,
  "startTime": "",
  "timeOffset": 0
}
```


**Params**:


| name | description | in    | require | type | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|drivingReportDto|drivingReportDto|body|true|DrivingPageReportDto|DrivingPageReportDto|
|&emsp;&emsp;carId|||false|integer(int32)||
|&emsp;&emsp;endTime|End Time(yyyy-MM-dd HH:mm:ss)||false|string||
|&emsp;&emsp;pageNum|||false|integer(int32)||
|&emsp;&emsp;pageSize|||false|integer(int32)||
|&emsp;&emsp;startTime|Start time(yyyy-MM-dd HH:mm:ss)||false|string||
|&emsp;&emsp;timeOffset|Standard time difference (seconds)||false|integer(int32)||


**Status**:


| code | description | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResult«CommonPage«drivingReport对象»»_1|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**Response Params**:


| name | description | type | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int64)|integer(int64)|
|data||CommonPage«drivingReport对象»_1|CommonPage«drivingReport对象»_1|
|&emsp;&emsp;list|drivingReport数据表|array|drivingReport对象_1|
|&emsp;&emsp;&emsp;&emsp;avgSpeed||number||
|&emsp;&emsp;&emsp;&emsp;carId||integer||
|&emsp;&emsp;&emsp;&emsp;driverName||string||
|&emsp;&emsp;&emsp;&emsp;driverRFID||string||
|&emsp;&emsp;&emsp;&emsp;durationSeconds||integer||
|&emsp;&emsp;&emsp;&emsp;endDatetime||integer||
|&emsp;&emsp;&emsp;&emsp;endFuelLiters||integer||
|&emsp;&emsp;&emsp;&emsp;endLat||number||
|&emsp;&emsp;&emsp;&emsp;endLng||number||
|&emsp;&emsp;&emsp;&emsp;endMileage||integer||
|&emsp;&emsp;&emsp;&emsp;endTime||string||
|&emsp;&emsp;&emsp;&emsp;gpsDataCount||integer||
|&emsp;&emsp;&emsp;&emsp;maxSpeed||number||
|&emsp;&emsp;&emsp;&emsp;reportType||integer||
|&emsp;&emsp;&emsp;&emsp;startDatetime||integer||
|&emsp;&emsp;&emsp;&emsp;startFuelLiters||integer||
|&emsp;&emsp;&emsp;&emsp;startLat||number||
|&emsp;&emsp;&emsp;&emsp;startLng||number||
|&emsp;&emsp;&emsp;&emsp;startMileage||integer||
|&emsp;&emsp;&emsp;&emsp;startTime||string||
|&emsp;&emsp;pageNum||integer(int32)||
|&emsp;&emsp;pageSize||integer(int32)||
|&emsp;&emsp;total||integer(int64)||
|&emsp;&emsp;totalPage||integer(int32)||
|message||string||


**Response Example**:
```javascript
{
	"code": 0,
	"data": {
		"list": [
			{
				"avgSpeed": 0,
				"carId": 0,
				"driverName": "",
				"driverRFID": "",
				"durationSeconds": 0,
				"endDatetime": 0,
				"endFuelLiters": 0,
				"endLat": 0,
				"endLng": 0,
				"endMileage": 0,
				"endTime": "",
				"gpsDataCount": 0,
				"maxSpeed": 0,
				"reportType": 0,
				"startDatetime": 0,
				"startFuelLiters": 0,
				"startLat": 0,
				"startLng": 0,
				"startMileage": 0,
				"startTime": ""
			}
		],
		"pageNum": 0,
		"pageSize": 0,
		"total": 0,
		"totalPage": 0
	},
	"message": ""
}
```


## driving_in_out_geo_report


**url**:`/report/page/driving_in_out_geo_report`


**method**:`POST`


**produces**:`application/json`


**consumes**:`*/*`


**Note**:


**Example**:


```javascript
{
  "carId": 0,
  "endTime": "",
  "pageNum": 0,
  "pageSize": 0,
  "startTime": "",
  "timeOffset": 0
}
```


**Params**:


| name | description | in    | require | type | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|drivingReportDto|drivingReportDto|body|true|DrivingPageReportDto|DrivingPageReportDto|
|&emsp;&emsp;carId|||false|integer(int32)||
|&emsp;&emsp;endTime|End Time(yyyy-MM-dd HH:mm:ss)||false|string||
|&emsp;&emsp;pageNum|||false|integer(int32)||
|&emsp;&emsp;pageSize|||false|integer(int32)||
|&emsp;&emsp;startTime|Start time(yyyy-MM-dd HH:mm:ss)||false|string||
|&emsp;&emsp;timeOffset|Standard time difference (seconds)||false|integer(int32)||


**Status**:


| code | description | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResult«CommonPage«车辆进出围栏报表»»|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**Response Params**:


| name | description | type | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int64)|integer(int64)|
|data||CommonPage«车辆进出围栏报表»|CommonPage«车辆进出围栏报表»|
|&emsp;&emsp;list|车辆进出围栏报表|array|车辆进出围栏报表|
|&emsp;&emsp;&emsp;&emsp;carId||integer||
|&emsp;&emsp;&emsp;&emsp;carName||string||
|&emsp;&emsp;&emsp;&emsp;geofenceId||integer||
|&emsp;&emsp;&emsp;&emsp;geofenceName||string||
|&emsp;&emsp;&emsp;&emsp;inPositionData||string||
|&emsp;&emsp;&emsp;&emsp;inTime||integer||
|&emsp;&emsp;&emsp;&emsp;inTimeStr||string||
|&emsp;&emsp;&emsp;&emsp;outPositionData||string||
|&emsp;&emsp;&emsp;&emsp;outTime||integer||
|&emsp;&emsp;&emsp;&emsp;outTimeStr||string||
|&emsp;&emsp;pageNum||integer(int32)||
|&emsp;&emsp;pageSize||integer(int32)||
|&emsp;&emsp;total||integer(int64)||
|&emsp;&emsp;totalPage||integer(int32)||
|message||string||


**Response Example**:
```javascript
{
	"code": 0,
	"data": {
		"list": [
			{
				"carId": 0,
				"carName": "",
				"geofenceId": 0,
				"geofenceName": "",
				"inPositionData": "",
				"inTime": 0,
				"inTimeStr": "",
				"outPositionData": "",
				"outTime": 0,
				"outTimeStr": ""
			}
		],
		"pageNum": 0,
		"pageSize": 0,
		"total": 0,
		"totalPage": 0
	},
	"message": ""
}
```


## Parking report


**url**:`/report/page/driving_over_speed_report`


**method**:`POST`


**produces**:`application/json`


**consumes**:`*/*`


**Note**:


**Example**:


```javascript
{
  "carId": 0,
  "endTime": "",
  "pageNum": 0,
  "pageSize": 0,
  "startTime": "",
  "timeOffset": 0
}
```


**Params**:


| name | description | in    | require | type | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|drivingReportDto|drivingReportDto|body|true|DrivingPageReportDto|DrivingPageReportDto|
|&emsp;&emsp;carId|||false|integer(int32)||
|&emsp;&emsp;endTime|End Time(yyyy-MM-dd HH:mm:ss)||false|string||
|&emsp;&emsp;pageNum|||false|integer(int32)||
|&emsp;&emsp;pageSize|||false|integer(int32)||
|&emsp;&emsp;startTime|Start time(yyyy-MM-dd HH:mm:ss)||false|string||
|&emsp;&emsp;timeOffset|Standard time difference (seconds)||false|integer(int32)||


**Status**:


| code | description | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResult«CommonPage«驾驶行为习惯对象»»|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**Response Params**:


| name | description | type | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int64)|integer(int64)|
|data||CommonPage«驾驶行为习惯对象»|CommonPage«驾驶行为习惯对象»|
|&emsp;&emsp;list|驾驶行为习惯数据表|array|驾驶行为习惯对象|
|&emsp;&emsp;&emsp;&emsp;avgSpeed||number||
|&emsp;&emsp;&emsp;&emsp;carId||integer||
|&emsp;&emsp;&emsp;&emsp;corneringName||string||
|&emsp;&emsp;&emsp;&emsp;driverName||string||
|&emsp;&emsp;&emsp;&emsp;driverRFID||string||
|&emsp;&emsp;&emsp;&emsp;durationSeconds||integer||
|&emsp;&emsp;&emsp;&emsp;endDatetime||integer||
|&emsp;&emsp;&emsp;&emsp;endLat||number||
|&emsp;&emsp;&emsp;&emsp;endLng||number||
|&emsp;&emsp;&emsp;&emsp;endMileage||integer||
|&emsp;&emsp;&emsp;&emsp;endSpeed||integer||
|&emsp;&emsp;&emsp;&emsp;endTime||string||
|&emsp;&emsp;&emsp;&emsp;maxSpeed||integer||
|&emsp;&emsp;&emsp;&emsp;reportType||integer||
|&emsp;&emsp;&emsp;&emsp;startDatetime||integer||
|&emsp;&emsp;&emsp;&emsp;startLat||number||
|&emsp;&emsp;&emsp;&emsp;startLng||number||
|&emsp;&emsp;&emsp;&emsp;startMileage||integer||
|&emsp;&emsp;&emsp;&emsp;startSpeed||integer||
|&emsp;&emsp;&emsp;&emsp;startTime||string||
|&emsp;&emsp;pageNum||integer(int32)||
|&emsp;&emsp;pageSize||integer(int32)||
|&emsp;&emsp;total||integer(int64)||
|&emsp;&emsp;totalPage||integer(int32)||
|message||string||


**Response Example**:
```javascript
{
	"code": 0,
	"data": {
		"list": [
			{
				"avgSpeed": 0,
				"carId": 0,
				"corneringName": "",
				"driverName": "",
				"driverRFID": "",
				"durationSeconds": 0,
				"endDatetime": 0,
				"endLat": 0,
				"endLng": 0,
				"endMileage": 0,
				"endSpeed": 0,
				"endTime": "",
				"maxSpeed": 0,
				"reportType": 0,
				"startDatetime": 0,
				"startLat": 0,
				"startLng": 0,
				"startMileage": 0,
				"startSpeed": 0,
				"startTime": ""
			}
		],
		"pageNum": 0,
		"pageSize": 0,
		"total": 0,
		"totalPage": 0
	},
	"message": ""
}
```


## Parking report


**url**:`/report/page/driving_parking_report`


**method**:`POST`


**produces**:`application/json`


**consumes**:`*/*`


**Note**:


**Example**:


```javascript
{
  "carId": 0,
  "endTime": "",
  "pageNum": 0,
  "pageSize": 0,
  "startTime": "",
  "timeOffset": 0
}
```


**Params**:


| name | description | in    | require | type | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|drivingReportDto|drivingReportDto|body|true|DrivingPageReportDto|DrivingPageReportDto|
|&emsp;&emsp;carId|||false|integer(int32)||
|&emsp;&emsp;endTime|End Time(yyyy-MM-dd HH:mm:ss)||false|string||
|&emsp;&emsp;pageNum|||false|integer(int32)||
|&emsp;&emsp;pageSize|||false|integer(int32)||
|&emsp;&emsp;startTime|Start time(yyyy-MM-dd HH:mm:ss)||false|string||
|&emsp;&emsp;timeOffset|Standard time difference (seconds)||false|integer(int32)||


**Status**:


| code | description | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResult«CommonPage«drivingReport对象»»_1|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**Response Params**:


| name | description | type | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int64)|integer(int64)|
|data||CommonPage«drivingReport对象»_1|CommonPage«drivingReport对象»_1|
|&emsp;&emsp;list|drivingReport数据表|array|drivingReport对象_1|
|&emsp;&emsp;&emsp;&emsp;avgSpeed||number||
|&emsp;&emsp;&emsp;&emsp;carId||integer||
|&emsp;&emsp;&emsp;&emsp;driverName||string||
|&emsp;&emsp;&emsp;&emsp;driverRFID||string||
|&emsp;&emsp;&emsp;&emsp;durationSeconds||integer||
|&emsp;&emsp;&emsp;&emsp;endDatetime||integer||
|&emsp;&emsp;&emsp;&emsp;endFuelLiters||integer||
|&emsp;&emsp;&emsp;&emsp;endLat||number||
|&emsp;&emsp;&emsp;&emsp;endLng||number||
|&emsp;&emsp;&emsp;&emsp;endMileage||integer||
|&emsp;&emsp;&emsp;&emsp;endTime||string||
|&emsp;&emsp;&emsp;&emsp;gpsDataCount||integer||
|&emsp;&emsp;&emsp;&emsp;maxSpeed||number||
|&emsp;&emsp;&emsp;&emsp;reportType||integer||
|&emsp;&emsp;&emsp;&emsp;startDatetime||integer||
|&emsp;&emsp;&emsp;&emsp;startFuelLiters||integer||
|&emsp;&emsp;&emsp;&emsp;startLat||number||
|&emsp;&emsp;&emsp;&emsp;startLng||number||
|&emsp;&emsp;&emsp;&emsp;startMileage||integer||
|&emsp;&emsp;&emsp;&emsp;startTime||string||
|&emsp;&emsp;pageNum||integer(int32)||
|&emsp;&emsp;pageSize||integer(int32)||
|&emsp;&emsp;total||integer(int64)||
|&emsp;&emsp;totalPage||integer(int32)||
|message||string||


**Response Example**:
```javascript
{
	"code": 0,
	"data": {
		"list": [
			{
				"avgSpeed": 0,
				"carId": 0,
				"driverName": "",
				"driverRFID": "",
				"durationSeconds": 0,
				"endDatetime": 0,
				"endFuelLiters": 0,
				"endLat": 0,
				"endLng": 0,
				"endMileage": 0,
				"endTime": "",
				"gpsDataCount": 0,
				"maxSpeed": 0,
				"reportType": 0,
				"startDatetime": 0,
				"startFuelLiters": 0,
				"startLat": 0,
				"startLng": 0,
				"startMileage": 0,
				"startTime": ""
			}
		],
		"pageNum": 0,
		"pageSize": 0,
		"total": 0,
		"totalPage": 0
	},
	"message": ""
}
```


## Parking report


**url**:`/report/page/driving_trip_report`


**method**:`POST`


**produces**:`application/json`


**consumes**:`*/*`


**Note**:


**Example**:


```javascript
{
  "carId": 0,
  "endTime": "",
  "pageNum": 0,
  "pageSize": 0,
  "startTime": "",
  "timeOffset": 0
}
```


**Params**:


| name | description | in    | require | type | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|drivingReportDto|drivingReportDto|body|true|DrivingPageReportDto|DrivingPageReportDto|
|&emsp;&emsp;carId|||false|integer(int32)||
|&emsp;&emsp;endTime|End Time(yyyy-MM-dd HH:mm:ss)||false|string||
|&emsp;&emsp;pageNum|||false|integer(int32)||
|&emsp;&emsp;pageSize|||false|integer(int32)||
|&emsp;&emsp;startTime|Start time(yyyy-MM-dd HH:mm:ss)||false|string||
|&emsp;&emsp;timeOffset|Standard time difference (seconds)||false|integer(int32)||


**Status**:


| code | description | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResult«CommonPage«drivingReport对象»»_1|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**Response Params**:


| name | description | type | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int64)|integer(int64)|
|data||CommonPage«drivingReport对象»_1|CommonPage«drivingReport对象»_1|
|&emsp;&emsp;list|drivingReport数据表|array|drivingReport对象_1|
|&emsp;&emsp;&emsp;&emsp;avgSpeed||number||
|&emsp;&emsp;&emsp;&emsp;carId||integer||
|&emsp;&emsp;&emsp;&emsp;driverName||string||
|&emsp;&emsp;&emsp;&emsp;driverRFID||string||
|&emsp;&emsp;&emsp;&emsp;durationSeconds||integer||
|&emsp;&emsp;&emsp;&emsp;endDatetime||integer||
|&emsp;&emsp;&emsp;&emsp;endFuelLiters||integer||
|&emsp;&emsp;&emsp;&emsp;endLat||number||
|&emsp;&emsp;&emsp;&emsp;endLng||number||
|&emsp;&emsp;&emsp;&emsp;endMileage||integer||
|&emsp;&emsp;&emsp;&emsp;endTime||string||
|&emsp;&emsp;&emsp;&emsp;gpsDataCount||integer||
|&emsp;&emsp;&emsp;&emsp;maxSpeed||number||
|&emsp;&emsp;&emsp;&emsp;reportType||integer||
|&emsp;&emsp;&emsp;&emsp;startDatetime||integer||
|&emsp;&emsp;&emsp;&emsp;startFuelLiters||integer||
|&emsp;&emsp;&emsp;&emsp;startLat||number||
|&emsp;&emsp;&emsp;&emsp;startLng||number||
|&emsp;&emsp;&emsp;&emsp;startMileage||integer||
|&emsp;&emsp;&emsp;&emsp;startTime||string||
|&emsp;&emsp;pageNum||integer(int32)||
|&emsp;&emsp;pageSize||integer(int32)||
|&emsp;&emsp;total||integer(int64)||
|&emsp;&emsp;totalPage||integer(int32)||
|message||string||


**Response Example**:
```javascript
{
	"code": 0,
	"data": {
		"list": [
			{
				"avgSpeed": 0,
				"carId": 0,
				"driverName": "",
				"driverRFID": "",
				"durationSeconds": 0,
				"endDatetime": 0,
				"endFuelLiters": 0,
				"endLat": 0,
				"endLng": 0,
				"endMileage": 0,
				"endTime": "",
				"gpsDataCount": 0,
				"maxSpeed": 0,
				"reportType": 0,
				"startDatetime": 0,
				"startFuelLiters": 0,
				"startLat": 0,
				"startLng": 0,
				"startMileage": 0,
				"startTime": ""
			}
		],
		"pageNum": 0,
		"pageSize": 0,
		"total": 0,
		"totalPage": 0
	},
	"message": ""
}
```


# api_time_management


## updateDayMileage


**url**:`/time/update_day_mileage`


**method**:`POST`


**produces**:`application/json`


**consumes**:`*/*`


**Note**:


**Example**:


```javascript
{
  "endTime": "",
  "startTime": "",
  "timeOffset": 0
}
```


**Params**:


| name | description | in    | require | type | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|dataTime|dataTime|body|true|DataTime|DataTime|
|&emsp;&emsp;endTime|End Time(yyyy-MM-dd HH:mm:ss)||false|string||
|&emsp;&emsp;startTime|Start time(yyyy-MM-dd HH:mm:ss)||false|string||
|&emsp;&emsp;timeOffset|Standard time difference (seconds)||false|integer(int32)||


**Status**:


| code | description | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResult|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**Response Params**:


| name | description | type | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int64)|integer(int64)|
|data||object||
|message||string||


**Response Example**:
```javascript
{
	"code": 0,
	"data": {},
	"message": ""
}
```