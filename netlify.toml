[build]
  # Build command to run
  command = "bun run build:client"
  
  # Directory to publish (the built client)
  publish = "client/dist"
  
  # Base directory for the build
  base = "."

[build.environment]
  # Use Node.js 18 for compatibility
  NODE_VERSION = "18"
  
  # Install Bun
  BUN_VERSION = "latest"

# SPA routing - redirect all routes to index.html
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
  force = false
  conditions = {Role = ["admin", "user"]}

# API routes (if you have any backend API calls)
[[redirects]]
  from = "/api/*"
  to = "/.netlify/functions/:splat"
  status = 200

# Handle specific routes for your app
[[redirects]]
  from = "/superadmin/*"
  to = "/index.html"
  status = 200

[[redirects]]
  from = "/admin/*"
  to = "/index.html"
  status = 200

[[redirects]]
  from = "/user/*"
  to = "/index.html"
  status = 200

# Handle assets and static files
[[redirects]]
  from = "/assets/*"
  to = "/assets/:splat"
  status = 200

# Fallback for any other routes
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Headers for security and performance
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"

[[headers]]
  for = "/static/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"
