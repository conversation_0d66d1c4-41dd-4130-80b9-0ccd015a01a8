# 🧪 GPS API Implementation Testing Guide

## 📋 Cara Cek Implementasi GPS API

### **1. Prerequisites**
- ✅ Server sudah running: `bun dev`
- ✅ GPS API credentials sudah dikonfigurasi di `.env`
- ✅ Database sudah ter-setup

### **2. Test Methods**

#### **Method 1: Browser Test (Recommended)**
```bash
# Buka browser dan akses:
http://localhost:3000/api/beacukai/test-gps-api
```

**Expected Response:**
```json
{
  "success": true,
  "message": "GPS API Implementation Test Results",
  "data": {
    "connection": true,
    "devices": [...],
    "positions": [...],
    "cronJobs": [...],
    "trackingLogs": [...]
  }
}
```

#### **Method 2: Command Line Test**
```bash
# Build project first
bun run build

# Run test script
node test-gps-api.js
```

### **3. Workflow Tracking Data**

#### **📊 Kapan Bisa Melihat Tracking Data?**

**Step 1: Setup E-Seal** ✅
```
1. <PERSON>uka: http://localhost:5173/superadmin/setup-eseal
2. Isi data E-Seal (Step 1-3)
3. Validasi AJU (Step 4) - Pastikan AJU valid
4. Review & Submit (Step 5)
```

**Step 2: Start Tracking** 🚀
```
1. E-Seal otomatis ter-register di Beacukai
2. GPS tracking otomatis aktif
3. Cron job mulai update posisi setiap 5 menit
```

**Step 3: View Tracking Data** 📍
```
1. Buka: http://localhost:5173/superadmin/tracking-data
2. Pilih E-Seal yang sudah di-setup
3. Lihat real-time position updates
```

### **4. Test Checklist**

#### **✅ GPS API Connection Test**
```bash
curl http://localhost:3000/api/beacukai/test-gps-api
```

**Expected:**
- `connection: true` - GPS API bisa diakses
- `devices: [...]` - Ada device di GPS system
- `positions: [...]` - Ada position data untuk E-Seal

#### **✅ Cron Job Test**
```bash
# Cek cron job logs di database
SELECT * FROM "CronJobLog" WHERE "jobName" = 'position-update' ORDER BY "createdAt" DESC LIMIT 5;
```

**Expected:**
- Ada log setiap 5 menit
- `status: 'completed'`
- `devicesProcessed > 0`

#### **✅ Tracking Logs Test**
```bash
# Cek tracking logs
SELECT * FROM "TrackingLog" ORDER BY "createdAt" DESC LIMIT 5;
```

**Expected:**
- Ada tracking data untuk E-Seal
- `latitude` dan `longitude` terisi
- `beacukaiStatus: 'success'`

### **5. Troubleshooting**

#### **❌ GPS API Connection Failed**
```bash
# Cek credentials
grep -i gps .env

# Expected:
GPS_API_BASE_URL=http://smartelock.net:8088
GPS_API_USERNAME=Virtus
GPS_API_PASSWORD=Virtus@12345
```

#### **❌ No Devices Found**
- GPS system mungkin kosong
- Credentials mungkin salah
- Network connectivity issue

#### **❌ No Tracking Data**
- E-Seal belum di-setup
- AJU belum divalidasi
- Cron job belum running

#### **❌ Cron Job Not Running**
```bash
# Cek server logs
tail -f server.log | grep "position-update"

# Expected: Log setiap 5 menit
⏰ Position update cron job triggered
🔄 Starting position update cron job...
✅ Position update job completed
```

### **6. Real-time Monitoring**

#### **📊 Dashboard Monitoring**
```
URL: http://localhost:5173/superadmin/tracking-data

Features:
- Real-time map view
- Live position updates
- Device status monitoring
- Battery level tracking
- Speed monitoring
```

#### **📈 Data Flow**
```
GPS Device → GPS API → Local Database → Beacukai API
     ↓           ↓           ↓              ↓
  Position   Smartelock   Tracking     Position
   Data      GPS System    Logs        Update
```

### **7. Expected Timeline**

#### **⏱️ Timeline Tracking Activation**
```
T+0:   Setup E-Seal & validasi AJU
T+1:   E-Seal ter-register di Beacukai
T+5:   Cron job pertama update posisi
T+10:  Tracking data mulai muncul di dashboard
T+15:  Real-time tracking fully active
```

### **8. Success Indicators**

#### **✅ GPS API Working**
- Connection test: `true`
- Devices found: `> 0`
- Position data: Available

#### **✅ Tracking Active**
- Cron job logs: Every 5 minutes
- Tracking logs: Recent entries
- Beacukai status: `success`

#### **✅ Real-time Updates**
- Dashboard shows live data
- Position updates every 5 minutes
- Battery/speed data available

### **9. Manual Test Commands**

#### **🔧 Test GPS Service Directly**
```javascript
// Di browser console atau Node.js
const response = await fetch('http://localhost:3000/api/beacukai/test-gps-api');
const data = await response.json();
console.log(data);
```

#### **🔧 Test Cron Job Manually**
```bash
# Trigger cron job manually
curl -X POST http://localhost:3000/api/cron/trigger-position-update
```

#### **🔧 Check Database Status**
```sql
-- Check E-Seal status
SELECT * FROM "ESeal" WHERE "status" = 'ACTIVE';

-- Check tracking logs
SELECT COUNT(*) FROM "TrackingLog" WHERE "createdAt" > NOW() - INTERVAL '1 hour';

-- Check cron jobs
SELECT * FROM "CronJobLog" WHERE "jobName" = 'position-update' ORDER BY "createdAt" DESC LIMIT 1;
```

---

## 🎯 Summary

**GPS API sudah terimplementasi dengan baik jika:**

1. ✅ **Connection test berhasil**
2. ✅ **Devices ditemukan di GPS system**
3. ✅ **Position data tersedia**
4. ✅ **Cron job running setiap 5 menit**
5. ✅ **Tracking logs ter-generate**
6. ✅ **Dashboard menampilkan real-time data**

**Tracking data akan muncul setelah:**
- E-Seal di-setup dengan AJU valid
- Start tracking diaktifkan
- GPS device terhubung dan mengirim data
- Cron job mulai update posisi (setiap 5 menit) 