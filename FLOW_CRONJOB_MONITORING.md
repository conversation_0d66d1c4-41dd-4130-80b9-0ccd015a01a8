# Flow Cron Job dan Monitoring E-Seal System

## 🔄 Flow Cron Job (<PERSON><PERSON><PERSON> Mentor)

### Kapan Cron Job Mulai?
Cron job **otomatis dimulai** saat server dijalankan:

1. **Server Start** → `server/src/index.ts` line 176-180
   ```typescript
   // Initialize cron jobs
   if (process.env.NODE_ENV !== "test") {
     console.log('🚀 Initializing cron jobs...');
     startAllCronJobs();
   }
   ```

2. **Cron Job Scheduler** → `server/src/jobs/positionUpdateJob.ts`
   ```typescript
   // Run every 5 minutes: '*/5 * * * *'
   const schedule = process.env.POSITION_UPDATE_CRON || '*/5 * * * *';
   ```

### Flow Detail Cron Job (Setiap 5 Menit):

```
┌─────────────────────────────────────────────────────────┐
│                 CRON JOB FLOW                           │
└─────────────────────────────────────────────────────────┘

1. ⏰ Trigger (Setiap 5 menit)
   └── CronJobService.runPositionUpdateJob()

2. 🔍 Cari Active Tracking Sessions
   └── SELECT * FROM tracking_session WHERE sessionStatus = 'ACTIVE'

3. 📍 Untuk Setiap Active Session:
   ├── 🌐 Ambil GPS Data dari API (smartelock.net:8088)
   │   ├── Login ke GPS API
   │   ├── Fetch device position by IMEI
   │   └── Get lat, lng, battery, speed, etc.
   │
   ├── 💾 Simpan ke Database
   │   ├── INSERT INTO tracking_log
   │   └── UPDATE device_status
   │
   └── 📤 Kirim ke Beacukai API
       ├── Session-based authentication
       ├── POST /eseal/update-position
       └── Log response

4. 📊 Update Job Statistics
   └── INSERT INTO cron_job_log (status, duration, devices_processed)
```

## 📊 Monitoring & Logging

### 1. Cron Job Logs
**Lokasi**: Database table `cron_job_log`
**Akses**: 
- API: `GET /api/tracking/job-history`
- Dashboard: Akan ditambahkan ke tracking dashboard

**Data yang Dicatat**:
```sql
- jobName: 'position-update'
- status: 'SUCCESS' | 'ERROR' | 'RUNNING'
- message: Detail hasil eksekusi
- executionTime: Waktu eksekusi dalam ms
- devicesProcessed: Jumlah device yang diproses
- errorsCount: Jumlah error
- jobData: Detail error dan statistik
```

### 2. Tracking Logs
**Lokasi**: Database table `tracking_log`
**Data GPS yang Disimpan**:
```sql
- latitude, longitude: Koordinat GPS
- address: Alamat (reverse geocoding)
- speed: Kecepatan device
- battery: Level baterai
- gpsDeviceId: ID device dari GPS API
- gpsTime: Timestamp dari GPS
- online: Status online GPS
- apiResponse: Response dari Beacukai API
- beacukaiStatus: Status pengiriman ke Beacukai
```

### 3. Device Status
**Lokasi**: Database table `device_status`
**Real-time Status**:
```sql
- currentStatus: 'ACTIVE' | 'INACTIVE' | 'ERROR'
- lastKnownLat, lastKnownLng: Posisi terakhir
- gpsOnline: Status koneksi GPS
- lastGpsUpdate: Waktu update GPS terakhir
- batteryLevel: Level baterai terkini
- registeredWithBeacukai: Status registrasi Beacukai
```

## 🖥️ Dashboard Monitoring

### Lokasi Dashboard:
1. **Tracking Dashboard**: `client/src/pages/superadmin/TrackingData/TrackingDashboard.tsx`
   - URL: `/tracking-dashboard` (perlu ditambahkan ke routing)
   - Menampilkan: Device status, active sessions, job history

2. **Detail E-Seal**: `client/src/pages/superadmin/DetailESeal.tsx`
   - Menampilkan: Detail device individual, tracking history
   - **PERLU DIUPDATE** untuk menampilkan GPS data dan cron job status

3. **Logs Page**: `client/src/pages/superadmin/Logs/Logs.tsx`
   - Menampilkan: API logs, request/response history
   - **PERLU DIUPDATE** untuk menampilkan cron job logs

### Dashboard Features:
- ✅ Real-time device status
- ✅ GPS connectivity monitoring
- ✅ Active tracking sessions
- ✅ Manual position update trigger
- ✅ Cron job execution history
- ⏳ **TODO**: Integration dengan DetailESeal.tsx
- ⏳ **TODO**: Integration dengan Logs.tsx

## 🔧 Manual Controls

### API Endpoints untuk Manual Control:
```bash
# Trigger manual position update
POST /api/tracking/trigger-position-update

# Get cron job history
GET /api/tracking/job-history?limit=50

# Get active tracking sessions
GET /api/tracking/active

# Test GPS connectivity
POST /api/tracking/test-gps/:esealId

# Test Beacukai session
POST /api/beacukai/test-connection
```

## 🚨 Error Handling & Alerts

### Error Scenarios:
1. **GPS API Down**: Retry 3x dengan exponential backoff
2. **Beacukai API Error**: Retry 3x, log error, continue dengan device lain
3. **Database Error**: Log error, stop job execution
4. **Network Timeout**: 10s untuk GPS, 30s untuk Beacukai

### Error Monitoring:
- Semua error dicatat di `cron_job_log`
- Device-specific error di `tracking_log.apiResponse`
- Real-time status di `device_status`

## 📈 Performance Metrics

### Metrics yang Ditrack:
- **Execution Time**: Waktu total eksekusi cron job
- **Success Rate**: Persentase berhasil update position
- **Device Processing Rate**: Jumlah device per menit
- **API Response Time**: Waktu response GPS dan Beacukai API
- **Error Rate**: Persentase error per job execution

### Optimization:
- Parallel processing untuk multiple devices
- Connection pooling untuk database
- Retry mechanism dengan backoff
- Timeout protection
- Automatic cleanup old logs (30 hari)

## 🔍 Troubleshooting

### Common Issues:
1. **Cron Job Tidak Jalan**:
   - Cek server logs saat startup
   - Pastikan `NODE_ENV !== "test"`
   - Verify cron schedule format

2. **GPS Data Tidak Update**:
   - Cek GPS API credentials di .env
   - Test connectivity: `POST /api/tracking/test-gps/:esealId`
   - Verify device IMEI mapping

3. **Beacukai API Error**:
   - Cek session authentication
   - Test connection: `POST /api/beacukai/test-connection`
   - Verify API endpoints dan payload format

### Debug Commands:
```bash
# Manual trigger untuk testing
curl -X POST http://localhost:3000/api/tracking/trigger-position-update

# Cek job history
curl http://localhost:3000/api/tracking/job-history

# Test GPS connectivity
curl -X POST http://localhost:3000/api/tracking/test-gps/DEVICE_ID

# Test Beacukai session
curl -X POST http://localhost:3000/api/beacukai/test-connection
```

## 📋 Next Steps untuk Integration

### Yang Perlu Ditambahkan:

1. **Update DetailESeal.tsx**:
   - Tampilkan real-time GPS data
   - Show tracking session status
   - Display cron job logs untuk device ini

2. **Update Logs.tsx**:
   - Tab baru untuk Cron Job Logs
   - Filter by job type dan status
   - Real-time log streaming

3. **Add Routing**:
   - Tambahkan route `/tracking-dashboard`
   - Link dari menu admin

4. **Notifications**:
   - Alert jika cron job gagal
   - Notification jika device offline
   - Email/SMS alerts untuk critical errors

5. **Performance Dashboard**:
   - Charts untuk success rate
   - Response time metrics
   - Device health monitoring
