#!/bin/bash

# Build script untuk Docker deployment
# Pastikan semua dependencies dan build artifacts sudah siap

echo "🚀 Starting Docker build process..."

# 1. Clean previous builds
echo "🧹 Cleaning previous builds..."
find . -type f -name '*.js' -not -path './node_modules/*' -delete

# 2. Install dependencies
echo "📦 Installing dependencies..."
bun install

# 3. Generate Prisma client
echo "🔧 Generating Prisma client..."
cd server && bunx prisma generate && cd ..

# 4. Build application
echo "🏗️ Building application..."
bun run build:single

# 5. Build Docker image
echo "🐳 Building Docker image..."
docker build --platform linux/amd64 -t eseal-monitor:latest .

# 6. Tag for deployment
echo "🏷️ Tagging image..."
docker tag eseal-monitor:latest eseal-monitor:$(date +%Y%m%d-%H%M%S)

echo "✅ Build completed successfully!"
echo "📋 Available images:"
docker images | grep eseal-monitor
