 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 1 / 89
GL600 Protocol Manual
V2.6
Revision Date: 2022-08-14

Shenzhen Mobicom Telematics Co.,Ltd Tel: + 86 755 8450 9394
Room 401,Building 2,Anxu Bussiness Park,No.35-1, Email: <EMAIL>
Xiangyin Road,Longgang District,Shenzhen,China Website:http://www.mobicomtracking.com
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 2 / 89
 Table of Contents
GL600 Protocol Manual.............................................................................................................................................1
Table of Contents............................................................................................................................................................2
1 Preface........................................................................................................................................................................4
2 Terms and Abbreviations..............................................................................................................................................4
3 Product Working logic ..................................................................................................................................................4
3.1 Sleep state.........................................................................................................................................................4
3.2 Awakening state ................................................................................................................................................4
3.3 Wake up source.................................................................................................................................................5
3.4 Blind zone data..................................................................................................................................................6
4 Protocol Basis..............................................................................................................................................................6
4.1 Communication mode........................................................................................................................................6
4.2 Transmission Rules.............................................................................................................................................6
5 Command Syntax(GRPS/SMS).......................................................................................................................................6
5.1 Read/Write Command Syntax.............................................................................................................................6
5.2 Command Reponse Syntax.................................................................................................................................7
6 Protocol Integration Guide ...........................................................................................................................................8
6.1 Message Interaction diagram .............................................................................................................................8
7 Device Send data to GPS Platform.................................................................................................................................9
7.1 Position and Alert Data ......................................................................................................................................9
7.2 P45 - Lock and Unlock Report...........................................................................................................................12
7.3 Command Response ........................................................................................................................................15
8 Device Send short message to Authorized Phone numbers ..........................................................................................15
8.1 Position data short message Format.................................................................................................................15
8.2 Alert data short message Format......................................................................................................................16
8.3 Command Response short message Format......................................................................................................18
9 GPS Platform Send Commands to Device ....................................................................................................................18
Command Word List(ASCII)....................................................................................................................................18
P00 - Set working mode.........................................................................................................................................20
P01 - Query firmware version ................................................................................................................................21
P02 - Query Current Position..................................................................................................................................22
P04 - Set/Query Position Data Reporting Time interval and Timing wake up interval.................................................23
P06 - Set/Query SIM1 and SIM2 Communication Parameters ..................................................................................24
P06 - Set/Query SIM1’s IP Port and GPRS/Cellular network Parameters.....................................................24
P06 - Set/Query SIM2’s IP Port and GPRS/Cellular network Parameters.....................................................26
P09 - Turn On/Off GPS and GSM indicator ..............................................................................................................28
P10 - Set/Query the time difference of GPRS position data and SMS alert................................................................29
P11 - Set/Query Authorized Phone Numbers..........................................................................................................31
P12 - Enable or Disable the device to send SMS alerts to the specified Authorized Phone number ............................32
P13 - Restore Factory setting..................................................................................................................................34
P14 - Read device’s IMEI number ...........................................................................................................................35
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 3 / 89
P15 - Reboot the device remotely ..........................................................................................................................36
P22 - Time Synchronization....................................................................................................................................37
P24 - Set/Query Geo-fence name and enable/disable one of fence ID......................................................................38
P29 - Set/Query the Fence Nodes of The Polygon Geo-fence ...................................................................................40
P30 - Delete Fence Node info and Geo-fence name for a Fence ID ...........................................................................44
P31 - Finished Fence Node information Setting.......................................................................................................45
P32 - Force the device to go to sleep......................................................................................................................46
P35 - Acknowledge Command to receive Alert and Position data.............................................................................47
P36 - Set/Query Vibration Sensitivity Coefficient threshold of Vibration alert and repeated interval..........................48
P37 - Set/Query Vibration Sensitivity Coefficient of Motion state detection .............................................................50
P38 - Set/Query Interval of Unlocking alert.............................................................................................................51
P39 - Set/Query Working time after waking up .......................................................................................................52
P40 - Set/Query Alert Switch..................................................................................................................................53
P41 - Authorized RFID key Management.................................................................................................................56
P41 - Query Authorized RFID key ....................................................................................................................56
P41 - Register(Add) Authorized RFID Key.........................................................................................................57
P41 - Delete Specified Authorized RFID key .....................................................................................................59
P41 - Delete All Authorized RFID keys in Device...............................................................................................60
P42 - Batch-Add Unlocking Authorized RFID ...........................................................................................................61
P43 - Unlock the device by Password......................................................................................................................61
P44 - Change the Unlock Password.........................................................................................................................63
P46 - Acknowledge Command to receive Lock or Unlock Report..............................................................................64
P50 - Enable/Disable the Power Switch on Device Mainboard .................................................................................65
P58 - Set/Query RFID unlocking related to geo-fence ..............................................................................................66
P97 - Set/Query Data Acknowledgement Mechanism .............................................................................................67
P99 - Firmware Upgrade over the air......................................................................................................................69
P100 - Query device real-time status and GSM module version ...............................................................................70
P106 - Set/Query Can Unlock In The polygon Geo-fence..........................................................................................72
P108 - Set/Query Can Unlock In The POI(Point of Interest) ......................................................................................74
P110 - Set/Query G-Sensor filter function...............................................................................................................76
P111 - Set/Query Working In Lock/Unlock Mode Reporting Data Time Interval ........................................................78
P112 - Enable/Disable Lock Rope Cut Reminder(Blink & Beep).................................................................................79
P113 - Enable/Disable Blind Area Data Packaging....................................................................................................80
P114 - Enable/Disable Base Station Positioning Function.........................................................................................81
P122 - Enable/Disable Bluetooth Function..............................................................................................................82
P123 - Change the Bluetooth Encryption Key ..........................................................................................................82
P125 – Extract Data From Micro USB/Bluetooth (need firmare version ‘GL600E_........’) ............................................83
P126 - Clear blind spot data...................................................................................................................................87
P127 - Extended power saving solution: turn off GPS module in idle time ................................................................88
P128 – Query Bluetooth Mac Address & Name & Version........................................................................................89
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 4 / 89
1 Preface
This document is intended primarily for software engineers and system administrators. Readers must have a basic knowledge of the
computer. Due to continuous optimization and improvement of product features, it is possible that the protocol documents you read
are not exactly the same as product currently used. Please contact with MOBICOM TELEMATICAS sales for the latest GL600
Protocol documentation.
2 Terms and Abbreviations
Name Description
GPS Platform Asset Management GPS platform software, hereafter called GPS platform
Device GL600 intelligent electronic lock, acts as client
IMEI International Mobile Equipment Identity, each 2G/3G/4G module has its unique IMEI.
e.g. IMEI: 86029**********
Unit ID The 10-digit numbers. e.g. 6024010099,8024059878
GPRS General Packet Radio Service
APN Access Point Name
TCP Transmission Control Protocol
SMS Short Message service
3 Product Working logic
3.1 Sleep state
The device will switch from the awake state to the sleep state. When sleeping, the GPS and 2G/3G modules will be turned off. The
device will sleep for 30 minutes by default (this time can be configured) and wake up periodically. During this period, it can be
externally Wake up the source.
3.2 Awakening state
In order to monitor the wake-up source in real time and communicate with the GPS platform, the device can be awakened by the
wake-up source to change from the sleep state to the awake state. At this time, if the vibration, RFID card reading ,Back Cover
opened and lock rope insertion and unplugging external wake-up source are detected again, the device will update the wake-up
work. The time is 10 minutes (this time is configurable), otherwise the device will work for up to 10 minutes (which is configurable) to 
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 5 / 89
go to sleep.
3.3 Wake up source
The device will wake up from the external interrupt wake-up source and the RTC wake-up source(internal interrupt), and the external
interrupt wake-up source, hereinafter referred to as “external wake-up source”:
No. Wake up source After wake up Remark
1 Vibration
When the device detects the vibration amplitude is greater than the preset
vibration sensitivity coefficient, it wakes up and continues to work for 10
minutes (this time is configurable). If the valid external wake-up source is
not captured during the period, it sleeps; otherwise, it starts timing when
the external wake-up source is detected last time. Work 10 minutes to go
to sleep.
external wake up
source
2 RFID Key Reading
The device is woken up when detecting the RFID card reading, and
continues to work for 10 minutes (this time is configurable). If the valid
external wake-up source is not captured during the period, it sleeps;
otherwise, the time is started when the external wake-up source is
detected last time. Work 10 minutes to go to sleep.
external wake up
source
3 Back Cover Opened
The device is woken up when detecting Back Cover Opened, and
continues to work for 10 minutes (this time is configurable). If the valid
external wake-up source is not captured during the period, it sleeps;
otherwise, the time is started when the external wake-up source is
detected last time. Work 10 minutes to go to sleep.
external wake up
source
4
Lock rope inserted
and unplugging
The device is woken up when detecting the lock rope is inserted or
unplugging, and continues to work for 10 minutes (this Time
configurable),If the valid external wake-up source is not captured during
the period, it sleeps; otherwise, it starts timing when the external wake-up
source is detected last time. Work 10 minutes to go to sleep.
external wake up
source
5
Sleep and timing
wake up
The device starts timing when it enters the sleep time. If no external
wake-up source is generated during this period, it wakes up after 30
minutes, uploads a positioning data and delays 30 seconds to enter the
sleep. Otherwise, it goes to the external wake-up source to trigger the
wake-up work for 10 minutes(this time is configurable).
RTC wake up
source
6 SMS
When the device receives any SMS, it wakes up and continues to work for
10 minutes (this time is configurable). If the valid external wake-up source
is not captured during the period, it sleeps; otherwise, it starts timing when
the external wake-up source is detected last time. Work 10 minutes to go
to sleep.
external wake up
source
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 6 / 89
3.4 Blind zone data
If The device enters the mobile network blind zone, the device will save the current position data and alert data (at least 28000) to
Flash, and these data will be uploaded to GPS platform in the first-in first-out order after the device is connected to the GPRS
network. The device saves lock and unlock report at least 2400, these data will be uploaded in the first-in first-out order after the
GPRS network is restored.
4 Protocol Basis
4.1 Communication mode
This communication protocol adopts TCP, GPS platform acts as server; Device acts as client. Usually use TCP protocol as the main
communication way.
4.2 Transmission Rules
Protocol adopts Big-endian network byte sequence to transmit word and double word. Position/Alert data ,Lock /Unlock report are
transmitting according First in First out (FIFO).
5 Command Syntax(GRPS/SMS)
5.1 Read/Write Command Syntax
 (<Command Word>,<Parameter 1>,…<Parameter N>)
Note:
 The command uses ‘(’ as the header, ‘’)” as the end of the package. When editing the command, ignore the ‘<’ and ‘>’ symbol.
Example: (P04,1,60,30)
Field Name length(byte) Example(ASCII)
Header 1 (
Command Word 3 P04
Comma as separator 1 , 
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 7 / 89
Each parameter takes a comma as a separator
<Parameters…>
N 60,30
End 1 )
5.2 Command Reponse Syntax
(<Unit ID>,<Command Word>,<Parameter 1>,…<Parameter N>)
Note:
 The command response uses ‘(’ as the header, ‘’)” as the end of the package. Please ignore the ‘<’ and ‘>’ symbol when decode
the command response.
 Example: (**********,P43,1,0)
Field Name length(byte) Example(ASCII)
Header 1 (
<Unit ID>
Last 10 digit numbers of IMEI number
10 **********
Comma as separator 1 ,
Command Word 3 P43
Comma as separator 1 ,
<Parameters…>
Each parameter takes a comma as a separator
N 1,0
End 1 )
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 8 / 89
6 Protocol Integration Guide
After the device is configured with the correct host IP address and TCP port, the device will send the position data to the GPS
platform automatically when it is in the awake state. The GPS platform refers to the following diagram to respond to the relevant
binary position data .All position data and alert data need to be parsed in binary data, with 0x24(HEX) as the data header, and then
intercept the data according to the data length; All lock and unlock reports and commands interactions need to convert the binary
data into ASCII format and then parse the data ,with 0x28(HEX) as the data header, 0x29(HEX) as the end of the packet.
6.1 Message Interaction diagram
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 9 / 89
7 Device Send data to GPS Platform
7.1 Position and Alert Data
Position and Alert Raw data Example (HEX):
24**********0111002718031919195822424550114158888E15A40000F124080000000000F00F110A24991900000DF0C7
For easy reading, separate the fields with underscores:
24_**********_01_1_1_0027_180319_191958_22424550_114158888_E_15_A4_0000F124_08_00000000_00F0_0F_110A249
9_19_00_000DF0_C7
No. Field Name
Data
Example(HEX)
Length
(Byte)
Description
1 Protocol header 24 1 0x24(HEX) ,Convert to ASCII format ,it’s ‘$’ character
2 Unit ID ********** 5 Last 10 digit numbers of IMEI
3 Protocol version 01 1 01 indicates protocol version
4 Device type 1 0.5 1 indicates GL600.
5 Data type 1 0.5
1 indicates Real time data
2 indicates Alert data
3 indicates Blind zone data
6 Data length 0034 2 Data content length, total 39 bytes. from No.7 to No.27
7 Date 180319 3 Day Month Year. It’s Mar. 18
th , 2019
8 Time 191958 3 Hour-Minute-Second, UTC time, 19:19:58
9 Latitude 22424550 4
DDMM.MMMM format, the latitude conversion method as below:
22424550/10000=2242.4550
2242.4550 DDMM.MMMM
22+42.4550/60=22+ 0.707583=22.707583o
10 Longitude 114158888 4.5
DDDMM.MMMM format, the longitude conversion method as below:
114158888/10000=11415.8888
11415.8888 DDDMM.MMMM
114+15.8888/60=114+ 0.264813=114.264813o
11
Latitude/Longitude
Direction
E 0.5
Convert this’ Latitude/Longitude Direction’ to Binary system data.
1110
Bit3 to Bit0 from left to right
Bit indicator Description Example
(Binary system)
Bit3 Fixed value 1 1
Bit2 Longitude Direction
1 East Longitude
0 West Longitude
1
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 10 / 89
Bit1 Latitude direction
1 North Latitude
0 South Latitude
1
Bit0 GPS Validity
1 GPS valid
0 GPS invalid
0
12 Speed 1A 1
1A(HEX) = 26(Dec), current GPS speed is 26 knots, Convert to
kilometer/hour 26 * 1.852 = 48.152 KM/H
13 Direction A4 1
A4(HEX) = 164(Dec), multiply by 2=328(Dec), direction is 328
o
North is 0o
,Clockwise counting
The unit in degree. range:0~359 degree
14 GPS Odometer 0000F124 4
Unit in kilometer. Current GPS Odometer is 61732 KM.
The accumulated mileage of the device when it is awake
15
Number of
captured satellites
08 1
Captured 8 satellites
16
Vehicle ID Blinding
Key number
00000000 4
Reserved. This value is fixed to ‘00000000’.
17 Device status 00F0 2
Device’s status or Alert indicator :
0x00 0xF0
Byte2 at left side .it’s 0x00; Byte1 at right side it’s 0xF0
Convert ’Device Status’ to Binary system data.
BYTE2 BYTE1
00000000 11110000
Bit7 to Bit0 from left to right
Byte. Bit Description Example(BIN)
00F0(HEX) Byte2 Description
Byte2.BIT7 Reserved. This bit is fixed at 0 0
Byte2.BIT6 Motor fault alert
1 indicates triggered alert;
0 indicates normal.
0
Byte2.BIT5 Back Cover status:
1 indicates back cover close;
0 indicates back cover opened
0
Byte2.BIT4 Back cover Opened alert
1 indicates triggered alert ;
0 indicates normal.
0
Byte2.BIT3 Low battery alert
1 indicates triggered alert;
0 indicates normal.
0
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 11 / 89
Byte2.BIT2 Swiping unauthorized RFID key alert
1 indicates triggered alert;
0 indicates normal.
0
Byte2.BIT1 Wrong password alert
The password is continuously entered
incorrectly 5 times.
1 indicates triggered alert;
0 indicates normal.
0
Byte2.BIT0 Unlocking alert
1 indicates triggered alert;
0 indicates normal.
0
Byte1 Description
Note:
Device Locked : Byte1.BIT7 and Byte1.BIT6=1
Device Unlocked: Byte1.BIT6=0
Byte1.BIT7 Motor Lock status:
1 indicates Motor lock;
0 indicates Motor unlock.
1
Byte1.BIT6 Lock Rope status:
1 indicates Lock Rope inserted;
0 indicates Lock Rope unplugging
1
Byte1.BIT5 ACK indicator
1 indicates This data requires the GPS
platform to acknowledge it;
0 indicates This data does not require the
GPS platform to acknowledge it.
Note:
 If Byte1.BIT5=1,GPS Platform need to
send (P35) command to acknowledge this
data. Otherwise, the device will keep
sending the same data.
1
Byte1.BIT4 Vibration alert
1 indicates triggered alert;
0 indicates normal.
1
Byte1.BIT3 Lock rope tamper alert
1 indicates triggered alert;
0 indicates normal.
0
Byte1.BIT2 Exit Geo-fence alert
1 indicates triggered alert;
0 indicates normal.
0
Byte1.BIT1 Enter Geo-fence alert
1 indicates triggered alert;
0 indicates normal.
0
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 12 / 89
Byte1.BIT0 Base station positioning indicator
1 indicates Base station positioning;
0 located by GPS positioning
0
18 Battery Level 0F 1
B Remaining Battery Level.
0x0F the current battery is 15%，
0x64 means 100%
the accuracy is 5%，if the value is 0xFF,means charging
19 CELL ID and LAC 1116B37E 4
0x1116 (HEX) is CELL ID;
0xB37E(HEX) is LAC.
20 GSM signal quality 19 1
Indicates the strength of GSM signal, 19(HEX), signal value 25(DEC).
The maximum value for GSM signal strength is 31.
21
Geo-Fence alert
Fence ID
00 1
Indicates the geo-fence alert fence ID ;when no geo-fence alert, this value
is fixed to ‘00’.
22
Temperature
Battery Voltage
1C0F66 3
0x1C is temperature,0x1C = 28℃
0x0F66 is battery voltage,0x0F66 = 3.942V
23 IMEI
086460604088
8025
8
24 CELL ID 0EE0 2
CELL ID of 3G device-high 16bit
if 3G,Cell ID is 0x0EE0 0x1116 = 3808 4374
If 2G,Cell ID is 0x1116
25 MCC 02CA 2 National Code, such as Panama is 714 (DEC), 0x02CA (HEX) = 714 (DEC)
26 MNC 14 1 Operators Code, such as Movistar is 20 (DEC), 0x14(HEX) = 20(DEC)
27 Serial number C7 1 Serial number 199(DEC)，Add 1 for each data sent; reset to 0x00 after 0xFF
7.2 P45 - Lock and Unlock Report
Report Description
Note: When parsing data, you need to convert hexadecimal data to ASCII format first.
This is Lock and Unlock Report, not a command. It’s transmitted to GPS platform side automatically when Customers Swipe
the RFID keys Or Lock/Unlock the Device. The GPS platform needs to send a (P46) command to acknowledge this report,
otherwise the device will continue to send this report. In case of multiple event sources at the same time, the device will send
one by one.
Syntax
Response (<Unit ID>,P45,<Date>,<Time>,<Latitude>,<N/S Latitude indicator>,<Longitude>,<E/W Longitude
indicator>,<GPS fix indicator>,<Speed>,<Direction>,<Even Source>,<Unlock successful/failed>,<RFID
key number>,<Password True/False>,<Input Wrong Password Times>,<Report Serial
number>,<Mileage>,<Device IMEI>,<Geofence ID>)
Parameter Description
Parameters Description
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 13 / 89
<Date> Repot Date. format :DDMMYY
170219 means Feb. 17th 2019
<Time> Report Time .format: HHMMSS
162349 means 16:23:49 UTC time
<Latitude> Latitude . format: DD.DDDDDD .
The unit in degree
<N/S Latitude
indicator>
N indicates Northern latitude;
S indicates South latitude
<Longitude> Longitude. format: DDD.DDDDDD.
The unit in degree
<E/W Longitude
indicator>
E indicates East longitude;
W indicates West longitude
<GPS Validity> A indicates GPS fix;
V indicates GPS signal invalid
<Speed> GPS speed. Unit in KM/H (Kilometers per hour)
<Direction> North is 0 degree, Clockwise counting. the unit in degree. range:0~359 degree
<Even Source> 1 indicates Swipe Authorized RFID key;
2 indicates Swipe Unauthorized RFID key;
3 indicates Swipe the vehicle ID blinding RFID key;
4 indicates Unlock by Password from GPRS；
5 indicates the device is automatically locked
6 indicates Unlock by Password from Bluetooth
<Unlock
successful/failed>
1 means the RFID key or password is verified, unlocked successfully,
0 means RFID key or password is not verified, refused to unlock
Note: If <Even Source> is 2,3,5 ,this value is fixed to 0 .
<RFID key
number>
The ID card number when swiping the card.
Note: If the <Event Source> is 4 or 5, the value is 0000000000.
<Password
True/False>
If the <Event Source> is 4, this value indicates whether the password is correct. If password is correct, it
is 1, and for other types, it is fixed to 0.
<Input Wrong
Password Times>
If the <Event Source> is 4, this value indicates the number of consecutive password input errors. For
other types, it is fixed to 0.
<Report Serial
number>
The serial number of the lock and unlock report, indicating the number of times the device sent the
records.
<Mileage> Unit is KM
<Device IMEI> 15 bytes IMEI numbers
<Geofnce ID> 0
Example
Swipe Authorized RFID key
Response (**********,P45,180219,184809,22.924088,N,114.540793,E,V,12.00,60,1,1,0006182080,0,0,35,100,
869731054448626,0)
Response
Description
Content Description
********** Unit ID
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 14 / 89
P45 Command Word
180219 Feb. 18th 2019
184809 18:48:09. UTC time
22.924088 Latitude
N Northern latitude
114.540793 Longitude
E East longitude
V GPS invalid
12.00 12 KM/H
60 60
o Direction
1 Swipe Authorized RFID key
1 RFID key is verified, unlocked successfully
0006182080 RFID key number
0 Password True/False. Fixed to ‘0’
0 Input Wrong Password Times .Fixed to ‘0’
35 The serial number of the lock and unlock
report
100 100 KM
869731054448626 IMEI numbers
0 Geofence ID
Unlock by Password
Response (**********,P45,180219,184809,22.924088,N,114.540793,E,A,12.00,60,4,1,0000000000,1,0,36,100,
869731054448626,0)
Response
Description
Content Description
********** Unit ID
P45 Command Word
180219 Feb. 18th 2019
184809 18:48:09 . UTC time
22.924088 Latitude
N Northern latitude
114.540793 Longitude
E East longitude
A GPS fix
12.00 12 KM/H
60 60
o Direction
4 Unlock by Password
1 Password is verified, unlocked successfully
0000000000 RFID key number. fixed to ‘0000000000’
1 Password is correct
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 15 / 89
0 Input Wrong Password Times
36 The serial number of the lock and unlock
report
100 100 KM
869731054448626 IMEI Numbers
0 Geofence ID
Sending Report Channel
GPRS
7.3 Command Response
For the response content of all commands sent through the GPS platform, please refer to the Section:9 GPS
platform sends commands to device Response content.
8 Device Send short message to Authorized
Phone numbers
8.1 Position data short message Format
Note: Refer to P02 -Query Current Position
Example:
********** ,09-28 12:11:02,Speed:0km/h,Battery:85%,GPS:3,Lock Close, http://maps.google.com/?q=22.549737,114.076685
NO. Field Name Example Description
1 Unit ID **********
2 Separator ,
3 Date time 09-28 12:11:02 Sep. 28th 12:11:02
UTC time. Adjust this time by P10
command.
4 Separator ,
5 GPS Speed Speed:0km/h
6 Separator ,
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 16 / 89
7 Battery Level Battery:85% if charging power, will display: Charging
8 Separator ,
9 Number of Capture
satellites
GPS:3 Number of captured satellites
10 Separator ,
11 Lock status Lock Closed Lock Status
12 Separator “,”
13 New line 0x0D 0x0A Invisible character(New Line)
14 Google Map link http://maps.google.com/?q=22.549737,114.07
6685
22.549737 indicate Latitude, positive
value north latitude, negative value south
latitude; 114.076685 indicate longitude,
positive value East Longitude, negative
value West longitude.
8.2 Alert data short message Format
Lock rope tamper Alert Format
ALM, Lock Rope Tamper, ********** ,09-28 12:03:43,Battery:95%,GPS:3, Lock Closed,http://maps.google.com/?q=22.549737,114.076685
Swiping unauthorized RFID key Alert Format
ALM, Swiping unauthorized RFID key,********** ,09-28 12:11:02,Battery:95%,GPS:3, Lock
Closed,http://maps.google.com/?q=22.549332,114.076561
Unlocking Alert Format
ALM,Lock Open,********** ,09-28 12:11:02,Battery:95%,GPS:3, Lock Open,http://maps.google.com/?q=22.549730,114.076615
Wrong Password Alert Format
ALM, Wrong Password,********** ,09-28 12:11:02,Battery:95%,GPS:3, Lock Closed,http://maps.google.com/?q=22.549656,114.076564
Vibration Alert Format
ALM,Vibration,********** ,09-28 04:31:32,Battery:66%,GPS:3, Lock Closed,http://maps.google.com/?q=22.549754,114.076250
Enter Geo-fence Alert Format
ALM,Enter Geo-fence,Fence Name:Home,********** ,09-28 00:02:39,Battery:60%,GPS:3, Lock
closed,http://maps.google.com/?q=22.549737,114.076685
Exit Geo-fence Alert Format
ALM, Exit Geo-fence,Fence Name:Company,********** ,09-28 03:21:45,Battery:58%,GPS:3, Lock 
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 17 / 89
closed,http://maps.google.com/?q=22.549737,114.076685
Low Battery Alert Format
ALM,Low Battery, ********** ,09-28 03:27:48,Battery:15%,GPS:3, Lock closed,http://maps.google.com/?q=22.549736,114.076588
Back cover Opened Alert Format
ALM,Back Cover Opened,********** ,09-28 03:27:48,Battery:58%,GPS:3, Lock closed,http://maps.google.com/?q=22.549736,114.076677
Motor Fault Alert Format
ALM,Moter Fault,********** ,09-28 03:27:48,Battery:58%,GPS:3, Lock closed,http://maps.google.com/?q=22.549736,114.076677
Jamming Alert Format
ALM,Gprs Jamming,********** ,09-28 03:27:48,Battery:58%,GPS:3, Lock closed,http://maps.google.com/?q=22.549736,114.076677
No. Field Name Example Description
1 Header ALM
2 Alert type Lock Rope Tamper
3
separator ,
4 Unit ID **********
5
separator ,
6 Date time 08-28 12:03:43 Aug 28th 12:03:43
UTC time. Adjust this time by P10 command.
7
separator ,
8 Battery Battery:85%
9
separator ,
10 GPS signal GPS:3 Number of captured satellites
11 Separator ,
12 Lock open/close status Lock closed Lock Status
13 separator ,
14 carriage return-linefeed 0x0D 0x0A Invisible character(New Line)
15 Google Map link http://maps.google.com/?q=22.549737,114.076685 22.549737 mean latitude. Positive value is
North latitude, negative value is South
latitude.
114.076685 mean longitude. Positive value
is East longitude, Negative value is West
longitude
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 18 / 89
8.3 Command Response short message Format
For the response content of all commands sent through authorized Phone Numbers, please refer to the Section:9
GPS platform sends commands to device Response content.
Note: Short message and GPRS command response content format are the same.
9 GPS Platform Send Commands to Device
Command Word List(ASCII)
Command Word Description
P00 Set/query Working Mode
P01 Query Device Firmware Version
P02 Query Current Position.
P04 Set /Query Position data reporting time interval after device wake up and Device Timing
wake up interval
P06 Set/query SIM1 and SIM2 Communication Parameters
P09 Set/query GPS and GSM indicators Control
P10 Set /query the time difference of short message Position data
P11 Set /query Authorized Phone Numbers that used to receive alert message /short message
position data or sending SMS commands.
P12 Enable or Disable the device to send SMS alerts to the specified Authorized Phone
Numbers.
P13 Restore factory setting of device. All parameters will be recovered to factory setting exclude
Host IP address, port, APN, Authorized Phone numbers.
P14 Query device’s IMEI number
P15 Reboot the device remotely. the device will restart after 30 sec when received this
command.
P22 Do time synchronization When the device continues to report invalid GPS signals and GPS
time in the Position data
P24 Set /query geo-fence name and enable/disable one of the geo-fence
P29 Set or query the Fence Nodes of The Polygon Geo-fence
P30 Delete fence Node information and geo-fence name for a fence ID
P31 Inform the device that GPS platform/Serial port side has finished setting this Geo-fence, it
can detect geo-fence alert now.
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 19 / 89
P32 Force the device to go to sleep.
P35 Acknowledge the alert or position data at GPS platform.
P36 Set /query Vibration sensitivity coefficient threshold of Vibration alert.
P37 Set /query Vibration Sensitivity Coefficient of Motion state detection. The smaller the
acceleration value, the easier it is to detect the Motion state.
P38 Set /query Interval of Unlocking alert.
P39 Set /query working time after waking up
P40 Set/query alert switch. The device supports 10 types of alerts. They are Lock rope tamper,
swiping unauthorized RFID key, unlocking, wrong password, vibration, enter geo-fence, exit
geo-fence, low battery, Back cover Opened and Motor Fault alerts
P41 Authorized RFID key Management
P42 Batch-Add Unlocking Authorized RFID
P43 Unlock device by password.
P44 Change the unlock password.
P46 Acknowledge the Lock and Unlock Report data at GPS platform.
P50 Enable or Disable the power switch on device mainboard.
P58 Set/Query RFID unlocking related to geo-fence
P97 Change the data acknowledgement mechanism. By default, all alert data and lock/unlock
report - (P45) require the GPS platform to acknowledge them, otherwise the data will
continue to be sent. With this command, you can configure these data without platform
confirmation, or configure the maximum number of reports when the platform does not
respond correctly.
P99 Upgrade the Device’s Firmware over the air.
P100 Query device real-time status and GSM module version (2G/3G/4G)
P106 Set/Query Can Unlock In The polygon Geo-fence
P108 Set/Query Can Unlock In The POI(Point of Interest)
P110 Set/Query G-Sensor filter function
P111 Set/Query Working In Lock/Unlock Mode Reporting Data Time Interval
P112 Enable/Disable Lock Rope Cut Reminder(Blink & Beep)
P113 Enable/Disable Blind Area Data Packaging
P114 Enable/Disable Base Station Positioning Function
P122 Enable/Disable Bluetooth Function
P123 Change the Unlock Password
P125 Extract Data From Micro USB/Bluetooth (need firmware version ‘GL600E_........’)
P126 Clear blind spot data
P127 Extended power saving solution: turn off GPS module in idle time
P128 Query Bluetooth Mac Address & Name & Version
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 20 / 89
P00 - Set working mode
Command Description
This command is used to set /query the working mode of the device.
Syntax
Read Command (P00,<Action>)
Response (<Unit ID>,P00,<Working mode>)
Write Command (P00,<Action>,< Working mode >)
Response (<Unit ID>,P00,<Working mode>)
Parameter Description
Parameters Description Value Range Default
<Action> 0 Query the previous setting
1 Write the Parameters
0~1
<Working mode> 0 SMS mode
1 Power saving mode
2 Real-time tracking mode
8 Lock/Unlock Mode
0,1,2,8 1
Example
Query Working mode
Read Command (P00,0)
Response (**********,P00,1)
Response Description Content Description
********** Unit ID
P00 Command Word
1 Working mode: Power saving mode
Set Working mode to SMS Mode
Write Command (P00,1,0)
Response (**********,P00,1)
Response Description Content Description
********** Unit ID
P00 Command Word
0 Working mode: SMS Mode
Set Working mode to Power Saving Mode
Write Command (P00,1,1)
Response (**********,P00,1)
Response Description Content Description
********** Unit ID
P00 Command Word
1 Working mode: Power saving mode
Set Working mode to Real-time tracking mode
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 21 / 89
Write Command (P00,1,2)
Response (**********,P00,2)
Response Description Content Description
********** Unit ID
P00 Command Word
2 Working mode: Real-time tracking mode
Set Working mode to Lock/Unlock mode
Write Command (P00,1,8)
Response (**********,P00,8)
Response Description Content Description
********** Unit ID
P00 Command Word
8 Working mode: Lock/Unlock mode
Sending Command Channel
GPRS SMS USB
P01 - Query firmware version
Command Description
Query Device Firmware Version
Syntax
Read Command (P01)
Response (<Unit ID>,P01,<Firmware version>)
Parameter Description
Parameters Description Value Range Default
<Firmware version> Device Firmware version
Example
Query Device Firmware Version
Read Command (P01)
Response (**********,P01,GL600__2019-03-13_13:41:57_V1.0)
Response Description Content Description
********** Unit ID
P01 Command Word
GL600__2019-03-13_13:41:57_V1.0 Firmware version
Sending Command Channel
GPRS SMS USB
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 22 / 89
P02 - Query Current Position
Command Description
Query Current Position.
The device will respond the Position data(short message) to the Authorized Phone Number 1 if send this command via
GPRS/Cellular Network; will respond the Position data(short message) to the Sending Authorized Phone Number if send this
command via one of the Authorized Phone Number. The short message Position data format, Refer to Section 8.1 Position
data short message format
Syntax
Read Command (P02)
Response <Unit ID>,<Date time>,<GPS Speed>,<Battery Level>,<Number of Capture satellites>,<Lock
Status>,<Google Map link>
Parameter Description
Parameters Description Value Range Default
<Unit ID> Device ID last 10 digit numbers of IMEI
<Date time> Report Date time. Format: MM-DD HH:MM:SS
Adjust the Time difference of the short message by P10
command.
<GPS Speed> GPS speed . in KM/H
<Battery Level> Battery Level . 1%~100%
<Number of Capture
satellites>
Number of captured satellites.
<Lock Status> Lock Status. Locked or Unlocked
<Google Map link> Google Map link. Copy this link to Browser to check
Detailed location.
Example
Query Current Position
Read Command (P02)
Response **********,11-30 12:00:00,Speed:10km/h,Battery:50%,GPS:7,Lock Closed,
http://maps.google.com/?q=22.549737,114.076685
Response Description Content Description
********** Unit ID
11-30 12:00:00 November 30th
 12:00:00
Speed:10km/h GPS speed is 10km/h
Battery:50% Battery Level is 50%
GPS:7 Number of captured satellites .7
Lock Closed Lock Status. Currently, It’s Locked.
http://maps.google.com/?q=22.549737,114.076685 Google Map link
Sending Command Channel
GPRS SMS USB
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 23 / 89
P04 - Set/Query Position Data Reporting Time interval and Timing
wake up interval
Command Description
This command is used to set /query Position data reporting time interval after device wake up and Device Timing wake up
interval
Syntax
Read Command (P04,<Action>)
Response (<Unit ID>,P04, <Reporting time interval>,<Timing wake up interval>)
Write Command (P04,<Action>,<Reporting time interval>,<Timing wake up interval>)
Response (<Unit ID>,P04,<Reporting time interval>,<Timing wake up interval>)
Parameter Description
Parameters Description Value Range Default
<Action> 0 Query the previous setting
1 Write the Parameters
0~1
<Reporting time interval> Position data reporting time interval after wake up.
unit in seconds
5~600 30
<Timing wake up interval> Timing wake up interval. Unit in minutes 30~1440 30
Example
Query Position data reporting time interval after wake up and Timing wake up interval
Read Command (P04,0)
Response (**********,P04,30,30)
Response Description Content Description
********** Unit ID
P04 Command Word
30 Reporting time interval is 30 sec
30 Timing wake up interval is 30 minutes.
Set Position data reporting time interval to 60 sec , and Timing wake up interval to 120 minutes.
Write Command (P04,1,60,120)
Response (**********,P04,60,120)
Response Description Content Description
********** Unit ID
P04 Command Word
60 Reporting time interval is 60 sec
120 Timing wake up interval is 120 minutes.
Sending Command Channel
GPRS SMS USB
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 24 / 89
P06 - Set/Query SIM1 and SIM2 Communication Parameters
P06 - Set/Query SIM1’s IP Port and GPRS/Cellular network Parameters
Command Description
This command is used to set /query SIM Card 1’s host IP address (Domain Name)/Port/APN and APN account.
Note:
The device can be installed with two Micro SIM cards and supports the dual SIM single standby mode. The device will
automatically select a SIM card to register the network.
When modifying IP, port, APN, ID information, the blind area will be cleared.
Syntax
Read Command (P06,<Action>)
Response (<Unit ID>,P06,<Host IP address/Domain Name>,<TCP port>,<APN>,<APN user>,<APN
pass>,<SIM card Number>)
Write Command (P06,<Action>,<Host IP address/Domain Name>,<TCP port>,<APN>,<APN user>,<APN
pass>,<SIM card Number>)
Response (<Unit ID>,P06,<Host IP address/Domain Name>,<TCP port>,<APN>,<APN user>,<APN
pass>,<SIM card Number>)
Parameter Description
Parameters Description Value Range Default
<Action> 0 Query the previous setting
1 Write the Parameters
0~1
<Host IP address/Domain
Name>
Hosting IP address: **************
Domain Name: tracker.mobicomtracking.com
**************
<TCP port> TCP Port number of the remote host server. 0~65530 12000
<APN> Access Point Name.
e.g. China Mobile APN is cmnet
Up to 50
characters
cmnet
<APN user> The APN username to access GPRS network. Up to 50
characters
<APN pass> The APN password to access GPRS network. Up to 50
characters
<SIM card Number> SIM card Number. 0 SIM1
 1 SIM2
0~1 0
Example
query SIM Card 1’s host IP address(Domain Name)/Port/APN and APN account
Read Command (P06,0)
Response (**********,P06,**************,11006,cmnet,,,0) or
(**********,P06,tracker.mobicomtracking.com,11006,cmnet,,,0)
Response Description Content Description
********** Unit ID
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 25 / 89
P06 Command Word
************** Host IP address
tracker.mobicomtracking.com Domain Name
11006 TCP port
cmnet APN
Note: here ,no APN account. Keep the APN
user and pass blank
0 SIM card 1
Set SIM Card 1’s host IP address: *************** ,TCP port: 1156 , APN: internet ,APN user: gprs
APN pass: web
Write Command (P06,1,***************,1156,internet,gprs,web)
Response (**********,P06,***************,1156,internet,gprs,web,0)
Response Description Content Description
********** Unit ID
P06 Command Word
*************** Host IP address
1156 TCP port
internet APN
gprs APN username
web APN password
0 SIM card 1
Set SIM Card 1’s host Domain Name : tracker.mobicomtracking.com ,TCP port: 1156 , APN: internet ,APN user and pass are
blank.
Write Command (P06,1,tracker.mobicomtracking.com,1156,internet,,)
Response (**********,P06,tracker.mobicomtracking.com,1156,internet,,,0)
Response Description Content Description
********** Unit ID
P06 Command Word
tracker.mobicomtracking.com Domain Name
1156 TCP port
internet APN .
APN username is blank
APN password is blank
0 SIM card 1
Sending Command Channel
GPRS SMS USB
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 26 / 89
P06 - Set/Query SIM2’s IP Port and GPRS/Cellular network Parameters
Command Description
This command is used to set /query SIM Card 2’s host IP address(Domain Name)/Port/APN and APN account.
Note:
The device can be installed with two Micro SIM cards and supports the dual SIM single standby mode. The device will
automatically select a SIM card to register the network.
Syntax
Read Command (P06, <Action>)
Response (<Unit ID>,P06,<Host IP address/Domain Name>,<TCP port>,<APN>,<APN user>,<APN
pass>,<SIM card Number>)
Write Command (P06,<Action>,<Host IP address/Domain Name>,<TCP port>,<APN>,<APN user>,<APN
pass>,<SIM card Number>)
Response (<Unit ID>,P06,<Host IP address/Domain Name>,<TCP port>,<APN>,<APN user>,<APN
pass>,<SIM card Number>)
Parameter Description
Parameters Description Value Range Default
<Action> 2 Query the previous setting
3 Write the Parameters
2~3
<Host IP address/Domain
Name>
Hosting IP address: **************
Domain Name: tracker.mobicomtracking.com
**************
<TCP port> TCP Port number of the remote host server. 0~65530 12000
<APN> Access Point Name.
e.g. China Mobile APN is cmnet
Up to 50
characters
cmnet
<APN user> The APN username to access GPRS network. Up to 50
characters
<APN pass> The APN password to access GPRS network. Up to 50
characters
<SIM card Number> SIM card Number. 0 SIM1
 1 SIM2
0~1 1
Example
query SIM Card 2’s host IP address(Domain Name)/Port/APN and APN account
Read Command (P06,2)
Response (**********,P06,**************,11006,cmnet,,,1) or
(**********,P06,tracker.mobicomtracking.com,11006,cmnet,,,1)
Response Description Content Description
********** Unit ID
P06 Command Word
************** Host IP address
tracker.mobicomtracking.com Domain Name
11006 TCP port
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 27 / 89
cmnet APN .
Note: here ,no APN account. Keep the APN
user and pass blank
1 SIM card 2
Set SIM Card 2’s host IP address: *************** ,TCP port: 1156 , APN: internet ,APN user: gprs
APN pass: web
Write Command (P06,3,***************,1156,internet,gprs,web)
Response (**********,P06,***************,1156,internet,gprs,web,0)
Response Description Content Description
********** Unit ID
P06 Command Word
*************** Host IP address
1156 TCP port
internet APN .
gprs APN username
web APN password
1 SIM card 2
Set SIM Card 2’s host Domain Name : tracker.mobicomtracking.com ,TCP port: 1156 , APN: internet ,APN user and pass are
blank.
Write Command (P06,3,tracker.mobicomtracking.com,1156,internet,,)
Response (**********,P06,tracker.mobicomtracking.com,1156,internet,,,1)
Response Description Content Description
********** Unit ID
P06 Command Word
tracker.mobicomtracking.com Domain Name
1156 TCP port
internet APN .
APN username is blank
APN password is blank
1 SIM card 2
Sending Command Channel
GPRS SMS USB
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 28 / 89
P09 - Turn On/Off GPS and GSM indicator
Command Description
This command is used to turn on or turn off GPS and GSM indicators.
After turn off the GPS and GSM indicators, they will remain off regardless of whether the device is working properly or not.
After turn on them, they will continue to work according to the original indicator definition state mode.
Syntax
Read Command (P09,<Action>)
Response (<Unit ID>,P09,<Turn On/Off indicator>)
Write Command (P09,<Action>,<Turn On/Off indicator>)
Response (<Unit ID>,P09,<Turn On/Off indicator>)
Parameter Description
Parameters Description Value Range Default
<Action> 0 Query the previous setting
1 Write the Parameters
0~1
<Turn On/Off indicator> 0 indicates Turn off GPS and GSM indicators;
1 indicates Turn on GPS and GSM indicators
0~1 1
Example
Query GPS and GSM indicators Control Status
Read Command (P09,0)
Response (**********,P09,1)
Response Description Content Description
********** Unit ID
P09 Command Word
1 Turn on GPS and GSM indicators
Turn off GPS and GSM indicators
Write Command (P09,1,0)
Response (**********,P09,0)
Response Description Content Description
********** Unit ID
P09 Command Word
0 Turn off GPS and GSM indicators
Sending Command Channel
GPRS SMS USB
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 29 / 89
P10 - Set/Query the time difference of GPRS position data and SMS
alert
Command Description
This command is used to set /query the time difference of GPRS position data and SMS alert.
Syntax
Read Command (P10,<Action>)
Response (<Unit ID>,P10,<GPRS/SMS time difference>)
Write Command (P10,<Action>,<time difference>)
Response (<Unit ID>,P10, <GPRS/SMS time difference>)
Parameter Description
Parameters Description Value Range Default
<Action> 0 Query the previous setting (GPRS)
1 Write the Parameters (GPRS)
2 Query the previous setting (SMS)
3 Write the Parameters (SMS)
0~3
<GPRS time difference>
Or
<SMS time difference>
the time difference of short message Position data.
Unit in minutes.
E.g.
UTC +08:00 8*60=480
UTC -05:30 5*60+30=-330
-720~780 0
Example
Query the time difference of GPRS Position data
Read Command (P10,0)
Response (**********,P10,480)
Response Description Content Description
********** Unit ID
P10 Command Word
480 GPRS time difference. UTC+08:00
Set the time difference of GPRS Position data to UTC -03:00. time difference is -180
Write Command (P10,1,-180)
Response (**********,P10,-180)
Response Description Content Description
********** Unit ID
P10 Command Word
-180 GPRS time difference. UTC-03:00
Query the time difference of SMS alert
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 30 / 89
Read Command (P10,2)
Response (**********,P10,480)
Response Description Content Description
********** Unit ID
P10 Command Word
480 SMS time difference. UTC+08:00
Set the time difference of SMS alert to UTC -03:00. time difference is -180
Write Command (P10,3,-180)
Response (**********,P10,-180)
Response Description Content Description
********** Unit ID
P10 Command Word
-180 SMS time difference. UTC-03:00
Sending Command Channel
GPRS SMS USB
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 31 / 89
P11 - Set/Query Authorized Phone Numbers
Command Description
This command is used to set /query Authorized Phone Numbers that used to receive alert message /short message position
data or sending SMS commands. The device does not respond when the mobile number is not registered.
Syntax
Read Command (P11,<Action>,<Authorized Phone number index>)
Response (<Unit ID>,P11,<Authorized Phone number index>,<Authorized Phone number>)
Write Command (P11,<Action>,<Authorized Phone number index>,<Authorized Phone number >)
Response (<Unit ID>,P11,<Authorized Phone number index>,<Authorized Phone number >)
Parameter Description
Parameters Description Value Range Default
<Action> 0 Query the previous setting
1 Write the Parameters
0~1
<Authorized Phone
number index>
Up to 5 Authorized Phone numbers. 1~5 0
<Authorized Phone
number>
phone number, can not exceed 15 digit numbers, add
country code at front,
e.g. China is 86 or +86.
Example
Query The first Authorized Phone number
Read Command (P11,0,1)
Response (**********,P11,1,8615017931001)
Response Description Content Description
********** Unit ID
P11 Command Word
1 Index, Authorized Phone number1
8615017931001 Authorized Phone number
Set the Authorized Phone number 2 to 8615017931001
Write Command (P11,1,2,8615017931001)
Response (**********,P11,2,8615017931001)
Response Description Content Description
********** Unit ID
P11 Command Word
2 Index, Authorized Phone number2
8615017931001 Authorized Phone number
Sending Command Channel
GPRS SMS USB
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 32 / 89
P12 - Enable or Disable the device to send SMS alerts to the specified
Authorized Phone number
Command Description
This command is used to enable or disable the device to send SMS alerts to the specified Authorized Phone Number. by
default, The device only sends SMS alert information to Authorized Phone Number1 and Authorized Phone Number 2.
Syntax
Read Command (P12,<Action>)
Response (<Unit ID>,P12,<Enable/Disable Phone Number1>,<Enable/Disable Phone
Number2>,<Enable/Disable Phone Number3>,<Enable/Disable Phone Number
4>,<Enable/Disable Phone Number5>)
Write Command (P12,<Action>,<Enable/Disable Phone Number1>,<Enable/Disable Phone
Number2>,<Enable/Disable Phone Number3>,<Enable/Disable Phone Number
4>,<Enable/Disable Phone Number5>)
Response (<Unit ID>,P12,<Enable/Disable Phone Number1>,<Enable/Disable Phone
Number2>,<Enable/Disable Phone Number3>,<Enable/Disable Phone Number
4>,<Enable/Disable Phone Number5>)
Parameter Description
Parameters Description Value Range Default
<Action> 0 Query the previous setting
1 Write the Parameters
0~1
<Enable/Disable Phone
Number1>
0 indicates Disable the device to send SMS alerts to
the specified Authorized Phone number1;
1 indicates Enable the device to send SMS alerts to
the specified Authorized Phone number 1.
0~1 1
<Enable/Disable Phone
Number2>
0 indicates Disable the device to send SMS alerts to
the specified Authorized Phone number 2;
1 indicates Enable the device to send SMS alerts to
the specified Authorized Phone number 2.
0~1 1
<Enable/Disable Phone
Number3>
0 indicates Disable the device to send SMS alerts to
the specified Authorized Phone number 3;
1 indicates Enable the device to send SMS alerts to
the specified Authorized Phone number 3.
0~1 0
<Enable/Disable Phone
Number4>
0 indicates Disable the device to send SMS alerts to
the specified Authorized Phone number 4;
1 indicates Enable the device to send SMS alerts to
the specified Authorized Phone number 4.
0~1 0
<Enable/Disable Phone
Number5>
0 indicates Disable the device to send SMS alerts to
the specified Authorized Phone number 5;
1 indicates Enable the device to send SMS alerts to
the specified Authorized Phone number 5.
0~1 0
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 33 / 89
Example
Query the configuration of the Authorized Phone number receiving SMS alert
Read Command (P12,0)
Response (**********,P12,1,1,0,0,0)
Response Description Content Description
********** Unit ID
P12 Command Word
1 Authorized Phone number1 Enable to accept
SMS alert
1 Authorized Phone number2 Enable to accept
SMS alert
0 Authorized Phone number3 Disable to accept
SMS alert
0 Authorized Phone number4 Disable to accept
SMS alert
0 Authorized Phone number5 Disable to accept
SMS alert
Set the specified Authorized Phone number 1,2,3 to accept the SMS alert
Write Command (**********,P12,1,1,1,1,0,0)
Response (**********,P12,1,1,1,0,0)
Response Description Content Description
********** Unit ID
P12 Command Word
1 Authorized Phone number1 Enable to accept
SMS alert
1 Authorized Phone number2 Enable to accept
SMS alert
1 Authorized Phone number3 Enable to accept
SMS alert
0 Authorized Phone number4 Disable to accept
SMS alert
0 Authorized Phone number5 Disable to accept
SMS alert
Sending Command Channel
GPRS SMS USB
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 34 / 89
P13 - Restore Factory setting
Command Description
Restore factory setting of device. All parameters will be recovered to factory setting exclude Host IP address, port, APN,
Authorized Phone numbers.
Syntax
Read Command (P13)
Response (<Unit ID>,P13)
Parameter Description
Parameters Description Value Range Default
- - - -
Example
Restore factory setting of device
Read Command (P13)
Response (**********,P13)
Response Description Content Description
********** Unit ID
P13 Command Word
Sending Command Channel
GPRS SMS USB
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 35 / 89
P14 - Read device’s IMEI number
Command Description
Query device’s IMEI number
Syntax
Read Command (P14)
Response (**********,P14,86029**********)
Parameter Description
Parameters Description Value Range Default
- - - -
Example
Query device’s IMEI number
Read Command (P14)
Response (**********,P14,86029**********)
Response Description Content Description
********** Unit ID
P14 Command Word
86029********** IMEI
Sending Command Channel
GPRS SMS USB
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 36 / 89
P15 - Reboot the device remotely
Command Description
Reboot the device remotely. the device will restart after 30 sec when received this command.
Syntax
Read Command (P15)
Response (**********,P15)
Parameter Description
Parameters Description Value Range Default
- - - -
Example
Reboot the device remotely
Read Command (P15)
Response (**********,P15)
Response Description Content Description
********** Unit ID
P15 Command Word
Sending Command Channel
GPRS SMS USB
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 37 / 89
P22 - Time Synchronization
Command Description
This command is used to do time synchronization When the device continues to report invalid GPS signals and GPS time in
the Position data. If the device is currently acquiring GPS signals normally, this command will not take effect.
Syntax
Write Command (P22,<Date Time>)
Response (<Unit ID>,P22,<Successful/failed>)
Parameter Description
Parameters Description Value Range Default
<Date Time> Year/Month/Day/Hour/Minute/Second, and it is UTC
time. Format as below: YYYYMMDDHHMMSS
<successful/failed> 1 indicates successful; 0 indicates failed 0~1
Example
Set the device’s Position data’s Date time to 2019/03/18 16:43:28 (UTC)
Write Command (P22,20190318164328)
Response (**********,P22,1)
Response Description Content Description
********** Unit ID
P22 Command Word
1 Set successfully;
if this value is 0 ,means Set unsuccessfully.
Sending Command Channel
GPRS SMS USB
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 38 / 89
P24 - Set/Query Geo-fence name and enable/disable one of fence ID
Command Description
This command is used to set or query geo-fence name and enable/disable one of the geo-fence.
Syntax
Read Command (P24,<Action>,<Fence ID index>,<Enable/Disable>,<Geo-fence Name>)
Response (<Unit ID>,P24, <Fence ID index>,<Enable/Disable>,<Geo-fence Name>)
Write Command (P24,<Action>,<Fence ID index>)
Response (<Unit ID>,P24,<Fence ID index>,<Enable/Disable>,<Geo-fence Name>)
Parameter Description
Parameters Description Value Range Default
<Action> 0 Query the previous setting
1 Write the Parameters
0~1
<Fence ID index> Geo-fence ID index. The device supports at most 10
Fences.
1~10
<Enable/Disable> Enable/Disable specified Fence ID
1 means Enable ; 0 means Disable
0~1
<Geo-fence Name> The fence name of the specified fence ID.
Up to 30 characters, only numbers and letters are
supported
Up to 30
characters
Example
Query fence ID 10’s Geo-fence name and Geo-fence Enable/Disable Status
Read Command (P24,0,10)
Response (**********,P24,10,1,companyaddress001)
Response Description Content Description
********** Unit ID
P24 Command Word
10 Fence ID
1 Enable
companyaddress001 Fence Name
Set fence ID 1’s geo-fence name to ‘long gang road144‘ and Enable its function
Write Command (P24,1,1,1,long gang road144)
Response (**********,P24,1,1,long gang road144)
Response Description Content Description
********** Unit ID
P24 Command Word
1 Fence ID
1 Enable
long gang road144 Fence Name
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 39 / 89
Disable fence ID 05’s Geo-fence function
Write Command (P24,1,5,0,)
Response (**********,P24,5,0,home)
Response Description Content Description
********** Unit ID
P24 Command Word
5 Fence ID
0 Disable
home Fence Name
Sending Command Channel
GPRS SMS USB
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 40 / 89
P29 - Set/Query the Fence Nodes of The Polygon Geo-fence
Command Description
This command is used to set or query the Fence Nodes of The Polygon Geo-fence, at most 10 Polygon Fences.
Syntax
Read Command (P29,<Action>,<Fence ID index>,<Fence Node Page index >)
Response (<Unit ID>,P29,<Fence ID index>,<Total Nodes>,<Fence Node Page index >,<Total Nodes in
this Page ID>,< Fence Node…>)
Write Command (P29,<Action>,<Fence ID index>,<Fence Node Page index >,<Total Nodes in this Page ID>,<
Fence Node…>)
Response (<Unit ID>,P29,<Fence ID index>,<Total Nodes>,<Fence Node Page index >,<Total Nodes in
this Page ID>,< Fence Node…>)
Parameter Description
Parameters Description Value Range Default
<Action> 0 Query the previous setting
1 Write the Parameters
0~1
<Fence ID index> Geo-fence ID index. The device supports at most 10
Fences.
1~10
<Total Nodes> Total Nodes in this Fence ID. Up to 50 Nodes 0~50
<Fence Node Page
index >
Fence Node Page index. One Page saves 10
Fence Nodes. 1 Fence Node includes the longitude
and latitude info.
1~5
<Total Nodes in this Page
ID>
Total Nodes in this Page ID. Up to 10 Nodes in one
Page
0~10
< Fence Node…> Node information, nodes must appear in pairs, that is,
one longitude and one latitude appear in pairs, one
longitude and one latitude constitute a complete
point, and the node information appears in the form of
DDDMM.MMMM,DDMM.MMMM. Up to 5 pages can
be set to 50 points.
East Longitude or Northern latitude: Positive value.
e.g. 11414.9742,2242.0727
West Longitude or South latitude: Negative value
e.g. -4815.2106,-2019.3663
Example
Query fence ID 5 all Page ‘s Fence Node info.
Note: To get the whole fence nodes. User need to query fence node from page 1 to 5.
Read Command (P29,0,5,1)
(P29,0,5,2)
(P29,0,5,3)
(P29,0,5,4)
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 41 / 89
(P29,0,5,5)
Response Note: There is no newline in the actual received command content. The newline in the example
is due to document layout.
(**********,P29,5,32,1,10,11414.9742,2242.0727,11415.0952,2242.2437,11415.1647,2242.2959,
11415.3398,2242.3506,11415.4814,2242.3814,11415.9784,2242.3886,11416.1020,2242.3957,
11416.2668,2242.4028,11416.3517,2242.3838,11416.4702,2242.3054)
(**********,P29,1,32,2,10,11416.4882,2242.2627,11416.5475,2242.1676,11416.5552,2242.1130,
11416.5423,2241.8945,11416.5371,2241.8303,11416.5785,2241.7404,11416.5166,2241.7235,
11416.4213,2241.6664,11416.2925,2241.6047,11416.1303,2241.4431)
(**********,P29,1,32,3,10,11416.0428,2241.3861,11416.0016,2241.3481,11415.9243,2241.3434,
11415.7441,2241.3837,11415.5716,2241.4407,11415.4300,2241.5025,11415.3449,2241.5833,
11415.1879,2241.6926,11414.9896,2241.7187,11414.8274,2241.7567)
(**********,P29,1,32,4,2,11414.7579,2241.8327,11414.7862,2241.9563)
(**********,P29,1,32,5,2,0,0,0,0)
Response Description Content Description
********** Unit ID
P29 Command Word
5 Fence ID
32 Total 32 Fence Nodes in fence ID 5
1 Fence Node Page ID= 1
Page Index 1 to 5
10 Total 10 Fence Nodes in Fence Node Page 1
11414.9742,2242.0727 first Node. Longitude and Latitude.
11414.9742 DDDMM.MMMM
Change the longitude to degree:
114+14.9742/60=114+0.24957=114.249570
It’s a positive value. Means East Longitude.
If -11414.9742 means West Longitude
2242.0727 DDMM.MMMM
Change the latitude to degree:
22+42.0727/60=22+ 0.7012116=22.701211
It’s a positive value. Means Northern latitude.
If -2242.0727 means South latitude
11415.0952,2242.2437 The Second Node. Longitude and Latitude
… From the third Node to 9th Node
11416.4702,2242.3054 The 10th Node. Longitude and Latitude
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 42 / 89
Set fence Nodes for Fence ID 2. Total 17 Nodes
If less than 10 Nodes. Just send one command;
If less than 20 Nodes, need send two commands.
Note: Write longitude and latitude -4815.2106,-2019.3663,
 Response longitude and latitude -4815.2105,-2019.3662
The last value is different between Write and Response message. This is because the longitude error caused by the
conversion of latitude and longitude units is within the allowable range.
Write Command Note: When the instruction is actually sent, there should be no line breaks in the content of the
instruction. The newline in the example is because of the need for document layout
(P29,1,2,1,10,-4815.2106,-2019.3663,-4810.1020,-2016.2750,-4806.6413,-2014.4198,-4802.8510,
-2013.4920,-4758.0719,-2012.7188,-4754.7760,-2012.8735,-4750.4914,-2016.1204,-4749.6674,
-2019.0572,-4749.6674,-2021.0660,-4750.3266,-2024.0012)
(P29,1,2,2,7,-4750.3266,-2026.6266,-4750.6561,-2029.5601,-4752.6337,-2030.4862,-4759.5551,
-2029.5601,-4809.6076,-2028.4794,-4814.8810,-2026.3178,-4817.8473,-2021.9930)
Response Note: There is no newline in the actual received command content. The newline in the example
is due to document layout.
(**********,P29,2,10,1,10,-4815.2105,-2019.3662,-4810.1019,-2016.2749,-4806.6412,-2014.4197,
-4802.8509,-2013.4919,-4758.0718,-2012.7187,-4754.7759,-2012.8734,-4750.4913,-2016.1203,
-4749.6673,-2019.0571,-4749.6673,-2021.0659,-4750.3265,-2024.0011)
(**********,P29,2,17,2,7,-4750.3265,-2026.6265,-4750.6560,-2029.5600,-4752.6336,-2030.4861,
-4759.5550,-2029.5600,-4809.6075,-2028.4793,-4814.8809,-2026.3177,-4817.8472,-2021.9929)
Response Description Content Description
********** Unit ID
P24 Command Word
2 Fence ID
17 Total 17 Nodes in Fence ID 2
2 Page ID 2
7 Total 7 Nodes in Page ID 2
-4750.3265,-2026.6265 first Node in page ID 2
Longitude and Latitude.
-4750.3265 DDDMM.MMMM
Change the longitude to degree:
-(47+50.3265/60)=-(47+0.838775)=-47.838775
It’s a negative value. Means West Longitude
If 4750.3265 means East Longitude.
-2026.6265 DDMM.MMMM
Change the latitude to degree:
-(20+26.6265/60)=-(20+0.443775)=-20.443775
It’s a negative value.. Means South latitude
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 43 / 89
If 2026.6265 means Northern latitude.
… From the second Node to 6th Node
-4817.8472,-2021.9929 The 7th Node. Longitude and Latitude
Sending Command Channel
GPRS SMS USB
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 44 / 89
P30 - Delete Fence Node info and Geo-fence name for a Fence ID
Command Description
This command is used to delete fence Node information and geo-fence name for a fence ID
Syntax
Write Command (P30,<Fence ID index>)
Response (<Unit ID>,P30,<Successful/failed>)
Parameter Description
Parameters Description Value Range Default
<Fence ID index> Geo-fence ID index. The device supports at most 10
Fences.
1~10
<successful/failed> 1 indicates Delete successfully; 0 indicates failed 0~1
Example
Delete fence Node information and geo-fence name for Fence ID 5
Write Command (P30,5)
Response (**********,P30,1)
Response Description Content Description
********** Unit ID
P30 Command Word
1 1 indicates Erase successfully;
0 indicates failed
Sending Command Channel
GPRS SMS USB
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 45 / 89
P31 - Finished Fence Node information Setting
Command Description
This command is used to inform the device that GPS platform/Serial port side has finished setting this Geo-fence,it can detect
geo-fence alert now.
Syntax
Write Command (P31)
Response (<Unit ID>,P31)
Parameter Description
Parameters Description Value Range Default
- -
Example
Inform the device that GPS platform/Serial port side has finished setting this Geo-fence
Write Command (P31)
Response (**********,P31)
Response Description Content Description
********** Unit ID
P31 Command Word
Sending Command Channel
GPRS SMS USB
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 46 / 89
P32 - Force the device to go to sleep
Command Description
This command is used to force the device to go to sleep.
Note: After the device receives this command for 30 seconds, it goes to sleep.
If the current device is in the process of unlocking or locking, wait for the operation to end, then go to sleep again.
Syntax
Write Command (P32)
Response (<Unit ID>,P32)
Parameter Description
Parameters Description Value Range Default
- -
Example
Force the device to go to sleep
Write Command (P32)
Response (**********,P32)
Response Description Content Description
********** Unit ID
P32 Command Word
Sending Command Channel
GPRS SMS USB
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 47 / 89
P35 - Acknowledge Command to receive Alert and Position data
Command Description
This command is used to acknowledge the alert or position data at GPS platform.
Note: The device will keep on sending the same alert or position data if didn't receive the acknowledge command from GPS
platform .
Whether the position data need to be acknowledge need to decode the ACK bit in Position data.
Syntax
Write Command (P35)
Response (<Unit ID>,P35)
Parameter Description
Parameters Description Value Range Default
- -
Example
GPS platform send below command to acknowledge the alert or position data from device side.
Write Command (P35)
Response (**********,P35)
Response Description Content Description
********** Unit ID
P35 Command Word
Sending Command Channel
GPRS SMS USB
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 48 / 89
P36 - Set/Query Vibration Sensitivity Coefficient threshold of Vibration
alert and repeated interval
Command Description
This command is used to set /query Vibration sensitivity coefficient threshold of Vibration alert. And minimum time interval for
repeated vibration alarms. The greater the acceleration value, the harder it is to detect the vibration alarm.
Syntax
Read Command (P36,<Action>)
Response (<Unit ID>,P36,<Acceleration threshold/interval for repeated>)
Write Command (P36,<Action>,< Acceleration threshold/interval for repeated>)
Response (<Unit ID>,P36, <Acceleration threshold/interval for repeated>)
Parameter Description
Parameters Description Value Range Default
<Action> 0 Query the previous setting
1 Write the Parameters
2 Query the interval for repeated vibration alarms
3 Write the interval for repeated vibration alarms
0~3
<Acceleration threshold> Vibration alert acceleration threshold. The greater the
acceleration value, the harder it is to detect the
vibration alarm. Unit in mg. suggest 500 to 900mg.
default 500 mg.
500~8000 500
<interval for repeated> Minimum time interval for repeated vibration alarms.
The unit is seconds. Default 600 seconds.
1~65535 600
Example
Query Vibration sensitivity coefficient threshold of Vibration alert.
Read Command (P36,0)
Response (**********,P36,500)
Response Description Content Description
********** Unit ID
P36 Command Word
500 Vibration alert acceleration threshold.500mg
Set Vibration sensitivity coefficient threshold of Vibration alert to 700mg
Write Command (P36,1,700)
Response (**********,P36,700)
Response Description Content Description
********** Unit ID
P36 Command Word
700 Vibration alert acceleration threshold.700mg
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 49 / 89
Query the interval for repeated vibration alarms
Read Command (P36,2)
Response (**********,P36,600)
Response Description Content Description
********** Unit ID
P36 Command Word
600 Minimum time interval for repeated vibration
alarms.600 seconds.
Query the interval for repeated vibration alarms
Write Command (P36,3,60)
Response (**********,P36,60)
Response Description Content Description
********** Unit ID
P36 Command Word
60 Minimum time interval for repeated vibration
alarms.60 seconds.
Sending Command Channel
GPRS SMS USB
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 50 / 89
P37 - Set/Query Vibration Sensitivity Coefficient of Motion state
detection
Command Description
This command is used to set /query Vibration Sensitivity Coefficient of Motion state detection. The smaller the acceleration
value, the easier it is to detect the Motion state.
Note: if the motion acceleration value is 0 ,means disable motion detection function. And Vibration or Moving can’t be wake up
the device in future. Restore this function by setting other motion acceleration values.
Syntax
Read Command (P37,<Action>)
Response (<Unit ID>,P37,<Acceleration threshold>)
Write Command (P37,<Action>,< Acceleration threshold>)
Response (<Unit ID>,P37,< Acceleration threshold>)
Parameter Description
Parameters Description Value Range Default
<Action> 0 Query the previous setting
1 Write the Parameters
0~1
<Motion Acceleration
Value>
Vibration Sensitivity Coefficient of Motion state detection. The
smaller the acceleration value, the easier it is to detect the
vibration. Unit in mg. suggest 63 to 500. default 126 mg.
0 indicates Disable motion detection function.
0 or
63~500
126
Example
Query Vibration Sensitivity Coefficient of Motion state detection
Read Command (P37,0)
Response (**********,P37,126)
Response Description Content Description
********** Unit ID
P37 Command Word
126 Motion Acceleration Value.126mg
Set Vibration Sensitivity Coefficient of Motion state detection to 63 mg
Write Command (P37,1,63)
Response (**********,P37,63)
Response Description Content Description
********** Unit ID
P37 Command Word
63 Motion Acceleration Value.63mg
Sending Command Channel
GPRS SMS USB
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 51 / 89
P38 - Set/Query Interval of Unlocking alert
Command Description
This command is used to set /query Interval of Unlocking alert.
When the device is unlocked, an unlock alert is generated immediately. When the device is always unlocked, you can set the
alert to be reported again at this interval.
Syntax
Read Command (P38,<Action>)
Response (<Unit ID>,P38,<Unlock alert interval>)
Write Command (P38,<Action>,< Unlock alert interval>)
Response (<Unit ID>,P38, <Unlock alert interval>)
Parameter Description
Parameters Description Value Range Default
<Action> 0 Query the previous setting
1 Write the Parameters
0~1
<Unlock alert interval> Unlock alert interval. Unit in minutes. 3~180 120
Example
Query Interval of Unlocking alert
Read Command (P38,0)
Response (**********,P38,120)
Response Description Content Description
********** Unit ID
P38 Command Word
120 Unlock alert interval.120 minutes
Set Interval of Unlocking alert to 3 minutes
Write Command (P38,1,3)
Response (**********,P38,3)
Response Description Content Description
********** Unit ID
P38 Command Word
3 Unlock alert interval.3 minutes
Sending Command Channel
GPRS SMS USB
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 52 / 89
P39 - Set/Query Working time after waking up
Command Description
This command is used to set /query working time after waking up.
Note:
The device can be woken up by vibrating/swipe the RFID key/unlock (lock)/timed condition. For example, after the device
vibration wakes up, it will work according to the preset working time. During the wake-up period, if the lock is unlocked, the
time is accumulated again from the unlocking time; if it is continuously in the vibration state, the device continues to work until
the device detects no wake-up condition, Then go to sleep.
Syntax
Read Command (P39,<Action>)
Response (<Unit ID>,P39,<Working time>)
Write Command (P39,<Action>,<Working time>)
Response (<Unit ID>,P39,<Working time>)
Parameter Description
Parameters Description Value Range Default
<Action> 0 Query the previous setting
1 Write the Parameters
0~1
< Working time> Working time after waking up. Unit in minutes. 3~10 10
Example
Query Working time after waking up
Read Command (P39,0)
Response (**********,P39,10)
Response Description Content Description
********** Unit ID
P39 Command Word
10 Working time after waking up.10 minutes
Set Working time after waking up to 5 minutes
Write Command (P39,1,5)
Response (**********,P39,5)
Response Description Content Description
********** Unit ID
P39 Command Word
5 Working time after waking up .5 minutes
Sending Command Channel
GPRS SMS USB
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 53 / 89
P40 - Set/Query Alert Switch
Command Description
This command is used to set /query alert switch.
The device supports 10 types of alerts. They are Lock rope tamper, swiping unauthorized RFID key, unlocking, wrong
password, vibration, enter geo-fence, exit geo-fence, low battery, Back cover Opened and Motor Fault alerts.
Syntax
Read Command (P40,<Action>)
Response (<Unit ID>,P40,<Lock rope tamper alert>,<Swiping unauthorized RFID key alert>,<Unlocking
alert>,<Wrong password alert>,<Vibration alert>,<Enter geo-fence alert>,<Exit geo-fence
alert >,<Low battery alert>,< Back cover Opened alert>,<Motor Fault alert>)
Write Command (P40,<Action>,<Lock rope tamper alert>,<Lock rope tamper alert>,<Swiping unauthorized RFID
key alert>,<Unlocking alert>,<Wrong password alert>,<Vibration alert>,<Enter geo-fence
alert>,<Exit geo-fence alert>,<Low battery alert>,< Back cover Opened alert>,<Motor Fault
alert>)
Response (<Unit ID>,P40,<Lock rope tamper alert>,<Swiping unauthorized RFID key alert>,<Unlocking
alert>,<Wrong password alert>,<Vibration alert>,<Enter geo-fence alert>,<Exit geo-fence
alert >,<Low battery alert>,< Back cover Opened alert>,<Motor Fault alert>)
Parameter Description
Parameters Description Value Range Default
<Action> 0 Query the previous setting
1 Write the Parameters
0~1
<Lock rope tamper alert> Lock rope tamper alert switch
0 indicates Disable alert transmitting via GPRS and SMS
1 indicates Enable alert transmitting via GPRS
2 indicates Enable alert transmitting via SMS
3 indicates Enable alert transmitting via GPRS and SMS
0~3 1
<Swiping unauthorized
RFID key alert>
Swiping unauthorized RFID key alert switch
0 indicates Disable alert transmitting via GPRS and SMS
1 indicates Enable alert transmitting via GPRS
2 indicates Enable alert transmitting via SMS
3 indicates Enable alert transmitting via GPRS and SMS
0~3 1
<Unlocking alert> Unlocking alert switch
0 indicates Disable alert transmitting via GPRS and SMS
1 indicates Enable alert transmitting via GPRS
2 indicates Enable alert transmitting via SMS
3 indicates Enable alert transmitting via GPRS and SMS
0~3 1
<Wrong password alert> Wrong password alert switch: send the Unlock command with
wrong password more than 5 times.
0 indicates Disable alert transmitting via GPRS and SMS
1 indicates Enable alert transmitting via GPRS
2 indicates Enable alert transmitting via SMS
0~3 1
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 54 / 89
3 indicates Enable alert transmitting via GPRS and SMS
<Vibration alert> Vibration alert switch
0 indicates Disable alert transmitting via GPRS and SMS
1 indicates Enable alert transmitting via GPRS
2 indicates Enable alert transmitting via SMS
3 indicates Enable alert transmitting via GPRS and SMS
0~3 0
<Enter geo-fence alert> Enter Geo-fence alert switch
0 indicates Disable alert transmitting via GPRS and SMS
1 indicates Enable alert transmitting via GPRS
2 indicates Enable alert transmitting via SMS
3 indicates Enable alert transmitting via GPRS and SMS
0~3 1
<Exit geo-fence alert> Exit Geo-fence alert switch
0 indicates Disable alert transmitting via GPRS and SMS
1 indicates Enable alert transmitting via GPRS
2 indicates Enable alert transmitting via SMS
3 indicates Enable alert transmitting via GPRS and SMS
0~3 1
<Low battery alert> Low Battery alert switch
0 indicates Disable alert transmitting via GPRS and SMS
1 indicates Enable alert transmitting via GPRS
2 indicates Enable alert transmitting via SMS
3 indicates Enable alert transmitting via GPRS and SMS
0~3 1
<Back cover Opened
alert>
Back Cover Opened alert switch
0 indicates Disable alert transmitting via GPRS and SMS
1 indicates Enable alert transmitting via GPRS
2 indicates Enable alert transmitting via SMS
3 indicates Enable alert transmitting via GPRS and SMS
0~3 1
<Motor Fault alert> Motor Fault alert switch
0 indicates Disable alert transmitting via GPRS and SMS
1 indicates Enable alert transmitting via GPRS
2 indicates Enable alert transmitting via SMS
3 indicates Enable alert transmitting via GPRS and SMS
0~3 1
<Jamming alert> Motor Fault alert switch
0 indicates Disable alert transmitting via GPRS and SMS
1 indicates Enable alert transmitting via GPRS
2 indicates Enable alert transmitting via SMS
3 indicates Enable alert transmitting via GPRS and SMS
0~3 1
Example
Query Alert switch setting
Read Command (P40,0)
Response (**********,P40,1,0,1,1,0,1,1,1,1,1,1)
Response Description Content Description
********** Unit ID
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 55 / 89
P40 Command Word
1 Enable Lock rope tamper alert transmitting via GPRS
channel
0 Disable Swiping unauthorized RFID key alert
transmitting
1 Enable Unlocking alert transmitting via GPRS channel
1 Enable Wrong password alert transmitting via GPRS
channel
0 Disable Vibration alert transmitting
1 Enable Enter geo-fence alert transmitting via GPRS
channel
1 Enable Exit geo-fence alert transmitting via GPRS
channel
1 Enable Low battery alert transmitting via GPRS channel
1 Enable Back cover Opened alert transmitting via GPRS
channel
1 Enable Motor Fault alert transmitting via GPRS
channel.
1 Enable Jamming alert transmitting via GPRS channel.
Set Alert switch For Each alert types
Write Command (P40,1,3,0,3,1,0,1,1,1,1,1,1)
Response (**********,P40,3,0,3,1,0,1,1,1,1,1,1)
Response Description Content Description
********** Unit ID
P40 Command Word
3 Enable Lock rope tamper alert transmitting via GPRS
and SMS channel
0 Disable Swiping unauthorized RFID key alert
transmitting
3 Enable Unlocking alert transmitting via GPRS and SMS
channel
1 Enable Wrong password alert transmitting via GPRS
channel
0 Disable Vibration alert transmitting
1 Enable Enter geo-fence alert transmitting via GPRS
channel
1 Enable Exit geo-fence alert transmitting via GPRS
channel
1 Enable Low battery alert transmitting via GPRS channel
1 Enable Back cover Opened alert transmitting via GPRS 
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 56 / 89
channel
1 Enable Motor Fault alert transmitting via GPRS
channel.
1 Enable Jamming alert transmitting via GPRS channel.
Sending Command Channel
GPRS SMS USB
P41 - Authorized RFID key Management
P41 - Query Authorized RFID key
Command Description
This command is used to query Authorized RFID key.
A total of 50 RFID keys are supported and they are stored in three separate groups. Up to 20 RFID keys per group
Syntax
Read Command (P41,<Action>,<Authorized RFID key group Index>)
Response (<Unit ID>,P41,<Authorized RFID key group Index>,<Total RFID keys in this group>,<Unlock RFID
keys…>)
Parameter Description
Parameters Description Value Range Default
<Action> 0 Query the previous setting 0
<Authorized RFID key
group Index>
A total of 50 RFID keys are supported and they are
stored in three separate groups. Up to 20 RFID keys
per group
1~3
<Total RFID keys in this
group>
Up to 20 RFID keys per group 0~20
<Unlock RFID keys…> The range of RFID key card number is:
0000000001~4294967295, no more than 10
Numbers, otherwise the input is considered invalid.
0000000001~
4294967295
Example
Query All Authorized RFID keys in group 2.
Read Command (P41,0,2)
Response (**********,P41,2,3,0013953759,0013953758,0013953757)
Response Description Content Description
********** Unit ID
P41 Command Word
2 group 2
3 Total RFID keys in this group
0013953759,0013953758,0013953757 Unlock RFID keys
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 57 / 89
Query All Authorized RFID keys in group 1 ,2 and 3
Read Command (P41,0,1)
(P41,0,2)
(P41,0,3)
Response Note: There is no newline in the actual received command content. The newline in the example is
due to document layout.
First Group
(**********,P41,1,20,0000000021,0000000022,0000000023,0000000024,0000000025,0000000026,
0000000027,0000000028,0000000029,0000000030,0000000031,0000000032,0000000033,0000000034,
0000000035,0000000036,0000000037,0000000038,0000000039,0000000040)
Second Group
(**********,P41,2,20,0000000041,0000000042,0000000043,0000000044,0000000045,0000000046,
0000000047,0000000048,0000000049,0000000050,0000000051,0000000052,0000000053,0000000054,
0000000055,0000000056,0000000057,0000000058,0000000059,0000000060)
Third Group
(**********,P41,3,10,0000000061,0000000062,0000000063,0000000064,0000000065,0000000066,0000000067,
0000000068,0000000069,0000000070)
Response Description Content Description
********** Unit ID
P41 Command Word
1 group 1
20 Total 20 RFID keys in this group
0000000021,0000000022,0000000023… Unlock RFID keys
Sending Command Channel
GPRS SMS USB
P41 - Register(Add) Authorized RFID Key
Command Description
This command is used to register Authorized RFID key.
A total of 50 RFID keys are supported.
Syntax
Write Command (P41,<Action>,<Add>,<Total of New added RFID keys>,< Unlock RFID keys…>)
Response (<Unit ID>,P41,<Add>,<Total RFID keys in Flash>)
Parameter Description
Parameters Description Value Range Default
<Action> 1 indicates Write the Parameters 1
<Add> Register Unlock RFID key. 1 indicates Add 1
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 58 / 89
<Total of New added
RFID keys>
The number of new RFID keys added this time. Add up
to 20 RFID keys at a time
1~20
<Unlock RFID keys…> The range of RFID key card number is:
0000000001~4294967295, no more than 10
Numbers, otherwise the input is considered invalid.
0000000001~
4294967295
<Total RFID keys in
Flash>
Total RFID keys remaining in Flash after added. 0~50
Example
Add RFID keys: 0013953759,0013953758,0013953757 as Unlock RFID key.
Write Command (P41,1,1,3,0013953759,0013953758,0013953757)
Response (**********,P41,2,3,0013953759,0013953758,0013953757)
Response Description Content Description
********** Unit ID
P41 Command Word
1 Add operation
3 Total 3 RFID keys remaining in Flash after
added
Add 50 RFID keys as Authorized RFID keys
Write Command Note: When the instruction is actually sent, there should be no line breaks in the content of the
instruction. The newline in the example is because of the need for document layout
Add 20 RFID keys
(P41,1,1,20,0000000021,0000000022,0000000023,0000000024,0000000025,0000000026,0000000027,
0000000028,0000000029,0000000030,0000000031,0000000032,0000000033,0000000034,0000000035,
0000000036,0000000037,0000000038,0000000039,0000000040)
Add another 20 RFID keys
(P41,1,1,20,0000000041,0000000042,0000000043,0000000044,0000000045,0000000046,0000000047,
0000000048,0000000049,0000000050,0000000051,0000000052,0000000053,0000000054,0000000055,
0000000056,0000000057,0000000058,0000000059,0000000060)
Add last 10 RFID keys. Total supports 50 Unlock RFID keys
(P41,1,1,10,0000000061,0000000062,0000000063,0000000064,0000000065,0000000066,0000000067,
0000000068,0000000069,0000000070)
Response (**********,P41,1,20) // Add 20 RFID keys
(**********,P41,1,40) // Add another 20 RFID keys
(**********,P41,1,50) // Add last 10 RFID keys. Total supports 50 Unlock RFID keys
Response Description Content Description
********** Unit ID
P41 Command Word
1 Add operation
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 59 / 89
40 Total 40 RFID keys remaining in Flash after
added
Sending Command Channel
GPRS SMS USB
P41 - Delete Specified Authorized RFID key
Command Description
This command is used to delete the specified Authorized RFID key.
Syntax
Write Command (P41,<Action>,<Delete>,<Total number of RFID keys deleted>,< Unlock RFID keys…>)
Response (<Unit ID>,P41,<Delete>,<Total RFID keys in Flash>)
Parameter Description
Parameters Description Value Range Default
<Action> 1 indicates Write the Parameters 1
<Delete> delete the specified Authorized RFID key. 2 indicates
Delete
2
<Total number of RFID
keys deleted>
The total number of RFID keys deleted in this
operation. Delete up to 20 RFID keys at a time
1~20
<Unlock RFID keys…> The range of RFID key card number is:
0000000001~4294967295, no more than 10
Numbers, otherwise the input is considered invalid.
0000000001~
4294967295
<Total RFID keys in
Flash>
Total RFID keys remaining in Flash after deleted. 0~50
Example
Delete RFID keys: 0013953759,0013953758,0013953757
Write Command (P41,1,2,3,0013953759,0013953758,0013953757)
Response (**********,P41,2,47)
Response Description Content Description
********** Unit ID
P41 Command Word
2 Delete operation
47 Total 47 RFID keys remaining in Flash after
deleted
Delete 10 Authorized RFID keys
Write Command Note: When the instruction is actually sent, there should be no line breaks in the content of the
instruction. The newline in the example is because of the need for document layout
Delete 10 Authorized RFID keys
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 60 / 89
(P41,1,2,10,0000000021,0000000062,0000000063,0000000064,0000000065,0000000066,0000000067,
0000000068,0000000069,0000000070)
Response (**********,P41,2,23)
Response Description Content Description
********** Unit ID
P41 Command Word
2 Delete operation
23 Total 23 RFID keys remaining in Flash after
deleted
Sending Command Channel
GPRS SMS USB
P41 - Delete All Authorized RFID keys in Device
Command Description
This command is used to delete the Authorized RFID keys stored in device at once.
Syntax
Write Command (P41,<Action>,<Delete All>)
Response (<Unit ID>,P41,<Delete>,<Total RFID keys in Flash>)
Parameter Description
Parameters Description Value Range Default
<Action> 1 indicates Write the Parameters 1
<Delete All> delete all Authorized RFID keys. 3 indicates Delete All 3
<Total RFID keys in
Flash>
Total RFID keys remaining in Flash after deleted 0
Example
Delete All Authorized RFID keys
Write Command (P41,1,3)
Response (**********,P41,3,0)
Response Description Content Description
********** Unit ID
P41 Command Word
3 Delete All
0 Total 0 RFID keys remaining in Flash after
deleted
Sending Command Channel
GPRS SMS USB
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 61 / 89
P42 - Batch-Add Unlocking Authorized RFID
Command Description
This command is used to start batch-add unlocking authorized RFID.
Syntax
Write Command (P42,<Start/End>)
Response (<Unit ID>,P42,< Start/End>)
Parameter Description
Parameters Description Value Range Default
< Start/End > 0 end batch-add
1 Start batch-add
0~1
Example
start batch-add unlocking authorized RFID
Write Command Start (P42,1)
Response (**********,P42,1)
Response Description Content Description
********** Unit ID
P42 Command Word
1 start batch-add
Set Working mode to Real-time tracking mode(End batch-add)
Write Command End (P42,0)
Response (**********,P42,0)
Response Description Content Description
********** Unit ID
P42 Command Word
0 end batch-add
Sending Command Channel
GPRS SMS USB
P43 - Unlock the device by Password
Command Description
This command is used to unlock device by password.
Default password is 888888
Syntax
Write Command (P43,<Password>)
Response (<Unit ID>,P43,<Successful/failed>,<Wrong password times>)
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 62 / 89
Parameter Description
Parameters Description Value Range Default
<Password> The password to unlock this device.The password is
fixed to a 6 arbitrary combination of characters.
6 characters 888888
<successful/failed> 1 indicates unlock successfully; 0 indicates failed 0~1
<Wrong password times> The number of times the unlock command was sent
with the wrong password. More than 5 times, the
device will trigger wrong password alert. This value is
reset to 0 when the correct password is successfully
entered.
Example
Unlock device by password
Write Command (P43,888888)
Response (**********,P43,1,0)
Response Description Content Description
********** Unit ID
P43 Command Word
1 1 indicates unlock successfully;
0 indicates failed
0 The number of times the unlock command
was sent with the wrong password is 0.
This value is reset to 0 when the correct
password is successfully entered.
Sending Command Channel
GPRS SMS USB
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 63 / 89
P44 - Change the Unlock Password
Command Description
This command is used to change the unlock password.
Default password is 888888
Syntax
Write Command (P44,<New Password>,<Old Password>)
Response (<Unit ID>,P44,<Successful/failed>)
Parameter Description
Parameters Description Value Range Default
<New Password> The new password to unlock this device. The
password is fixed to a 6 arbitrary combination of
characters.
6 characters
<Old Password> The new password to unlock this device. The
password is fixed to a 6 arbitrary combination of
characters.
6 characters 888888
<successful/failed> 1 indicates change password successfully; 0
indicates failed
0~1
Example
Change the unlock password
Write Command (P44,12#aAM,888888)
Response (**********,P44,1)
Response Description Content Description
********** Unit ID
P44 Command Word
1 1 indicates change password successfully;
0 indicates failed
Sending Command Channel
GPRS SMS USB
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 64 / 89
P46 - Acknowledge Command to receive Lock or Unlock Report
Command Description
This command is used to acknowledge the Lock and Unlock Report data at GPS platform.
Note: The device will keep on sending the same Lock and Unlock Report data if didn't receive this acknowledge command
from GPS platform .
Syntax
Write Command (P46)
Response (<Unit ID>,P46)
Parameter Description
Parameters Description Value Range Default
- -
Example
GPS platform send below command to acknowledge the Lock and Unlock Report data from device side
Write Command (P46)
Response (**********,P46)
Response Description Content Description
********** Unit ID
P46 Command Word
Sending Command Channel
GPRS SMS USB
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 65 / 89
P50 - Enable/Disable the Power Switch on Device Mainboard
Command Description
This command is used to enable or disable the power switch on device mainboard.
After Disable power switch function, The device can’t be shut down by this power switch.
Syntax
Read Command (P50,<Action>)
Response (<Unit ID>,P50,<Enable/Disable Power Switch >)
Write Command (P50,<Action>,<Enable/Disable Power Switch>)
Response (<Unit ID>,P50,<Enable/Disable Power Switch >)
Parameter Description
Parameters Description Value Range Default
<Action> 0 Query the previous setting
1 Write the Parameters
0~1
<Enable/Disable Power
Switch >
0 indicates Disable Power Switch function, The
device can’t be power off by this power switch.;
1 indicates Enable Power Switch function
0~1 1
Example
Query Power Switch Status
Read Command (P50,0)
Response (**********,P50,1)
Response Description Content Description
********** Unit ID
P50 Command Word
1 Enable Power Switch function
Disable Power Switch on device Mainboard
Write Command (P50,1,0)
Response (**********,P50,0)
Response Description Content Description
********** Unit ID
P50 Command Word
0 Disable Power Switch function
Sending Command Channel
GPRS SMS USB
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 66 / 89
P58 - Set/Query RFID unlocking related to geo-fence
Command Description
This command is used to set/query RFID unlocking related to geo-fence (Default value is 1)
Syntax
Read Command (P50,<Action>)
Response (<Unit ID>,P58,<Enable/Disable related to geo-fence >)
Write Command (P50,<Action>,<Enable/Disable related to geo-fence >)
Response (<Unit ID>,P58,<Enable/Disable related to geo-fence >)
Parameter Description
Parameters Description Value Range Default
<Action> 0 Query the previous setting
1 Write the Parameters
0~1
<Enable/Disable related to
geo-fence >
0 Not related to geo-fence(the dvicean be unlocked
by RFID any where);
1 Related to geo-fence(the dvicean be unlocked by
RFID only as its in the go-fence)
0~1 1
Example
Query Power Switch Status
Read Command (P58,0)
Response (**********,P58,1)
Response Description Content Description
********** Unit ID
P58 Command Word
1 Related to geo-fence(the dvicean be unlocked
by RFID only as its in the go-fence)
Disable Related To Geo-fence
Write Command (P58,1,0)
Response (**********,P58,0)
Response Description Content Description
********** Unit ID
P58 Command Word
0 Not related to geo-fence(the dvicean be
unlocked by RFID any where)
Sending Command Channel
GPRS SMS USB
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 67 / 89
P97 - Set/Query Data Acknowledgement Mechanism
Command Description
This command is used to change the data acknowledgement mechanism. By default, all alert data and lock/unlock report -
(P45) require the GPS platform to acknowledge them, otherwise the data will continue to be sent. With this command, you can
configure these data without platform confirmation, or configure the maximum number of reports when the platform does not
respond correctly.
Syntax
Read Command (P97,<Action>)
Response (<Unit ID>,P97,<Data type>,<Enable/Disable>,<Repeat times>)
Write Command (P97,<Action>,<Data type>,<Enable/Disable>,<Repeat times>)
Response (<Unit ID>,P97,<Data type>,<Enable/Disable>,<Repeat times>)
Parameter Description
Parameters Description Value Range Default
<Action> 0 Query the previous setting
1 Write the Parameters
0~1
<Data type> 0 indicates Real time Position data
1 indicates Blind zone data
2 indicates Alert data
 Refer to Section 7.1 Position and Alert data No.5
3 indicates Lock and Unlock report-P45
0~3
<Enable/Disable> 1 Indicates Enable acknowledgement mechanism.
i.e. Set this data type require acknowledgement
from GPS platform.
0 indicates Disable acknowledgement mechanism.
i.e. Set this data type doesn’t require
acknowledgement from GPS platform. If this value
is 0, the <Repeat times> parameter can be ignored
0~1
<Repeat times> 0 means Repeat this report all the time if device do
not receive the correct response command;
1~127 means Repeat Specified times, and then
report the next data.
0~127
Example
Query Blind zone data’s data acknowledgement mechanism setting
Read Command (P97,0,1)
Response (**********,P97,1,0,0)
Response Description Content Description
********** Unit ID
P97 Command Word
1 Data type: Blind zone data
0 Disable acknowledgement mechanism
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 68 / 89
If this value is 0, the <Repeat times>
parameter can be ignored
0 Repeat times
Query Lock and Unlock data’s data acknowledgement mechanism setting
Read Command (P97,0,3)
Response (**********,P97,3,1,0)
Response Description Content Description
********** Unit ID
P97 Command Word
3 Data type: Lock and Unlock data
1 Enable acknowledgement mechanism
0 Repeat this report all the time if device do not
receive the correct response command
Disable Alert data acknowledgement mechanism
Write Command (P97,1,2,0,0)
Response (**********,P97,2,0,0)
Response Description Content Description
********** Unit ID
P97 Command Word
2 Data type: Alert data
0 Disable acknowledgement mechanism.
If this value is 0, the <Repeat times>
parameter can be ignored
0 Repeat times
Sending Command Channel
GPRS SMS USB
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 69 / 89
P99 - Firmware Upgrade over the air
Command Description
This command is used to upgrade the device firmware the air. Need to contact sales to confirm the correct IP address and port
Syntax
Write Command (P99,<OTA server IP>,<OTA server port>)
Response (<Unit ID>,P99)
Parameter Description
Parameters Description Value Range Default
<OTA server IP> Host Server IP that deploy OTA server software and
specified firmware file
**************
<OTA server port> UDP port. XXXX
Example
Query Power Switch Status
Write Command (P99,**************,1234)
Response (**********,P99)
Response Description Content Description
********** Unit ID
P99 Command Word
Sending Command Channel
GPRS SMS USB
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 70 / 89
P100 - Query device real-time status and GSM module version
Command Description
This command is used to query device real-time status and GSM module version(2G/3G/4G)
Syntax
Read Command (P100)
Response (<Unit ID>,P100,<status data...>)
Parameter Description
Parameters Description Value Range Default
/ / / /
Example
Query device real-time status and GSM module version
Read Command (P100)
Response (**********,P100,SIM:1,CG:1,CSQ:22,MV:EC200AEUHAR01A19M16,MS:7,IP:**************,1234,Loc:1,
Sat:11,Lock:1,Mil:10546,His:0,Rc:0,Volt:4082,90%,Chg:0,Tmp:37)
Response
Description
Content Description
********** Unit ID
P100 Command Word
SIM Working SIM card slot:
1 mean’s SIM Card slot 1 is working. The value is 1 or 2
CG Network registration status
1: registered local network
2: unregistered, in search
3; registration rejected
4: unknown
5: registered roamed
CSQ Received signal strength indication
MV GSM module version:
this is Quectel 4G module EC200A-EU
MS Mobile Status (Online mode)
0: The module is powered on for the first time
1: Module power off and restart
2: AT command initialization
3: Network check
4: Dialing
5: Network connection
6: Login
7: Online
8: Offline
IP Upload data to this IP/Port
IP:************** Port:1234
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 71 / 89
Loc Location status:
1: Located
0: Not located
Sat Number of satellites
Lock Lock status:
0: Unknown
1: Locked
2: Unlocked
3: Fault
Mil Currently mileage (unit is meter)
His The number of remaining blind zone replenishment data
stored by the current device
Rc The number of remaining blind zone P45(lock/unlock data)
stored by the current device
Volt Current battery voltage (the unit is millivolts) and Percentage
of electricity
Chg Whether charging status
1: Charging
0: Not charging
Tmp Current MCU temperature (the unit is Celsius)
Sending Command Channel
GPRS SMS USB
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 72 / 89
P106 - Set/Query Can Unlock In The polygon Geo-fence
Command Description
This command can query or set whether unlocked by swipe RFID card in the specified Geo-fence.
The total number of GL600 configurable polygon Geo-fence is 10 pcs. Please refer to commands P24,P29,P30,P31.
Syntax
Read Command (P106,<Action>)
Response (<Unit ID>,P106,<Geo-fence 1 Enable/Disable>,...<Geo-fence 10 Enable/Disable>)
Write Command (P106,<Action>,<Geo-fence 1 Enable/Disable>,...<Geo-fence 10 Enable/Disable>)
Response (<Unit ID>,P106, <Geo-fence 1 Enable/Disable>,...<Geo-fence 10 Enable/Disable>)
Parameter Description
Parameters Description Value Range Default
<Action> 0 Query the previous setting
1 Write the Parameters
0~1
<POI 1
Enable/Disable>,...<POI
100 Enable/Disable>
0 Disable
1 Enable
0~1
Example
Query can unlock in the specified Geo-fence
Read Command (P106,0)
Response (**********,P106,1,0,0,0,1,0,0,0,0,1)
Response Description Content Description
********** Unit ID
P106 Command Word
1 swipe RFID only unlock in Geo-fence 1
0 swipe RFID can’t unlock in Geo-fence 2
0 swipe RFID can’t unlock in Geo-fence 3
0 swipe RFID can’t unlock in Geo-fence 4
1 swipe RFID only unlock in Geo-fence 5
0 swipe RFID can’t unlock in Geo-fence 6
0 swipe RFID can’t unlock in Geo-fence 7
0 swipe RFID can’t unlock in Geo-fence 8
0 swipe RFID can’t unlock in Geo-fence 9
1 swipe RFID only unlock in Geo-fence 10
Set can unlock in the specified Geo-fence
Write Command (P106,1,1,1,1,1,1,1,1,1,1,1)
Response (**********,P106,1,1,1,1,1,1,1,1,1,1)
Response Description Content Description
********** Unit ID
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 73 / 89
P106 Command Word
1 swipe RFID only unlock in Geo-fence 1
1 swipe RFID only unlock in Geo-fence 2
1 swipe RFID only unlock in Geo-fence 3
1 swipe RFID only unlock in Geo-fence 4
1 swipe RFID only unlock in Geo-fence 5
1 swipe RFID only unlock in Geo-fence 6
1 swipe RFID only unlock in Geo-fence 7
1 swipe RFID only unlock in Geo-fence 8
1 swipe RFID only unlock in Geo-fence 9
1 swipe RFID only unlock in Geo-fence 10
Sending Command Channel
GPRS SMS USB
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 74 / 89
P108 - Set/Query Can Unlock In The POI(Point of Interest)
Command Description
This command can query or set whether unlocked by swipe RFID card in the specified POI.
The total number of GL600 configurable POI is 100 pcs.
Syntax
Read Command (P108,<Action>)
Response (<Unit ID>,P108, <Total number of POIs>,<unlock status>,<Radius>)
Write Command (P108,<Action>,<unlock status>,<Radius POI 1 lng lat POI2 lng lat ... POI100 lng lat>)
Response (<Unit ID>,P108, <Total number of POIs>,<unlock status>,<Radius>)
Parameter Description
Parameters Description Value Range Default
<Action> 0 Query the previous setting
1 Write the Parameters
0~1
<unlock status> 0 Disable
1 Enable
0~1
<Radius POI 1
lng lat POI2
lng lat ...
POI100 lng lat>
Radius range: 0-FFFF, two bytes, unit is meter
Longitude and latitude string: 4 bytes per longitude, 4 bytes per
latitude, appearing in pairs, a total of up to 100 pairs
Note: From the beginning of the radius to the end longitude latitude
pairs, all are in hexadecimal notation, with no sign in the middle.
Example
Query can unlock in the specified POI
Read Command (P108,0)
Response (**********,P108,91,0,65535)
Response
Description
Content Description
********** Unit ID
P108 Command Word
91 The total number of POIs that have been set
0 swipe RFID can’t unlock in POIs, can unlock by GPRS command
65535 Radius is 65535 meters
Set can unlock in the specified POI
Write Command (P108,1,1,FFFFFB42AA45008984E0FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 75 / 89
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF)
Command in hex format:
28503130382C312C312CFFFFFB42AA45008984E0FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
FFF29
Response (**********,P108,1,1,65535)
Response
Description
Content Description
********** Unit ID
P108 Command Word
1 The total number of POIs that have been set
1 swipe RFID only unlock in POIs
65535 Radius is 65535 meters
Sending Command Channel
GPRS USB
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 76 / 89
P110 - Set/Query G-Sensor filter function
Command Description
This command is used to query/enable/disable G-sensor’s filter function.
This function is using for mileage calculate.
Syntax
Read Command (P110,<Action>)
Response (<Unit ID>,P110,<Vibration Level>,<Vibration Keep Time>,<Rest Keep Time>,<Enable/Disable>)
Write Command (P110,<Action>,<Vibration Level>,<Vibration Keep Time>,<Rest Keep Time>,<Enable/Disable>)
Response (<Unit ID>,P110,<Vibration Level>,<Vibration Keep Time>,<Rest Keep time>,<Enable/Disable>)
Parameter Description
Parameters Description Value Range Default
<Action> 0 Query the previous setting
1 Write the Parameters
0~1
<Vibration Level> unit is mg 60~120 80
<Vibration Keep Time> unit is seconds 1~255 5
<Rest Keep time> unit is seconds 1~255 30
<Enable/Disable> 1 Indicates Enable
0 indicates Disable
0~1 0
Example
Query G-Sensor filter function’s parameter
Read Command (P110,0)
Response (**********,P110,80,5,30,0)
Response Description Content Description
********** Unit ID
P110 Command Word
80 Vibration Level is 80mg
5 Vibration Keep Time is 5 seconds
30 Rest Keep time is 30 seconds
0 Disable this function
Enable G-Sensor filter function
Write Command (P110,1,80,5,30,1)
Response (**********,P110,80,5,30,1)
Response Description Content Description
********** Unit ID
P110 Command Word
80 Vibration Level is 80mg
5 Vibration Keep Time is 5 seconds
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 77 / 89
30 Rest Keep time is 30 seconds
1 Enable this function
Sending Command Channel
GPRS SMS USB
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 78 / 89
P111 - Set/Query Working In Lock/Unlock Mode Reporting Data Time
Interval
Command Description
This command is used to set /query working in Lock/Unlock mode reporting data time interval.
When the lock rope is connected (locked state). Do not sleep, can configure the report interval in the vibration state and the
report interval in the static state. For example, it is reported every 30 seconds when it moves and every 60 seconds when it
stops.
When the lock rope is disconnected (unlocked state), no longer refer to the vibration state, a fixed wake-up value can be
configured, wake-up work for 10 minutes, timing wake up interval range 30 ~ 1440 minutes. Refer to command P04
Syntax
Read Command (P111,<Action>)
Response (<Unit ID>,P111, <Shake Reporting Data Time Interval>,<Static Reporting Data Time Interval>)
Write Command (P111,<Action>,<Shake Reporting Data Time Interval>,<Static Reporting Data Time Interval>)
Response (<Unit ID>,P111, <Shake Reporting Data Time Interval>,<Static Reporting Data Time Interval>)
Parameter Description
Parameters Description Value Range Default
<Action> 0 Query the previous setting
1 Write the Parameters
0~1
<Shake Reporting Data
Time Interval>
unit in seconds 1~65535 30
<Static Reporting Data
Time Interval>
unit in seconds 1~65535 60
Example
query working in Lock/Unlock mode reporting data time interval.
Read Command (P111,0)
Response (**********,P111,30,60)
Response Description Content Description
********** Unit ID
P111 Command Word
30 Shake Reporting Data Time Interval is 30 sec
60 Static Reporting Data Time Interval is 60 sec
set working in Lock/Unlock mode reporting data time interval.
Write Command (P111,1,60,120)
Response (**********,P111,60,120)
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 79 / 89
Response Description Content Description
********** Unit ID
P111 Command Word
60 Shake Reporting Data Time Interval is 60 sec
120 Static Reporting Data Time Interval is 120 sec
Sending Command Channel
GPRS SMS USB
P112 - Enable/Disable Lock Rope Cut Reminder(Blink & Beep)
Command Description
This command is used to enable or disable the lock rope cut reminder (Blink & Beep)
Syntax
Read Command (P112,<Action>)
Response (<Unit ID>,P112,<Enable/Disable reminder>)
Write Command (P112,<Action>,<Enable/Disable reminder>)
Response (<Unit ID>,P112,<Enable/Disable reminder>)
Parameter Description
Parameters Description Value Range Default
<Action> 0 Query the previous setting
1 Write the Parameters
0~1
<Enable/Disable
reminder >
0 indicates Disable reminder function
1 indicates Enable reminder function
0~1 0
Example
Query lock rope cut reminder status
Read Command (P112,0)
Response (**********,P112,0)
Response Description Content Description
********** Unit ID
P112 Command Word
0 Disable lock rope cut reminder function
Enable lock rope cut reminder function
Write Command (P112,1,1)
Response (**********,P112,1)
Response Description Content Description
********** Unit ID
P112 Command Word
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 80 / 89
1 Enable lock rope cut reminder function
Sending Command Channel
GPRS SMS USB
P113 - Enable/Disable Blind Area Data Packaging
Command Description
This command is used to enable or disable the blind area data packaging function.
Syntax
Read Command (P113,<Action>)
Response (<Unit ID>,P113,<Enable/Disable Packaging Function>)
Write Command (P113,<Action>,<Enable/Disable Packaging Function >)
Response (<Unit ID>,P113,<Enable/Disable Packaging Function >)
Parameter Description
Parameters Description Value Range Default
<Action> 0 Query the previous setting
1 Write the Parameters
0~1
<Enable/Disable
reminder >
0 indicates Disable packaging function
1 indicates Enable packaging function
0~1 0
Example
Query blind area data packaging function status
Read Command (P113,0)
Response (**********,P113,0)
Response Description Content Description
********** Unit ID
P113 Command Word
0 Disable blind area data packaging function
Enable blind area data packaging function
Write Command (P113,1,1)
Response (**********,P113,1)
Response Description Content Description
********** Unit ID
P113 Command Word
1 Enable blind area data packaging function
Sending Command Channel
GPRS SMS USB
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 81 / 89
P114 - Enable/Disable Base Station Positioning Function
Command Description
This command is used to enable or disable base station positioning function
This function uses the Quectel server’s base station positioning function, some countries and
regions do not support! which may cause the device to be unstable the TCP link.
Please use it with caution!!!! This function is disable by default!!!
Syntax
Read Command (P114,<Action>)
Response (<Unit ID>,P114,<Enable/Disable base station positioning function>)
Write Command (P114,<Action>,<Enable/Disable base station positioning function>)
Response (<Unit ID>,P114,<Enable/Disable base station positioning function>)
Parameter Description
Parameters Description Value Range Default
<Action> 0 Query the previous setting
1 Write the Parameters
0~1
<Enable/Disable base
station positioning
function>
0 indicates Disable base station positioning function
1 indicates Enable base station positioning function
0~1 0
Example
Query blind area data packaging function status
Read Command (P114,0)
Response (**********,P114,0)
Response Description Content Description
********** Unit ID
P114 Command Word
0 Disable base station positioning function
Enable blind area data packaging function
Write Command (P114,1,1)
Response (**********,P114,1)
Response Description Content Description
********** Unit ID
P114 Command Word
1 Enable base station positioning function
Sending Command Channel
GPRS SMS USB
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 82 / 89
P122 - Enable/Disable Bluetooth Function
Command Description
This command is used to enable or disable the Bluetooth function.
Syntax
Read Command (P122,<Action>)
Response (<Unit ID>,P122,<Enable/Disable Bluetooth Function>)
Write Command (P122,<Action>,<Enable/Disable Bluetooth Function >)
Response (<Unit ID>,P122,<Enable/Disable Bluetooth Function >)
Parameter Description
Parameters Description Value Range Default
<Action> 0 Query the previous setting
1 Write the Parameters
0~1
<Enable/Disable
reminder >
0 indicates Disable Bluetooth function
1 indicates Enable Bluetooth function
0~1 0
Example
Query blind area data packaging function status
Read Command (P122,0)
Response (**********,P122,0)
Response Description Content Description
********** Unit ID
P122 Command Word
0 Disable Bluetooth function
Enable blind area data packaging function
Write Command (P122,1,1)
Response (**********,P122,1)
Response Description Content Description
********** Unit ID
P122 Command Word
1 Enable Bluetooth function
Sending Command Channel
GPRS SMS USB
P123 - Change the Bluetooth Encryption Key
Command Description
This command is used to change the Bluetooth communication encryption key of the algorithm.
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 83 / 89
Default key is imei’s last ten number
Syntax
Write Command (P123,< encryption key>)
Response (<Unit ID>,P123,<Successful/failed>)
Parameter Description
Parameters Description Value Range Default
< encryption key> The encryption key of the algorithm 6 characters
<successful/failed> 1 indicates change encryption key successfully
0 indicates change encryption key failed
0~1
Example
Change he Bluetooth communication secret key of the algorithm
Write Command (P123,12345678)
Response (**********,P123,1)
Response Description Content Description
********** Unit ID
P123 Command Word
1 1 indicates change encryption key successfully
0 indicates change encryption key failed
Sending Command Channel
GPRS SMS USB
P125 – Extract Data From Micro USB/Bluetooth (need firmware version
‘GL600E_........’)
Command Description
This command is used to extract data from micro usb or bluetooth.
Firmware version must be in ‘GL600E_........’ can support this function.
Syntax
Command Read All Data: (P125,<Data Type>,<To USB/BT>,<Action>)
Time Period Read: (P125,<Data Type>,<To USB/BT>,<Action>,<Begin Time>,<End Time>)
Response Return different data according to the combination of write command parameters:
(<Unit ID>,P125,<Data Type>,<Count>)
(<Unit ID>,P125,<Data Type>,<Comma-separated data>)
Parameter Description
Parameters Description Value
Range
Default
< Data Type > 0 Position Data
1 Alert Data
2 Lock and Unlock Report
0 ~ 2
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 84 / 89
< To USB/BT > 0 Micro USB (TTL)
1 Bluetooth
0 ~ 1
< Action > 0 Read Summary
1 Read Data
0 ~ 1
<Begin Time > Format is Day Month Year Hour Minute Second:
DDMMYYHHNNSS
e.g. 301019000000 = 2019-10-30 00:00:00
< End Time > Format is Day Month Year Hour Minute Second:
DDMMYYHHNNSS
e.g. 301019235959 = 2019-10-30 23:59:59
< Count > Maximum Number of storages:
0 Position Data: 28000 PCS
1 Alert Data: 2800 PCS
2 Lock and Unlock Report: 2400 PCS
<Comma-separ
ated data>
Position Data:
(8033613486,P125,1,17,111019130526,22707700,114264830,15,1,80,20189,8,0,8192,8
5,4362,9369,22,0,0,460,0,163)
<Protocol Version> 1
<Data Type> 17 (DEC) Position Data
<Datetime> 111019130526 DDNNYYHHNNSS
<Latitude> 22707700 DD.DDDDDD 22707700/1000000 = 22.707700
<Longitude> 114264830 DDD.DDDDDD 114264830/1000000 = 114.264830
<Latitude/Longitude Direction> 15 (DEC) 15 -> 0xF -> 1111
<Speed> 1 (Unit is KM/H)
<Direction> 80 (0~360)
<Mileage> 20189 (Unit is KM)
<SAT Count> 8 Number of satellites (DEC)
< Binding ID> 0 (DEC)
<Status> 8192 (DEC) 8192->0x2000
<Battery> 85 (85%) (DEC)
<Cell ID> 4362 (DEC)
<LAC> 9369 (DEC)
<CSQ> 22 Received Signal Strength Indication
<Geo-ID> 0 (DEC)
<Cell ID High>0 (DEC)
<MCC> 460 (DEC)
<MNC> 0 (DEC)
<SN> 163 serial number (DEC)
Alert Data:
(8033613486,P125,1,18,111019071350,22707036,114264748,15,65,0,20199,8,0,8448,8
5,4511,9368,17,0,0,460,0,225)
<Protocol Version> 1
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 85 / 89
<Data Type> 18 (DEC) Alert Data
<Datetime> 111019071350 DDNNYYHHNNSS
<Latitude> 22707036 DD.DDDDDD 22707036/1000000 = 22.707036
<Longitude> 114264748 DDD.DDDDDD 114264748/1000000 = 114.264748
<Latitude/Longitude Direction> 15 (DEC) 15 -> 0xF -> 1111
<Speed> 65 (Unit is KM/H)
<Direction> 0 (0~360)
<Mileage> 20199 (Unit is KM)
<SAT Count> 8 Number of satellites (DEC)
< Binding ID> 0 (DEC)
<Status> 8448 (DEC) 8448->0x2100
<Battery> 85 (85%) (DEC)
<Cell ID> 4511 (DEC)
<LAC> 9368 (DEC)
<CSQ> 17 Received Signal Strength Indication
<Geo-ID> 0 (DEC)
<Cell ID High>0 (DEC)
<MCC> 460 (DEC)
<MNC> 0 (DEC)
<SN> 255 serial number (DEC)
Lock And Unlock Report Data:
(8033613486,P45,121019,022530,22.706768,N,114.265245,E,A,0,0,4,1,0000000000,1,0,
0)
Example 1
Read Summary (Position Data)
Command (P125,0,0,0)
Response (8033613486,P125,0,1889)
Response
Description
Content Description
8033613486 Unit ID
P125 Command Word
0 Position Data Type
1889 count
Example 2
Read Summary (Alert Data)
Command (P125,1,0,0)
Response (8033613486,P125,1,13)
Response
Description
Content Description
8033613486 Unit ID
P125 Command Word
1 Alert Data Type
13 count
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 86 / 89
Example 3
Read Summary (Lock and Unlock Report Data)
Command (P125,2,0,0)
Response (8033613486,P125,2,1)
Response
Description
Content Description
8033613486 Unit ID
P125 Command Word
2 Alert Data Type
1 count
Example 4
Read All Data (Position Data)
Command (P125,0,0,1)
Response (8033613486,P125,0,1951)
(8033613486,P125,1,17,311200000030,0,0,14,0,0,0,0,0,8192,5,0,0,20,0,0,0,0,1)
(8033613486,P125,1,17,311200000100,22709528,114265182,14,0,0,0,0,0,8193,5,4512,9368,20,0,0,460,0,2)
(8033613486,P125,1,17,311200000130,22709528,114265182,14,0,0,0,0,0,8193,5,4512,9368,20,0,0,460,0,3)
...
Example 5
Read All Data (Alert Data)
Command (P125,1,0,1)
Response (8033613486,P125,1,14)
(8033613486,P125,1,18,111019071350,22707036,114264748,15,0,0,0,8,0,8448,85,4511,9368,17,0,0,460,0,225)
(8033613486,P125,1,18,111019051350,22706915,114264883,15,0,0,0,7,0,8448,85,4511,9368,17,0,0,460,0,240)
...
Example 6
Read All Data (Lock and Unlock Report Data)
Command (P125,2,0,1)
Response (8033613486,P125,2,1)
(8033613486,P45,121019,022530,22.706768,N,114.265245,E,A,0,0,4,1,0000000000,1,0,0)
Example 7
Time Period Read Data (Position Data)
Command (P125,0,0,2,121019000000,121019105959)
Response (8033613486,P125,0,121019000000,121019105959)
(8033613486,P125,1,17,121019010532,22703859,114256226,14,0,0,0,0,0,8193,20,4362,9369,22,0,0,460,0,10)
(8033613486,P125,1,17,121019010602,22703859,114256226,14,0,0,0,3,0,8193,20,4362,9369,21,0,0,460,0,11)
(8033613486,P125,1,17,121019010632,22703859,114256226,14,0,0,0,0,0,8193,20,4362,9369,20,0,0,460,0,12)
...
Example 8
Time Period Read Data (Alert Data)
Command (P125,1,0,2,121019070000,121019105959)
Response (8033613486,P125,1,121019070000,121019105959)
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 87 / 89
(8033613486,P125,1,18,121019071949,22707533,114264901,15,0,0,0,8,0,8448,15,4053,9369,18,0,0,460,0,242)
(8033613486,P125,1,18,121019075622,22706748,114263564,14,0,0,0,0,0,10240,20,0,0,0,0,0,0,0,0)
(8033613486,P125,1,18,121019093001,22707261,114264810,14,0,0,0,0,0,10240,10,0,0,0,0,0,0,0,0)
Example 9
Time Period Read Data (Lock and Unlock Report Data)
Command (P125,2,0,2,121019000000,121019035959)
Response (8033613486,P125,2,121019000000,121019035959)
(8033613486,P45,121019,022530,22.706768,N,114.265245,E,A,0,0,4,1,0000000000,1,0,0)
Sending Command Channel
USB Bluetooth
P126 - Clear blind spot data
Command Description
This command is used to clear blind spot data.
When modifying IP, port, APN, ID information, the blind area will be cleared.
Syntax
Write Command (P126)
Response (<Unit ID>,P126,<Successful/failed>)
Parameter Description
Parameters Description Value Range Default
<successful/failed> 1 successfully
0 failed
0~1
Example
Change he Bluetooth communication secret key of the algorithm
Write Command (P126,1)
Response (**********,P126,1)
Response Description Content Description
********** Unit ID
P126 Command Word
1 1 successfully
0 failed
Sending Command Channel
GPRS SMS USB
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 88 / 89
P127 - Extended power saving solution: turn off GPS module in idle
time
Command Description
This command is used to turn off the GPS module in idle time to save power.
Syntax
Command (P127,<Action>,< turn off GPS module time >)
(P127,1,10)
Response (<Unit ID>,P127,<turn off GPS module time>)
Parameter Description
Parameters Description Value Range Default
< Action > 0 Read
1 Set
0 ~ 1
<turn off
GPS module
time>
10: If the upload interval is 30 seconds, after uploading the data, turn off the
GPS 10 seconds.
If the upload interval is 0 seconds, mean’s disable this function.
0~590 0
Example 1
Enable the GPS off function, the GPS off time is 10 seconds, if the upload interval is 30 seconds, after uploading the
data, turn off the GPS 10 seconds.
Command (P127,1,10)
Response (8033613486,P127,10)
Response
Description
Content Description
8033613486 Unit ID
P127 Command Word
10 Turn off GPS module time
is 10 seconds
Example 2
Disable the GPS off function
Command (P127,1,0)
Response (8033613486,P127,0)
Response
Description
Content Description
8033613486 Unit ID
P127 Command Word
0 Disable this function
Example 3
Query the GPS off function
Command (P127) or (P127,0)
Response (8033613486,P127,10)
Response Content Description
 Mobicom Telematics – GL600 Protocol Manual
Copyright © 2019 Shenzhen Mobicom Telematics Co.,Ltd.All rights reserved. 89 / 89
Description 8033613486 Unit ID
P127 Command Word
10 Turn off GPS module time
is 10 seconds
Sending Command Channel
GPRS SMS USB
P128 – Query Bluetooth Mac Address & Name & Version
Command Description
This command is used to query Bluetooth mac address & name & version. If the return is empty, it means that there is
no Bluetooth module in the lock.
Syntax
Write Command (P128)
Response (<Unit ID>,P128,<Bluetooth mac address>,<Bluetooth name>,<Bluetooth version>)
Parameter Description
Parameters Description Value Range Default
Example
Query Bluetooth Mac Address & Name & Version
Command (P128)
Response (6020110481,P128,3CA5197B16C2,GL600_6020110481,JDY-16-V2.3)
Response Description Content Description
6020110481 Unit ID
P128 Command Word
3CA5197B16C2 Bluetooth mac address
GL600_6020110481 Bluetooth name
JDY-16-V2.3 Bluetooth version
Sending Command Channel
GPRS SMS USB