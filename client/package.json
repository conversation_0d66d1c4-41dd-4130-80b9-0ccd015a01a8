{"name": "client", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@maptiler/sdk": "^3.7.0", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@tailwindcss/vite": "^4.1.10", "better-auth": "^1.3.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "leaflet": "^1.9.4", "lucide-react": "^0.507.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-leaflet": "^5.0.0", "react-router-dom": "^7.7.0", "server": "workspace:*", "shared": "workspace:*", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.10"}, "devDependencies": {"@eslint/js": "^9.28.0", "@types/leaflet": "^1.9.20", "@types/node": "^22.15.31", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "autoprefixer": "^10.4.21", "eslint": "^9.28.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "postcss": "^8.5.6", "tw-animate-css": "^1.3.4", "typescript": "~5.7.3", "typescript-eslint": "^8.34.0", "vite": "^6.3.5"}}