import React from 'react';
import { Input } from '../../../components/ui/input';
import { Button } from '../../../components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../../components/ui/table';
import { Download, Save, Upload, Filter, List, Columns } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../components/ui/select';

const Laporan: React.FC = () => {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 mb-1">La<PERSON>an</h1>
      </div>

      <div className="bg-white p-4 rounded-lg border space-y-4">
        <div className="flex flex-wrap items-center gap-4">
          <Select>
            <SelectTrigger className="w-full sm:w-auto">
              <SelectValue placeholder="-PILIH NOMOR E-SEAL" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1">7848585252</SelectItem>
              <SelectItem value="2">9992458585</SelectItem>
            </SelectContent>
          </Select>
          <Input type="date" className="w-full sm:w-auto" />
          <Button className="bg-slate-800 hover:bg-slate-700 text-white">
            <Download className="w-4 h-4 mr-2" />
            Download
          </Button>
          <Button variant="outline">
            <Save className="w-4 h-4 mr-2" />
            Save
          </Button>
          <div className="flex items-center space-x-2">
            <input type="checkbox" id="exportAddress" />
            <label htmlFor="exportAddress" className="text-sm">Export with Address</label>
          </div>
        </div>
        <div className="flex justify-end items-center gap-2">
            <Button variant="ghost" size="sm"><List className="w-4 h-4" /></Button>
            <Button variant="ghost" size="sm"><Columns className="w-4 h-4" /></Button>
            <Button variant="ghost" size="sm"><Filter className="w-4 h-4" /></Button>
        </div>
      </div>

      <div className="bg-white rounded-lg border overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50">
                <TableHead className="font-semibold text-slate-700">No</TableHead>
                <TableHead className="font-semibold text-slate-700">No E-Seal</TableHead>
                <TableHead className="font-semibold text-slate-700">No Aju</TableHead>
                <TableHead className="font-semibold text-slate-700">No IMEI</TableHead>
                <TableHead className="font-semibold text-slate-700">Event</TableHead>
                <TableHead className="font-semibold text-slate-700">Count</TableHead>
                <TableHead className="font-semibold text-slate-700">Duration</TableHead>
                <TableHead className="font-semibold text-slate-700">Mileage</TableHead>
                <TableHead className="font-semibold text-slate-700">Max Speed(Km/h)</TableHead>
                <TableHead className="font-semibold text-slate-700">Avg Speed(Km/h)</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow>
                <TableCell colSpan={10} className="text-center py-8 text-gray-500">
                  Data Tidak Ditemukan
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
};

export default Laporan;
