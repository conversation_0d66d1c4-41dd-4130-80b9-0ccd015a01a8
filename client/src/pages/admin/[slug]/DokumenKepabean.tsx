import React from 'react';
import { Input } from '../../../components/ui/input';
import { Button } from '../../../components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../../components/ui/table';
import { Search } from 'lucide-react';

const Do<PERSON><PERSON>Kepabean: React.FC = () => {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 mb-1"><PERSON><PERSON><PERSON>an</h1>
      </div>

      <div className="flex items-center gap-3 bg-white p-4 rounded-lg border">
        <label htmlFor="nomorAju" className="text-sm font-medium text-gray-700">
          Masukkan Nomor Aju
        </label>
        <div className="relative flex-grow max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input id="nomorAju" placeholder="Nomor Aju" className="pl-10" />
        </div>
        <Button className="bg-slate-800 hover:bg-slate-700 text-white">
          Ambil Data
        </Button>
      </div>

      <div className="bg-white rounded-lg border overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50">
                <TableHead className="font-semibold text-slate-700">Nomor Aju</TableHead>
                <TableHead className="font-semibold text-slate-700">Kode Dokumen</TableHead>
                <TableHead className="font-semibold text-slate-700">Nomor Daftar</TableHead>
                <TableHead className="font-semibold text-slate-700">Tanggal Daftar</TableHead>
                <TableHead className="font-semibold text-slate-700">Kode Kantor</TableHead>
                <TableHead className="font-semibold text-slate-700">Nama Kantor</TableHead>
                <TableHead className="font-semibold text-slate-700">Kode TPS</TableHead>
                <TableHead className="font-semibold text-slate-700">Nama Gudang</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {/* Example Row */}
              <TableRow className="hover:bg-gray-50">
                <TableCell>222</TableCell>
                <TableCell>263722</TableCell>
                <TableCell>BB4627</TableCell>
                <TableCell>2025-01-25</TableCell>
                <TableCell>55</TableCell>
                <TableCell>Kantor ABC</TableCell>
                <TableCell>10</TableCell>
                <TableCell>Gudang Utara</TableCell>
              </TableRow>
              <TableRow>
                <TableCell colSpan={8} className="text-center py-8 text-gray-500">
                  Data akan muncul di sini setelah pencarian.
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
};

export default DokumenKepabean;
