import React from 'react';
import { Input } from '../../../components/ui/input';
import { Button } from '../../../components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../../components/ui/table';

const Status: React.FC = () => {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 mb-1">Tracking Data - Status</h1>
      </div>

      <div className="bg-white p-6 rounded-lg border space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-1">
            <label htmlFor="nomorAju" className="text-sm font-medium text-gray-700">
              Nomor Aju <span className="text-red-500">*</span>
            </label>
            <Input id="nomorAju" placeholder="Masukkan Nomor Aju" />
          </div>
          <div className="space-y-1">
            <label htmlFor="nomorEseal" className="text-sm font-medium text-gray-700">
              Nomor E-Seal <span className="text-red-500">*</span>
            </label>
            <Input id="nomorEseal" placeholder="Masukkan Nomor E-Seal" />
          </div>
        </div>
        <div className="space-y-1">
          <label htmlFor="token" className="text-sm font-medium text-gray-700">
            Token <span className="text-red-500">*</span>
          </label>
          <Input id="token" placeholder="Masukkan Token" />
        </div>
        <p className="text-xs text-gray-500">* Kolom wajib diisi</p>
        <div className="flex justify-end">
          <Button className="bg-slate-800 hover:bg-slate-700 text-white">
            Ambil Data
          </Button>
        </div>
      </div>

      <div className="bg-white rounded-lg border overflow-hidden">
        <div className="p-4 border-b">
          <h2 className="text-lg font-semibold">Nomor E-Seal: -</h2>
        </div>
        <div className="flex">
          <Button variant="ghost" className="flex-1 rounded-none border-r">Start</Button>
          <Button variant="ghost" className="flex-1 rounded-none border-r">Update Position</Button>
          <Button variant="ghost" className="flex-1 rounded-none">Stop</Button>
        </div>
        <div className="overflow-x-auto">
          <Table>
            <TableBody>
              <TableRow>
                <TableCell className="text-center py-8 text-gray-500">
                  Data Masih Kosong
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
};

export default Status;
