import React from 'react';
import { Input } from '../../../components/ui/input';
import { Button } from '../../../components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../../components/ui/table';
import { Search } from 'lucide-react';
import MapTiler from '../../../components/map/MapTiler';

const Monitoring: React.FC = () => {
  const staticLocation = { lat: -6.2088, lng: 106.8456, address: 'Jakarta, Indonesia' }; // Example static location

  return (
    <div className="space-y-6 h-full flex flex-col">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 mb-1">Monitoring</h1>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 flex-grow">
        {/* Left Panel */}
        <div className="lg:col-span-1 bg-white p-4 rounded-lg border flex flex-col space-y-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input placeholder="Cari..." className="pl-10" />
          </div>
          <div className="flex-grow space-y-2 overflow-y-auto">
            {/* Checkbox items */}
            <div className="flex items-center space-x-2">
              <input type="checkbox" id="eseal1" />
              <label htmlFor="eseal1" className="text-sm">7848585252</label>
              <span className="text-xs text-green-600">ONLINE</span>
            </div>
            <div className="flex items-center space-x-2">
              <input type="checkbox" id="eseal2" />
              <label htmlFor="eseal2" className="text-sm">9992458585</label>
              <span className="text-xs text-red-600">OFFLINE</span>
            </div>
            <div className="flex items-center space-x-2">
              <input type="checkbox" id="eseal3" />
              <label htmlFor="eseal3" className="text-sm">7612340022</label>
              <span className="text-xs text-green-600">ONLINE</span>
            </div>
            <div className="flex items-center space-x-2">
              <input type="checkbox" id="eseal4" />
              <label htmlFor="eseal4" className="text-sm">2754585262</label>
              <span className="text-xs text-red-600">OFFLINE</span>
            </div>
          </div>
        </div>

        {/* Right Panel - Map */}
        <div className="lg:col-span-2 bg-white p-4 rounded-lg border">
          <MapTiler originLocation={staticLocation} interactive={false} />
        </div>
      </div>

      <div className="bg-white rounded-lg border overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50">
                <TableHead className="font-semibold text-slate-700">No</TableHead>
                <TableHead className="font-semibold text-slate-700">Icon</TableHead>
                <TableHead className="font-semibold text-slate-700">No E-Seal</TableHead>
                <TableHead className="font-semibold text-slate-700">No IMEI</TableHead>
                <TableHead className="font-semibold text-slate-700">Address</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow>
                <TableCell>1</TableCell>
                <TableCell>Icon</TableCell>
                <TableCell>7848585252</TableCell>
                <TableCell>875298572967967922</TableCell>
                <TableCell>Jalan Pantura, Kendal, Jawa Tengah</TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
};

export default Monitoring;
