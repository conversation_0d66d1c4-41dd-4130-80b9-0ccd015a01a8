import React from 'react';
import { Input } from '../../../components/ui/input';
import { Button } from '../../../components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../../components/ui/table';
import { Search, Plus } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../components/ui/select';

const StartStop: React.FC = () => {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 mb-1">Tracking Data - Start/Stop</h1>
      </div>

      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 bg-white p-4 rounded-lg border">
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-600">Show</span>
          <Select value="10">
            <SelectTrigger className="w-20">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="10">10</SelectItem>
              <SelectItem value="25">25</SelectItem>
              <SelectItem value="50">50</SelectItem>
              <SelectItem value="100">100</SelectItem>
            </SelectContent>
          </Select>
          <span className="text-sm text-gray-600">entries per page</span>
        </div>

        <div className="flex items-center gap-3">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Cari..."
              className="pl-10 w-48 sm:w-64"
            />
          </div>
          <Button className="bg-slate-800 hover:bg-slate-700 text-white">
            <Plus className="w-4 h-4" />
          </Button>
        </div>
      </div>

      <div className="bg-white rounded-lg border overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50">
                <TableHead className="font-semibold text-slate-700">Lokasi Tujuan</TableHead>
                <TableHead className="font-semibold text-slate-700">Jarak Tempuh</TableHead>
                <TableHead className="font-semibold text-slate-700">Nomor IMEI</TableHead>
                <TableHead className="font-semibold text-slate-700">Nomor E-Seal</TableHead>
                <TableHead className="font-semibold text-slate-700">Nomor Kontainer</TableHead>
                <TableHead className="font-semibold text-slate-700">Nomor Polisi</TableHead>
                <TableHead className="font-semibold text-slate-700">Token</TableHead>
                <TableHead className="font-semibold text-slate-700">Ukuran Kontainer</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow>
                <TableCell colSpan={8} className="text-center py-8 text-gray-500">
                  Data Masih Kosong
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>
      </div>

      <div className="flex justify-between items-center text-sm text-gray-600">
        <span>Menampilkan 0 sampai 0 dari 0 entri</span>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" disabled>
            Previous
          </Button>
          <span className="px-3 py-1 bg-slate-800 text-white rounded text-xs">1</span>
          <Button variant="outline" size="sm" disabled>
            Next
          </Button>
        </div>
      </div>
    </div>
  );
};

export default StartStop;
