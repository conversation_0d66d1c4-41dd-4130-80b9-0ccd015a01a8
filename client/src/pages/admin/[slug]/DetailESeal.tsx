import { useState, useEffect, useCallback } from 'react';
import { ArrowLeft, MapPin, Truck, User, Package, Activity, Clock } from 'lucide-react';
import { Button } from '../../../components/ui/button';
import { Badge } from '../../../components/ui/badge';

interface DetailESealProps {
  esealId: string;
  onBack: () => void;
}

interface ESealDetail {
  id: string;
  noEseal: string;
  noImei: string;
  status: string;
  vendor: string;
  merk: string;
  model: string;
  tipe: string;
  
  // Informasi Pengiriman
  nomorAJU: string;
  kodeDokumen: string;
  
  // Informasi Kontainer
  noKontainer: string;
  jnsKontainer: string;
  ukKontainer: string;
  
  // Informasi Kendaraan & Driver
  noPolisi: string;
  namaDriver: string;
  nomorTeleponDriver: string;
  
  // Informasi Lokasi
  alamatAsal: string;
  alamatTujuan: string;
  latitudeAsal: string;
  longitudeAsal: string;
  latitudeTujuan: string;
  longitudeTujuan: string;
  
  // Log Activities
  activities: Array<{
    id: string;
    timestamp: string;
    activity: string;
    status: string;
  }>;

  // GPS Tracking Data
  deviceStatus?: {
    currentStatus: string;
    gpsOnline: boolean;
    lastKnownLat?: string;
    lastKnownLng?: string;
    batteryLevel?: string;
    lastGpsUpdate?: string;
    registeredWithBeacukai: boolean;
  };

  // Tracking Session
  activeSession?: {
    id: string;
    sessionStatus: string;
    startedAt: string;
    totalUpdates: number;
    lastUpdateAt?: string;
  };

  // Recent Position Updates
  recentPositions?: Array<{
    id: string;
    latitude: string;
    longitude: string;
    speed?: string;
    battery?: string;
    createdAt: string;
  }>;
}

export default function DetailESeal({ esealId, onBack }: DetailESealProps) {
  const [esealDetail, setESealDetail] = useState<ESealDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [showFullAddress, setShowFullAddress] = useState(false);
  // const [refreshing, setRefreshing] = useState(false);

  const fetchESealDetail = useCallback(async () => {
    try {
      setLoading(true);

      // Fetch basic E-Seal data
      const response = await fetch(`/api/eseal/${esealId}`);
      if (!response.ok) throw new Error('Failed to fetch E-Seal data');

      const esealData = await response.json();

      // Fetch GPS tracking status
      const trackingResponse = await fetch(`/api/tracking/status/${esealId}`);
      let trackingData = null;
      if (trackingResponse.ok) {
        trackingData = await trackingResponse.json();
      }

      // Combine data
      const combinedData: ESealDetail = {
        ...esealData.data,
        deviceStatus: trackingData?.data?.deviceStatus,
        activeSession: trackingData?.data?.activeSession,
        recentPositions: trackingData?.data?.recentPositions?.slice(0, 10) // Last 10 positions
      };

      setESealDetail(combinedData);
    } catch (error) {
      console.error('Error fetching E-Seal detail:', error);
    } finally {
      setLoading(false);
    }
  }, [esealId]);

  // const refreshGpsData = async () => {
  //   try {
  //     // Test GPS connectivity
  //     const testResponse = await fetch(`/api/tracking/test-gps/${esealId}`, {
  //       method: 'POST'
  //     });

  //     if (testResponse.ok) {
  //       // Refresh the data
  //       await fetchESealDetail();
  //     }
  //   } catch (error) {
  //     console.error('Error refreshing GPS data:', error);
  //   }
  // };

  useEffect(() => {
    fetchESealDetail();
  }, [fetchESealDetail]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (!esealDetail) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-gray-500 mb-4">E-Seal tidak ditemukan</p>
          <Button onClick={onBack} variant="outline">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Kembali
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={onBack}
                className="flex items-center hover:bg-gray-100"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Kembali
              </Button>
              <div className="min-w-0 flex-1">
                <h1 className="text-xl sm:text-2xl font-bold text-gray-900 truncate">
                  Detail E-Seal
                </h1>
                <p className="text-sm text-gray-500 truncate">
                  Informasi lengkap tracking E-Seal {esealDetail.noEseal}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-3 flex-shrink-0">
              <Badge
                variant={esealDetail.status === 'ACTIVE' ? 'default' : 'secondary'}
                className={`px-3 py-1 text-sm font-medium ${
                  esealDetail.status === 'ACTIVE'
                    ? 'bg-green-100 text-green-800 border-green-200'
                    : 'bg-gray-100 text-gray-800 border-gray-200'
                }`}
              >
                {esealDetail.status}
              </Badge>
              <Button size="sm" variant="outline" className="hidden sm:flex">
                Admin
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* First Row - Two Columns */}
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-6 lg:gap-8 mb-6">

          {/* Left Column */}
          <div className="space-y-6">

            {/* Informasi Dasar */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-100 bg-gray-50">
                <h2 className="text-lg font-semibold text-gray-900 flex items-center">
                  <Package className="w-5 h-5 mr-2 text-blue-600" />
                  Informasi Dasar
                </h2>
              </div>
              <div className="p-6">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                  <div className="space-y-1">
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Nomor E-Seal
                    </label>
                    <p className="text-sm font-medium text-gray-900">{esealDetail.noEseal}</p>
                  </div>
                  <div className="space-y-1">
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Status
                    </label>
                    <div className="flex items-center">
                      <Badge
                        variant={esealDetail.status === 'ACTIVE' ? 'default' : 'secondary'}
                        className={`${
                          esealDetail.status === 'ACTIVE'
                            ? 'bg-green-100 text-green-800 border-green-200'
                            : 'bg-gray-100 text-gray-800 border-gray-200'
                        }`}
                      >
                        {esealDetail.status}
                      </Badge>
                    </div>
                  </div>
                  <div className="space-y-1">
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Nomor IMEI
                    </label>
                    <div className="bg-gray-50 px-3 py-2 rounded-lg border border-gray-200">
                      <p className="text-sm font-mono text-gray-900 break-all">
                        {esealDetail.noImei}
                      </p>
                    </div>
                  </div>
                  <div className="space-y-1">
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Nomor AJU
                    </label>
                    <div className="bg-blue-50 px-3 py-2 rounded-lg border border-blue-200">
                      <p className="text-sm font-mono text-blue-700 break-all">
                        {esealDetail.nomorAJU}
                      </p>
                    </div>
                  </div>
                  <div className="space-y-1">
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Vendor
                    </label>
                    <p className="text-sm text-gray-900">{esealDetail.vendor}</p>
                  </div>
                  <div className="space-y-1">
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Tipe
                    </label>
                    <p className="text-sm text-gray-900">{esealDetail.tipe}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Informasi Kontainer */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-100 bg-gray-50">
                <h2 className="text-lg font-semibold text-gray-900 flex items-center">
                  <Package className="w-5 h-5 mr-2 text-orange-600" />
                  Informasi Kontainer
                </h2>
              </div>
              <div className="p-6">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                  <div className="space-y-1">
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Nomor Kontainer
                    </label>
                    <div className="bg-gray-50 px-3 py-2 rounded-lg border border-gray-200">
                      <p className="text-sm font-mono text-gray-900 break-all">
                        {esealDetail.noKontainer}
                      </p>
                    </div>
                  </div>
                  <div className="space-y-1">
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Ukuran Kontainer
                    </label>
                    <p className="text-sm text-gray-900">
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                        {esealDetail.ukKontainer} Feet
                      </span>
                    </p>
                  </div>
                  <div className="sm:col-span-2 space-y-1">
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Jenis Kontainer
                    </label>
                    <p className="text-sm text-gray-900">
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {esealDetail.jnsKontainer}
                      </span>
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right Column */}
          <div className="space-y-6">

            {/* Informasi Pengiriman */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-100 bg-gray-50">
                <h2 className="text-lg font-semibold text-gray-900 flex items-center">
                  <Activity className="w-5 h-5 mr-2 text-purple-600" />
                  Informasi Pengiriman
                </h2>
              </div>
              <div className="p-6">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                  <div className="space-y-1">
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Nomor AJU
                    </label>
                    <div className="bg-purple-50 px-3 py-2 rounded-lg border border-purple-200">
                      <p className="text-sm font-mono text-purple-700 break-all">
                        {esealDetail.nomorAJU}
                      </p>
                    </div>
                  </div>
                  <div className="space-y-1">
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Kode Dokumen
                    </label>
                    <p className="text-sm text-gray-900">
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                        {esealDetail.kodeDokumen}
                      </span>
                    </p>
                  </div>
                  <div className="sm:col-span-2 space-y-1">
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Nomor Kontainer
                    </label>
                    <div className="bg-gray-50 px-3 py-2 rounded-lg border border-gray-200">
                      <p className="text-sm font-mono text-gray-900 break-all">
                        {esealDetail.noKontainer}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Informasi Kendaraan & Driver */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-100 bg-gray-50">
                <h2 className="text-lg font-semibold text-gray-900 flex items-center">
                  <Truck className="w-5 h-5 mr-2 text-indigo-600" />
                  Informasi Kendaraan & Driver
                </h2>
              </div>
              <div className="p-6">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                  <div className="space-y-1">
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Nomor Polisi
                    </label>
                    <div className="bg-indigo-50 px-3 py-2 rounded-lg border border-indigo-200">
                      <p className="text-sm font-mono text-indigo-700 font-semibold break-all">
                        {esealDetail.noPolisi}
                      </p>
                    </div>
                  </div>
                  <div className="space-y-1">
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Nama Driver
                    </label>
                    <div className="flex items-center space-x-2">
                      <User className="w-4 h-4 text-gray-400" />
                      <p className="text-sm text-gray-900">{esealDetail.namaDriver}</p>
                    </div>
                  </div>
                  <div className="sm:col-span-2 space-y-1">
                    <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                      No. Telepon Driver
                    </label>
                    <div className="bg-gray-50 px-3 py-2 rounded-lg border border-gray-200">
                      <p className="text-sm font-mono text-gray-900 break-all">
                        {esealDetail.nomorTeleponDriver}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Log Aktivitas Terbaru */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-100 bg-gray-50">
                <h2 className="text-lg font-semibold text-gray-900 flex items-center">
                  <Clock className="w-5 h-5 mr-2 text-emerald-600" />
                  Log Aktivitas Terbaru
                </h2>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  {esealDetail.activities?.slice(0, 5).map((activity, index) => (
                    <div key={activity.id} className="relative">
                      {/* Timeline line */}
                      {index < esealDetail.activities.slice(0, 5).length - 1 && (
                        <div className="absolute left-4 top-8 w-0.5 h-6 bg-gray-200"></div>
                      )}

                      <div className="flex items-start space-x-4 p-4 bg-gradient-to-r from-gray-50 to-white border border-gray-100 rounded-lg hover:shadow-sm transition-shadow">
                        <div className="flex-shrink-0">
                          <div className="w-8 h-8 bg-emerald-100 rounded-full flex items-center justify-center">
                            <div className="w-3 h-3 bg-emerald-500 rounded-full"></div>
                          </div>
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 mb-1">
                            {activity.activity}
                          </p>
                          <p className="text-xs text-gray-500">
                            {new Date(activity.timestamp).toLocaleString('id-ID', {
                              year: 'numeric',
                              month: 'short',
                              day: 'numeric',
                              hour: '2-digit',
                              minute: '2-digit'
                            })}
                          </p>
                        </div>
                        <div className="flex-shrink-0">
                          <Badge
                            variant="outline"
                            className="text-xs bg-emerald-50 text-emerald-700 border-emerald-200"
                          >
                            {activity.status}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  )) || (
                    <div className="text-center py-8">
                      <Clock className="w-12 h-12 text-gray-300 mx-auto mb-3" />
                      <p className="text-sm text-gray-500">
                        Belum ada aktivitas tercatat
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Full Width Section - Informasi Lokasi */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-100 bg-gray-50">
            <h2 className="text-lg font-semibold text-gray-900 flex items-center">
              <MapPin className="w-5 h-5 mr-2 text-green-600" />
              Informasi Lokasi
            </h2>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Lokasi Asal */}
              <div className="relative p-4 bg-gradient-to-r from-red-50 to-red-100 border border-red-200 rounded-lg">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                      <MapPin className="w-4 h-4 text-white" />
                    </div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className="text-sm font-semibold text-red-800 mb-2">Lokasi Asal</h3>
                    <div className="mb-3">
                      <p
                        className="text-sm text-red-700 leading-relaxed"
                        style={!showFullAddress ? {
                          display: '-webkit-box',
                          WebkitLineClamp: 2,
                          WebkitBoxOrient: 'vertical',
                          overflow: 'hidden'
                        } : {}}
                      >
                        {esealDetail.alamatAsal}
                      </p>
                      {esealDetail.alamatAsal.length > 100 && (
                        <button
                          onClick={() => setShowFullAddress(!showFullAddress)}
                          className="text-xs text-red-600 hover:text-red-800 mt-1 font-medium"
                        >
                          {showFullAddress ? 'Lihat lebih sedikit' : 'Lihat selengkapnya'}
                        </button>
                      )}
                    </div>
                    <div className="flex flex-col sm:flex-row sm:flex-wrap sm:items-center gap-2">
                      <div className="bg-red-200 px-2 py-1 rounded border border-red-300">
                        <span className="text-xs font-mono text-red-800 break-all">
                          {esealDetail.latitudeAsal}
                        </span>
                      </div>
                      <span className="text-red-400 hidden sm:inline">•</span>
                      <div className="bg-red-200 px-2 py-1 rounded border border-red-300">
                        <span className="text-xs font-mono text-red-800 break-all">
                          {esealDetail.longitudeAsal}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Lokasi Tujuan */}
              <div className="relative p-4 bg-gradient-to-r from-green-50 to-green-100 border border-green-200 rounded-lg">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                      <MapPin className="w-4 h-4 text-white" />
                    </div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className="text-sm font-semibold text-green-800 mb-2">Lokasi Tujuan</h3>
                    <div className="mb-3">
                      <p
                        className="text-sm text-green-700 leading-relaxed"
                        style={!showFullAddress ? {
                          display: '-webkit-box',
                          WebkitLineClamp: 2,
                          WebkitBoxOrient: 'vertical',
                          overflow: 'hidden'
                        } : {}}
                      >
                        {esealDetail.alamatTujuan}
                      </p>
                      {esealDetail.alamatTujuan.length > 100 && (
                        <button
                          onClick={() => setShowFullAddress(!showFullAddress)}
                          className="text-xs text-green-600 hover:text-green-800 mt-1 font-medium"
                        >
                          {showFullAddress ? 'Lihat lebih sedikit' : 'Lihat selengkapnya'}
                        </button>
                      )}
                    </div>
                    <div className="flex flex-col sm:flex-row sm:flex-wrap sm:items-center gap-2">
                      <div className="bg-green-200 px-2 py-1 rounded border border-green-300">
                        <span className="text-xs font-mono text-green-800 break-all">
                          {esealDetail.latitudeTujuan}
                        </span>
                      </div>
                      <span className="text-green-400 hidden sm:inline">•</span>
                      <div className="bg-green-200 px-2 py-1 rounded border border-green-300">
                        <span className="text-xs font-mono text-green-800 break-all">
                          {esealDetail.longitudeTujuan}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
