import { useState } from 'react';
import { Input } from '../../../components/ui/input';
import { Button } from '../../../components/ui/button';
import { Badge } from '../../../components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../../components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../components/ui/select';
import { Search, Loader2, MapPin, Battery, Clock } from 'lucide-react';
import { useLogsData } from '../../../hooks/useLogsData';

export default function Logs() {
  const [idVendor, setIdVendor] = useState('');
  const [nomorESeal, setNomorESeal] = useState('');
  const [token, setToken] = useState('');
  const [searchParams, setSearchParams] = useState({
    idVendor: '',
    nomorESeal: '',
    token: ''
  });
  const [currentPage, setCurrentPage] = useState(1);
  const [entriesPerPage, setEntriesPerPage] = useState('10');
  const [, setHasSearched] = useState(false);

  // Get E-Seal data for dropdowns
  // const { data: esealData } = useESealData({ limit: 1000 }); // Get all E-Seals for dropdown

  // Use logs data hook
  const { data: logsData, total, totalPages, loading, error, refetch } = useLogsData({
    idVendor: searchParams.idVendor,
    nomorESeal: searchParams.nomorESeal,
    token: searchParams.token,
    page: currentPage,
    limit: parseInt(entriesPerPage),
  });

  const handleSearch = () => {
    console.log('Searching with:', { idVendor, nomorESeal, token });
    setSearchParams({
      idVendor,
      nomorESeal,
      token
    });
    setCurrentPage(1);
    setHasSearched(true);
  };

  const handleClear = () => {
    setIdVendor('');
    setNomorESeal('');
    setToken('');
    setSearchParams({
      idVendor: '',
      nomorESeal: '',
      token: ''
    });
    setCurrentPage(1);
    setHasSearched(false);
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString('id-ID', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 mb-1">Logs</h1>
        <p className="text-gray-600">Cari dan lihat log aktivitas E-Seal monitoring.</p>
      </div>

      {/* Search Form */}
      <div className="bg-white rounded-lg border p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-6">Cari Data Log</h2>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          {/* ID Vendor */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              ID Vendor
            </label>
            <Input
              placeholder="Masukkan ID Vendor"
              value={idVendor}
              onChange={(e) => setIdVendor(e.target.value)}
              className="w-full"
            />
          </div>

          {/* Nomor E-Seal */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Nomor E-Seal
            </label>
            <Input
              placeholder="Masukkan Nomor E-Seal"
              value={nomorESeal}
              onChange={(e) => setNomorESeal(e.target.value)}
              className="w-full"
            />
          </div>

          {/* Token */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Token
            </label>
            <Input
              placeholder="Masukkan Token"
              value={token}
              onChange={(e) => setToken(e.target.value)}
              className="w-full"
            />
          </div>
        </div>

        {/* Search Buttons */}
        <div className="flex gap-3">
          <Button
            onClick={handleSearch}
            className="bg-slate-800 hover:bg-slate-700 text-white"
            disabled={loading}
          >
            {loading ? (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <Search className="w-4 h-4 mr-2" />
            )}
            Cari
          </Button>
          <Button
            onClick={handleClear}
            variant="outline"
            className="border-slate-300 text-slate-700 hover:bg-slate-50"
          >
            Clear
          </Button>
        </div>
      </div>

      {/* Results Section */}
      {(searchParams.idVendor || searchParams.nomorESeal || searchParams.token) && (
        <div className="bg-white rounded-lg border overflow-hidden">
          {/* Results Header */}
          <div className="p-4 border-b bg-gray-50">
            <div className="flex justify-between items-center">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Hasil Pencarian Log</h3>
                <p className="text-sm text-gray-600">
                  {loading ? 'Mencari...' : `Ditemukan ${total} aktivitas`}
                </p>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600">Show</span>
                <Select value={entriesPerPage} onValueChange={setEntriesPerPage}>
                  <SelectTrigger className="w-20">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="10">10</SelectItem>
                    <SelectItem value="25">25</SelectItem>
                    <SelectItem value="50">50</SelectItem>
                  </SelectContent>
                </Select>
                <span className="text-sm text-gray-600">entries</span>
              </div>
            </div>
          </div>

          {/* Results Table */}
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow className="bg-gray-50">
                  <TableHead className="font-semibold text-slate-700">Waktu</TableHead>
                  <TableHead className="font-semibold text-slate-700">E-Seal</TableHead>
                  <TableHead className="font-semibold text-slate-700">Vendor</TableHead>
                  <TableHead className="font-semibold text-slate-700">Aktivitas</TableHead>
                  <TableHead className="font-semibold text-slate-700">Lokasi</TableHead>
                  <TableHead className="font-semibold text-slate-700">Status</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8">
                      <div className="flex items-center justify-center">
                        <Loader2 className="w-6 h-6 animate-spin mr-2" />
                        <span className="text-gray-500">Mencari log aktivitas...</span>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : error ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8 text-red-500">
                      <div className="flex flex-col items-center">
                        <span className="mb-2">Error: {error}</span>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => refetch()}
                          className="text-red-600 border-red-300 hover:bg-red-50"
                        >
                          Coba Lagi
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : logsData.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8 text-gray-500">
                      Tidak ada log aktivitas ditemukan untuk kriteria pencarian ini
                    </TableCell>
                  </TableRow>
                ) : (
                  logsData.map((log) => (
                    <TableRow key={log.id} className="hover:bg-gray-50">
                      <TableCell className="font-mono text-sm">
                        <div className="flex items-center">
                          <Clock className="w-4 h-4 mr-2 text-gray-400" />
                          {formatTimestamp(log.timestamp)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">{log.esealName}</div>
                          <div className="text-sm text-gray-500 font-mono">{log.esealIMEI}</div>
                        </div>
                      </TableCell>
                      <TableCell className="font-medium">{log.idVendor}</TableCell>
                      <TableCell className="max-w-xs">
                        <div className="truncate" title={log.activity}>
                          {log.activity}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center text-sm">
                          <MapPin className="w-4 h-4 mr-1 text-gray-400" />
                          <div>
                            {log.location.address ? (
                              <div className="max-w-xs truncate" title={log.location.address}>
                                {log.location.address}
                              </div>
                            ) : (
                              <span className="text-gray-400">Unknown location</span>
                            )}
                            {log.location.latitude && log.location.longitude && (
                              <div className="text-xs text-gray-500 font-mono">
                                {log.location.latitude}, {log.location.longitude}
                              </div>
                            )}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col gap-1">
                          <Badge
                            variant={log.status === 'TRACKING' ? 'default' : 'secondary'}
                            className={log.status === 'TRACKING' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}
                          >
                            {log.status}
                          </Badge>
                          {log.deviceInfo.battery && (
                            <div className="flex items-center text-xs text-gray-500">
                              <Battery className="w-3 h-3 mr-1" />
                              {log.deviceInfo.battery}%
                            </div>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          {total > 0 && (
            <div className="flex justify-between items-center p-4 border-t bg-gray-50">
              <span className="text-sm text-gray-600">
                Showing {((currentPage - 1) * parseInt(entriesPerPage)) + 1} to {Math.min(currentPage * parseInt(entriesPerPage), total)} of {total} entries
              </span>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1 || loading}
                >
                  Previous
                </Button>
                <span className="px-3 py-1 bg-slate-800 text-white rounded text-xs">
                  {currentPage}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages || loading}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </div>
      )}

    </div>
  );
}