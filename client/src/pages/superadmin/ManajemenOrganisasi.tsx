import { useState, useEffect } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../components/ui/table';
import { Input } from '../../components/ui/input';
import { Button } from '../../components/ui/button';
import { Badge } from '../../components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../components/ui/select';
import { Search, Eye, Plus, X, Loader2, Edit, Trash2 } from 'lucide-react';
import { authClient, useSession } from '../../lib/auth-client';

interface Organization {
  id: string;
  name: string;
  slug: string;
  logo?: string | null;
  createdAt: string;
  members?: any[];
}

interface OrganizationFormData {
  name: string;
  slug: string;
}

// Helper function to generate slug
const generateSlug = (name: string) => {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove non-alphanumeric characters except spaces and hyphens
    .trim()
    .replace(/\s+/g, '-'); // Replace spaces with hyphens
};

// Modal Component
const TambahDataModal = ({
  isOpen,
  onClose,
  onSubmit,
  initialData
}: {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: OrganizationFormData) => void;
  initialData?: Organization | null;
}) => {
  const [formData, setFormData] = useState<OrganizationFormData>({
    name: '',
    slug: ''
  });

  useEffect(() => {
    if (isOpen) {
      if (initialData) {
        setFormData({
          name: initialData.name,
          slug: initialData.slug,
        });
      } else {
        setFormData({
          name: '',
          slug: '',
        });
      }
    }
  }, [isOpen, initialData]);

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newName = e.target.value;
    setFormData(prev => ({
      ...prev,
      name: newName,
      slug: generateSlug(newName) // Auto-generate slug
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
    onClose();
  };

  const handleCancel = () => {
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
      onClick={onClose}
    >
      <div
        className="bg-white rounded-xl shadow-xl w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-gray-900">{initialData ? 'Edit' : 'Tambah'} Organisasi</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="w-6 h-6" />
            </button>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Nama Organisasi <span className="text-red-500">*</span>
              </label>
              <Input
                placeholder="Masukkan nama organisasi"
                value={formData.name}
                onChange={handleNameChange}
                required
              />
            </div>

            {/* Slug */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Slug <span className="text-red-500">*</span>
              </label>
              <Input
                placeholder="Slug akan otomatis dibuat"
                value={formData.slug}
                readOnly // Make slug read-only as it's auto-generated
                className="bg-gray-100 cursor-not-allowed"
                required
              />
            </div>

            {/* Buttons */}
            <div className="flex gap-3 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={handleCancel}
                className="flex-1"
              >
                Batal
              </Button>
              <Button
                type="submit"
                className="flex-1 bg-slate-800 hover:bg-slate-700 text-white"
              >
                Simpan
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default function ManajemenOrganisasi() {
  const [search, setSearch] = useState('');
  const [debouncedSearch, setDebouncedSearch] = useState('');
  const [entriesPerPage, setEntriesPerPage] = useState('10');
  const [currentPage, setCurrentPage] = useState(1);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingOrganization, setEditingOrganization] = useState<Organization | null>(null);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [total, setTotal] = useState(0);
  const { data: session } = useSession(); // Get current session to access user ID

  const fetchOrganizations = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch(`/api/organization/list-all?page=${currentPage}&limit=${entriesPerPage}&search=${debouncedSearch}`, {
        credentials: 'include'
      });
      if (!response.ok) {
        throw new Error('Gagal mengambil data organisasi');
      }
      const result = await response.json();
      setOrganizations(result.data);
      setTotal(result.pagination.total);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(search);
      setCurrentPage(1);
    }, 500);
    return () => clearTimeout(timer);
  }, [search]);

  useEffect(() => {
    fetchOrganizations();
  }, [currentPage, entriesPerPage, debouncedSearch]);

  const handleAdd = () => {
    setEditingOrganization(null);
    setIsModalOpen(true);
  };

  const handleEdit = (org: Organization) => {
    setEditingOrganization(org);
    setIsModalOpen(true);
  };

  const handleDelete = async (organizationId: string) => {
    if (confirm('Apakah Anda yakin ingin menghapus organisasi ini?')) {
      try {
        const response = await fetch('/api/organization/delete', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ organizationId }),
          credentials: 'include'
        });
        
        if (!response.ok) {
          throw new Error('Failed to delete organization');
        }
        
        alert('Organisasi berhasil dihapus');
        fetchOrganizations();
      } catch (error) {
        console.error('Gagal menghapus organisasi:', error);
        alert('Gagal menghapus organisasi');
      }
    }
  };

  const handleSubmit = async (formData: OrganizationFormData) => {
    try {
      if (editingOrganization) {
        const response = await fetch('/api/organization/update', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            organizationId: editingOrganization.id,
            data: formData,
          }),
          credentials: 'include'
        });
        
        if (!response.ok) {
          throw new Error('Failed to update organization');
        }
        
        alert('Organisasi berhasil diperbarui');
      } else {
        // Use custom endpoint for superadmin to create organization without being added as member
        if (session?.user?.role === 'superadmin') {
          const response = await fetch('/api/organization/create', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData),
            credentials: 'include'
          });
          
          if (!response.ok) {
            throw new Error('Failed to create organization');
          }
          
          alert('Organisasi berhasil ditambahkan');
        } else {
          // Regular users use the default better-auth endpoint
          await authClient.organization.create(formData);
          alert('Organisasi berhasil ditambahkan');
        }
      }
      fetchOrganizations();
    } catch (error) {
      console.error('Gagal menyimpan organisasi:', error);
      alert('Gagal menyimpan organisasi');
    }
  };

  const totalPages = Math.ceil(total / parseInt(entriesPerPage));

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 mb-1">Manajemen Organisasi</h1>
        <p className="text-gray-600">Kelola semua organisasi yang terdaftar di sistem.</p>
      </div>

      {/* Controls */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 bg-white p-4 rounded-lg border">
        {/* Show entries */}
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-600">Show</span>
          <Select value={entriesPerPage} onValueChange={setEntriesPerPage}>
            <SelectTrigger className="w-20">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="10">10</SelectItem>
              <SelectItem value="25">25</SelectItem>
              <SelectItem value="50">50</SelectItem>
              <SelectItem value="100">100</SelectItem>
            </SelectContent>
          </Select>
          <span className="text-sm text-gray-600">entries per page</span>
        </div>

        {/* Search and Add Button */}
        <div className="flex items-center gap-3">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Cari organisasi..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="pl-10 w-48 sm:w-64"
            />
          </div>
          <Button
            onClick={handleAdd}
            className="bg-slate-800 hover:bg-slate-700 text-white"
          >
            <Plus className="w-4 h-4 sm:mr-2" />
            <span className="hidden sm:inline">Tambah Organisasi</span>
          </Button>
        </div>
      </div>

      {/* Table */}
      <div className="bg-white rounded-lg border overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50">
                <TableHead className="font-semibold text-slate-700">Nama Organisasi</TableHead>
                <TableHead className="font-semibold text-slate-700">Slug</TableHead>
                <TableHead className="font-semibold text-slate-700">Jumlah Anggota</TableHead>
                <TableHead className="font-semibold text-slate-700">Tanggal Dibuat</TableHead>
                <TableHead className="font-semibold text-slate-700">Aksi</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={5} className="text-center py-8">
                    <div className="flex items-center justify-center">
                      <Loader2 className="w-6 h-6 animate-spin mr-2" />
                      <span className="text-gray-500">Memuat data...</span>
                    </div>
                  </TableCell>
                </TableRow>
              ) : error ? (
                <TableRow>
                  <TableCell colSpan={5} className="text-center py-8 text-red-500">
                    <div className="flex flex-col items-center">
                      <span className="mb-2">Error: {error}</span>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={fetchOrganizations}
                        className="text-red-600 border-red-300 hover:bg-red-50"
                      >
                        Coba Lagi
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ) : organizations.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={5} className="text-center py-8 text-gray-500">
                    {search ? 'Organisasi tidak ditemukan' : 'Belum ada organisasi'}
                  </TableCell>
                </TableRow>
              ) : (
                organizations.map((org) => (
                  <TableRow key={org.id} className="hover:bg-gray-50">
                    <TableCell className="font-medium">{org.name}</TableCell>
                    <TableCell className="font-mono text-sm">{org.slug}</TableCell>
                    <TableCell>{org.members?.length || 0}</TableCell>
                    <TableCell>{new Date(org.createdAt).toLocaleDateString('id-ID')}</TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleEdit(org)}
                          className="text-slate-600 hover:text-slate-800"
                        >
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={() => handleDelete(org.id)}
                          className="bg-red-100 text-red-800 border border-red-200 hover:bg-red-200"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Pagination info */}
      {total > 0 && (
        <div className="flex justify-between items-center text-sm text-gray-600">
          <span>
            Showing {((currentPage - 1) * parseInt(entriesPerPage)) + 1} to {Math.min(currentPage * parseInt(entriesPerPage), total)} of {total} entries
          </span>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
            >
              Previous
            </Button>
            <span className="px-3 py-1 bg-slate-800 text-white rounded text-xs">
              {currentPage}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
            >
              Next
            </Button>
          </div>
        </div>
      )}

      {/* Modal */}
      <TambahDataModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSubmit={handleSubmit}
        initialData={editingOrganization}
      />
    </div>
  );
}
