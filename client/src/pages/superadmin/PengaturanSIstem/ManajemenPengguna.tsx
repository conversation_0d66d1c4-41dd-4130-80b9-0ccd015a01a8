import { useState, useEffect, useCallback } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Pencil, Trash2, Plus } from 'lucide-react';
import { authClient } from '@/lib/auth-client';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogClose } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  organization: {
    id: string;
    name: string;
  } | null;
  lastActive: string;
  checked: boolean;
}

interface Organization {
  id: string;
  name: string;
}

interface Role {
  id: string;
  name: string;
  description?: string;
  isDefault?: boolean;
}

export default function ManajemenPengguna() {
  const [users, setUsers] = useState<User[]>([]);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);

  const [newUser, setNewUser] = useState({
    name: '',
    email: '',
    password: '',
    role: 'admin',
    organizationId: '',
  });

  const [editUser, setEditUser] = useState({
    id: '',
    name: '',
    email: '',
    role: '',
    organizationId: '',
  });

  const { data: session } = authClient.useSession();

  const fetchRoles = useCallback(async () => {
    if (!session) return;
    try {
      let allRoles: Role[] = [
        { id: 'superadmin', name: 'Super Admin', isDefault: true },
        { id: 'admin', name: 'Admin', isDefault: true }
      ];

      if (session.user?.role === 'superadmin') {
        // Superadmin fetches all roles from all organizations
        const [customRolesResponse, memberRolesResponse] = await Promise.all([
          fetch('/api/role/all'),
          fetch('/api/role/members/all')
        ]);

        const customRolesResult = await customRolesResponse.json();
        const memberRolesResult = await memberRolesResponse.json();

        if (customRolesResult.success && customRolesResult.data) {
          allRoles.push(...customRolesResult.data);
        }
        if (memberRolesResult.success && memberRolesResult.data) {
          allRoles.push(...memberRolesResult.data);
        }
      } else {
        // Regular admin fetches roles for their own organization
        const activeOrg = await authClient.organization.getActive();
        if (!activeOrg?.data?.id) {
          console.log('No active organization found for non-superadmin');
          setRoles(allRoles); // Set default roles
          return;
        }
        const orgId = activeOrg.data.id;

        const [customRolesResponse, memberRolesResponse] = await Promise.all([
          fetch(`/api/role/org/${orgId}`),
          fetch(`/api/role/members/${orgId}`)
        ]);

        const customRolesResult = await customRolesResponse.json();
        const memberRolesResult = await memberRolesResponse.json();

        if (customRolesResult.success && customRolesResult.data) {
          allRoles.push(...customRolesResult.data);
        }
        if (memberRolesResult.success && memberRolesResult.data) {
          allRoles.push(...memberRolesResult.data);
        }
      }
      
      // Remove duplicates
      const uniqueRoles = allRoles.filter((role, index, self) =>
        index === self.findIndex((r) => r.id === role.id)
      );

      setRoles(uniqueRoles);
    } catch (error) {
      console.error('Failed to fetch roles:', error);
      setRoles([
        { id: 'superadmin', name: 'Super Admin', isDefault: true },
        { id: 'admin', name: 'Admin', isDefault: true }
      ]);
    }
  }, [session]);

  const fetchUsers = useCallback(async () => {
    if (!session) return;
    try {
      // 1. Fetch all users with their memberships using custom endpoint
      const usersResponse = await fetch('/api/admin/users', {
        credentials: 'include', // Include cookies for better-auth session
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      if (!usersResponse.ok) {
        throw new Error('Failed to fetch users');
      }
      
      const usersData = await usersResponse.json();
      if (!usersData.success || !usersData.data) {
        throw new Error("No users found or failed to fetch users.");
      }
      const usersFromApi = usersData.data;

      // 2. Fetch all organizations
      let organizationsList: Organization[] = [];
      if (session.user?.role === 'superadmin') {
        const response = await fetch('/api/organization/list-all');
        if (!response.ok) throw new Error('Failed to fetch organizations');
        const orgData = await response.json();
        organizationsList = orgData.data || orgData || [];
      } else {
        const orgData = await authClient.organization.list({});
        organizationsList = orgData.data || [];
      }
      setOrganizations(organizationsList);
      
      console.log("API Users:", usersFromApi);
      console.log("API Organizations:", organizationsList);

      // 3. Map users to include organization name
      const formattedUsers = usersFromApi.map((user: any) => {
        const member = user.memberships && user.memberships.length > 0 ? user.memberships[0] : null;
        const organization = member ? member.organization : null;

        return {
          id: user.id,
          name: user.name,
          email: user.email,
          role: user.role || '-',
          organization: organization ? { id: organization.id, name: organization.name } : null,
          lastActive: user.sessions && user.sessions.length > 0 ? new Date(user.sessions[0].updatedAt).toLocaleString('id-ID', { dateStyle: 'medium', timeStyle: 'short' }) : '-',
          checked: false,
        };
      });
      
      console.log("Formatted Users with Org:", formattedUsers);
      setUsers(formattedUsers);

    } catch (error) {
      console.error("Failed to fetch data:", error);
      // Optionally set users to an empty array on failure
      setUsers([]);
      setOrganizations([]);
    }
  }, [session]);

  useEffect(() => {
    fetchUsers();
    fetchRoles();
  }, [fetchUsers, fetchRoles]);

  const handleCheckboxChange = (id: string) => {
    setUsers(prevUsers =>
      prevUsers.map(user =>
        user.id === id ? { ...user, checked: !user.checked } : user
      )
    );
  };

  const handleAddUser = async () => {
    try {
      const createdUser = await authClient.admin.createUser({
        name: newUser.name,
        email: newUser.email,
        password: newUser.password,
        role: newUser.role as 'admin' | 'superadmin',
      });
      
      // If user is admin and organization is selected, add them as member
      if (newUser.role === 'admin' && newUser.organizationId && createdUser.data?.user?.id) {
        try {
          const response = await fetch('/api/organization/add-member', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            credentials: 'include',
            body: JSON.stringify({
              userId: createdUser.data.user.id,
              organizationId: newUser.organizationId,
              role: 'admin'
            })
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || 'Failed to add user to organization');
          }
        } catch (memberError) {
          console.error("Failed to add user to organization:", memberError);
          // User was created but couldn't be added to organization
          // You might want to show a warning to the user here
        }
      }
      
      setIsAddModalOpen(false);
      await fetchUsers();
    } catch (error) {
      console.error("Failed to add user:", error);
    }
  };

  const handleUpdateUser = async () => {
    try {
      await authClient.admin.updateUser({
        userId: editUser.id,
        data: {
          name: editUser.name,
          email: editUser.email,
        },
      });
      await authClient.admin.setRole({
        userId: editUser.id,
        role: editUser.role as 'admin' | 'superadmin' | 'member',
      });
      // Handle organization change if necessary
      setIsEditModalOpen(false);
      await fetchUsers();
    } catch (error) {
      console.error("Failed to update user:", error);
    }
  };

  const handleDeleteUser = async () => {
    if (selectedUser) {
      try {
        await authClient.admin.removeUser({ userId: selectedUser.id });
        setIsDeleteModalOpen(false);
        setSelectedUser(null);
        await fetchUsers();
      } catch (error) {
        console.error("Failed to delete user:", error);
      }
    }
  };

  const openEditModal = (user: User) => {
    setSelectedUser(user);
    setEditUser({
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role,
      organizationId: user.organization?.id || '',
    });
    setIsEditModalOpen(true);
  };

  const openDeleteModal = (user: User) => {
    setSelectedUser(user);
    setIsDeleteModalOpen(true);
  };

  const openAddModal = () => {
    setNewUser({
      name: '',
      email: '',
      password: '',
      role: 'admin',
      organizationId: '',
    });
    setIsAddModalOpen(true);
  };

  return (
    <div className="space-y-6 p-4">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 mb-1">Manajemen Pengguna</h1>
          <div className="w-16 h-1 bg-blue-600"></div>
        </div>
        <Button onClick={openAddModal}>
          <Plus className="mr-2 h-4 w-4" /> Tambah Pengguna
        </Button>
      </div>
      <div className="border rounded-lg overflow-hidden bg-white shadow-sm">
        <Table>
          <TableHeader>
            <TableRow className="bg-slate-50 border-b-2 border-slate-200">
              <TableHead className="min-w-[50px] font-semibold text-slate-700">
                <input type="checkbox" className="form-checkbox h-4 w-4 text-blue-600" />
              </TableHead>
              <TableHead className="min-w-[150px] font-semibold text-slate-700">Nama</TableHead>
              <TableHead className="min-w-[120px] font-semibold text-slate-700">Role</TableHead>
              <TableHead className="min-w-[150px] font-semibold text-slate-700">Perusahaan</TableHead>
              <TableHead className="min-w-[150px] font-semibold text-slate-700">Terakhir Aktif</TableHead>
              <TableHead className="min-w-[100px] font-semibold text-slate-700">Aksi</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {users.map((user) => (
              <TableRow key={user.id} className="hover:bg-slate-50 transition-colors">
                <TableCell className="font-medium">
                  <input
                    type="checkbox"
                    className="form-checkbox h-4 w-4 text-blue-600"
                    checked={user.checked}
                    onChange={() => handleCheckboxChange(user.id)}
                  />
                </TableCell>
                <TableCell>
                  <div>{user.name}</div>
                  <div className="text-sm text-gray-500">{user.email}</div>
                </TableCell>
                <TableCell>{user.role}</TableCell>
                <TableCell>{user.organization?.name || '-'}</TableCell>
                <TableCell>{user.lastActive}</TableCell>
                <TableCell className="flex gap-2">
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => openEditModal(user)}
                  >
                    <Pencil className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="destructive"
                    size="icon"
                    onClick={() => openDeleteModal(user)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Add User Modal */}
      <Dialog open={isAddModalOpen} onOpenChange={setIsAddModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Tambah Pengguna Baru</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="name">Nama</Label>
              <Input id="name" value={newUser.name} onChange={(e: React.ChangeEvent<HTMLInputElement>) => setNewUser({ ...newUser, name: e.target.value })} />
            </div>
            <div>
              <Label htmlFor="email">Email</Label>
              <Input id="email" type="email" value={newUser.email} onChange={(e: React.ChangeEvent<HTMLInputElement>) => setNewUser({ ...newUser, email: e.target.value })} />
            </div>
            <div>
              <Label htmlFor="password">Password</Label>
              <Input id="password" type="password" value={newUser.password} onChange={(e: React.ChangeEvent<HTMLInputElement>) => setNewUser({ ...newUser, password: e.target.value })} />
            </div>
            <div>
              <Label htmlFor="role">Role</Label>
              <Select value={newUser.role} onValueChange={(value: string) => setNewUser({ ...newUser, role: value })}>
                <SelectTrigger>
                  <SelectValue placeholder="Pilih role" />
                </SelectTrigger>
                <SelectContent>
                  {roles.map((role) => (
                    <SelectItem key={role.id} value={role.id}>
                      {role.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            {newUser.role === 'admin' && (
              <div>
                <Label htmlFor="organization">Perusahaan</Label>
                <Select value={newUser.organizationId} onValueChange={(value: string) => setNewUser({ ...newUser, organizationId: value })}>
                  <SelectTrigger>
                    <SelectValue placeholder="Pilih perusahaan" />
                  </SelectTrigger>
                  <SelectContent>
                    {organizations.map((org) => (
                      <SelectItem key={org.id} value={org.id}>{org.name}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline">Batal</Button>
            </DialogClose>
            <Button onClick={handleAddUser}>Simpan</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit User Modal */}
      <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Pengguna</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="edit-name">Nama</Label>
              <Input id="edit-name" value={editUser.name} onChange={(e: React.ChangeEvent<HTMLInputElement>) => setEditUser({ ...editUser, name: e.target.value })} />
            </div>
            <div>
              <Label htmlFor="edit-email">Email</Label>
              <Input id="edit-email" type="email" value={editUser.email} onChange={(e: React.ChangeEvent<HTMLInputElement>) => setEditUser({ ...editUser, email: e.target.value })} />
            </div>
            <div>
              <Label htmlFor="edit-role">Role</Label>
              <Select value={editUser.role} onValueChange={(value: string) => setEditUser({ ...editUser, role: value })}>
                <SelectTrigger>
                  <SelectValue placeholder="Pilih role" />
                </SelectTrigger>
                <SelectContent>
                  {roles.map((role) => (
                    <SelectItem key={role.id} value={role.id}>
                      {role.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            {editUser.role === 'admin' && (
              <div>
                <Label htmlFor="edit-organization">Perusahaan</Label>
                <Select value={editUser.organizationId} onValueChange={(value: string) => setEditUser({ ...editUser, organizationId: value })}>
                  <SelectTrigger>
                    <SelectValue placeholder="Pilih perusahaan" />
                  </SelectTrigger>
                  <SelectContent>
                    {organizations.map((org) => (
                      <SelectItem key={org.id} value={org.id}>{org.name}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline">Batal</Button>
            </DialogClose>
            <Button onClick={handleUpdateUser}>Simpan</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Modal */}
      <Dialog open={isDeleteModalOpen} onOpenChange={setIsDeleteModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Hapus Pengguna</DialogTitle>
          </DialogHeader>
          <p>Anda yakin ingin menghapus pengguna {selectedUser?.name}?</p>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline">Batal</Button>
            </DialogClose>
            <Button variant="destructive" onClick={handleDeleteUser}>Hapus</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
