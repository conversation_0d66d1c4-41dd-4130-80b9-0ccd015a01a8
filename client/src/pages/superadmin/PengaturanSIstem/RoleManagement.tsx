import { useState, useEffect } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../../components/ui/table';
import { Button } from '../../../components/ui/button';
import { Input } from '../../../components/ui/input';
import { Checkbox } from '../../../components/ui/checkbox';
import { Trash2, Edit, Plus, X } from 'lucide-react';
import { useSession, useActiveOrganization } from '../../../lib/auth-client';
enum ModuleType {
  DATA_ESEAL = 'DATA_ESEAL',
  TRACKING_DATA = 'TRACKING_DATA',
  LOGS = 'LOGS',
  PENGATURAN_SISTEM = 'PENGATURAN_SISTEM',
}

interface Permission {
  id?: string;
  module: ModuleType;
  canCreate: boolean;
  canRead: boolean;
  canUpdate: boolean;
  canDelete: boolean;
}

interface Role {
  id: string;
  name: string;
  description: string | null;
  permissions: Permission[];
  isDefault?: boolean;
  organizationId?: string;
  organizationName?: string;
}

interface Organization {
  id: string;
  name: string;
}

const moduleDisplayNames = {
  [ModuleType.DATA_ESEAL]: 'Data E-Seal',
  [ModuleType.TRACKING_DATA]: 'Tracking Data',
  [ModuleType.LOGS]: 'Logs',
  [ModuleType.PENGATURAN_SISTEM]: 'Pengaturan Sistem',
};

const allModules = Object.keys(ModuleType) as Array<keyof typeof ModuleType>;

const AddEditRoleModal = ({
  isOpen,
  onClose,
  onSubmit,
  initialData,
  organizations,
  isSuperAdmin,
}: {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: Omit<Role, 'id'> & { organizationId?: string }) => void;
  initialData?: Role | null;
  organizations: Organization[];
  isSuperAdmin: boolean;
}) => {
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [selectedOrg, setSelectedOrg] = useState<string>('');

  useEffect(() => {
    if (initialData) {
      setName(initialData.name);
      setDescription(initialData.description || '');
      setPermissions(initialData.permissions);
      setSelectedOrg(initialData.organizationId || '');
    } else {
      // Reset to default for new role
      setName('');
      setDescription('');
      setSelectedOrg('');
      setPermissions(
        allModules.map((module) => ({
          module: ModuleType[module],
          canCreate: false,
          canRead: false,
          canUpdate: false,
          canDelete: false,
        }))
      );
    }
  }, [initialData]);

  const handlePermissionChange = (module: ModuleType, action: keyof Omit<Permission, 'module' | 'id'>, value: boolean) => {
    setPermissions((prev) =>
      prev.map((p) =>
        p.module === module ? { ...p, [action]: value } : p
      )
    );
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (isSuperAdmin && !selectedOrg) {
      alert('Please select an organization.');
      return;
    }
    onSubmit({ name, description, permissions, organizationId: selectedOrg });
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto" onClick={(e) => e.stopPropagation()}>
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-gray-900">{initialData ? 'Edit' : 'Tambah'} Role</h2>
            <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
              <X className="w-6 h-6" />
            </button>
          </div>
          <form onSubmit={handleSubmit} className="space-y-4">
            {isSuperAdmin && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Organisasi</label>
                <select
                  value={selectedOrg}
                  onChange={(e) => setSelectedOrg(e.target.value)}
                  className="w-full p-2 border rounded-md"
                  required
                >
                  <option value="" disabled>Pilih Organisasi</option>
                  {organizations.map(org => (
                    <option key={org.id} value={org.id}>{org.name}</option>
                  ))}
                </select>
              </div>
            )}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Nama Role</label>
              <Input value={name} onChange={(e) => setName(e.target.value)} required />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Deskripsi</label>
              <Input value={description} onChange={(e) => setDescription(e.target.value)} />
            </div>
            <div>
              <h3 className="text-lg font-medium text-gray-800 mb-2">Kapabilitas</h3>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Module</TableHead>
                    <TableHead>Create</TableHead>
                    <TableHead>Read</TableHead>
                    <TableHead>Update</TableHead>
                    <TableHead>Delete</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {permissions.map((p) => (
                    <TableRow key={p.module}>
                      <TableCell>{moduleDisplayNames[p.module]}</TableCell>
                      <TableCell>
                        <Checkbox checked={p.canCreate} onCheckedChange={(checked: boolean) => handlePermissionChange(p.module, 'canCreate', checked)} />
                      </TableCell>
                      <TableCell>
                        <Checkbox checked={p.canRead} onCheckedChange={(checked: boolean) => handlePermissionChange(p.module, 'canRead', checked)} />
                      </TableCell>
                      <TableCell>
                        <Checkbox checked={p.canUpdate} onCheckedChange={(checked: boolean) => handlePermissionChange(p.module, 'canUpdate', checked)} />
                      </TableCell>
                      <TableCell>
                        <Checkbox checked={p.canDelete} onCheckedChange={(checked: boolean) => handlePermissionChange(p.module, 'canDelete', checked)} />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
            <div className="flex gap-3 pt-4">
              <Button type="button" variant="outline" onClick={onClose} className="flex-1">Batal</Button>
              <Button type="submit" className="flex-1 bg-slate-800 hover:bg-slate-700 text-white">Simpan</Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default function RoleManagement() {
  const [roles, setRoles] = useState<Role[]>([]);
  const [loading, setLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingRole, setEditingRole] = useState<Role | null>(null);
  const { data: session } = useSession();
  const { data: activeOrg } = useActiveOrganization();
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const isSuperAdmin = session?.user?.role === 'superadmin';

  const fetchRoles = async () => {
    console.log('🚀 fetchRoles called');
    
    // Check if user is superadmin
    const isSuperAdmin = session?.user?.role === 'superadmin';
    let orgId = null;
    
    if (!isSuperAdmin) {
      orgId = activeOrg?.id;
      if (!orgId) {
        console.log('❌ No active organization found in fetchRoles');
        setLoading(false);
        return;
      }
    } else {
      // For superadmin, we fetch all roles, so orgId is not needed here.
    }
    
    setLoading(true);
    try {
      console.log('🔄 Fetching roles for organization:', orgId);
      
      let customRolesResult = { success: false, data: [] };
      let memberRolesResult = { success: false, data: [] };
      
      // Only fetch from API if we have a valid orgId (not 'superadmin')
      if (orgId && orgId !== 'superadmin') {
        // Fetch both custom roles and member roles
        const [customRolesResponse, memberRolesResponse] = await Promise.all([
          fetch(`/api/role/org/${orgId}`),
          fetch(`/api/role/members/${orgId}`)
        ]);
        
        customRolesResult = await customRolesResponse.json();
        memberRolesResult = await memberRolesResponse.json();
        
        console.log('Custom roles result:', customRolesResult);
        console.log('Member roles result:', memberRolesResult);
      } else {
        console.log('Superadmin mode: showing default roles only');
      }
      
      let allRoles: Role[] = [];
      
      // Add default superadmin role
      const superAdminRole = {
        id: 'superadmin',
        name: 'Super Admin',
        description: 'Full access dashboard, bisa membuat role dan user',
        permissions: allModules.map(module => ({
          module: ModuleType[module],
          canCreate: true,
          canRead: true,
          canUpdate: true,
          canDelete: true,
        })),
        isDefault: true,
      };
      allRoles.push(superAdminRole);
      
      // Add default admin role
      const adminRole = {
        id: 'admin',
        name: 'Admin',
        description: 'Admin role with read access to all modules',
        permissions: allModules.map(module => ({
          module: ModuleType[module],
          canCreate: module === 'PENGATURAN_SISTEM' ? false : true,
          canRead: true, // Admin can read all modules including PENGATURAN_SISTEM
          canUpdate: module === 'PENGATURAN_SISTEM' ? false : true,
          canDelete: module === 'PENGATURAN_SISTEM' ? false : true,
        })),
        isDefault: false, // Changed to false so delete button can appear
      };
      allRoles.push(adminRole);
      
      // Add member roles (from Member table)
      if (memberRolesResult.success && memberRolesResult.data) {
        console.log('Adding member roles:', memberRolesResult.data);
        // Filter out default roles that we already added
        const uniqueMemberRoles = memberRolesResult.data.filter(
          (memberRole: Role) => !['superadmin', 'admin'].includes(memberRole.id)
        );
        allRoles.push(...uniqueMemberRoles);
      }
      
      // Add custom roles (from CustomRole table)
      if (customRolesResult.success && customRolesResult.data) {
        console.log('Adding custom roles:', customRolesResult.data);
        // Filter out duplicates (in case a custom role has the same ID)
        const uniqueCustomRoles = customRolesResult.data.filter(
          (customRole: Role) => !allRoles.find(role => role.id === customRole.id)
        );
        allRoles.push(...uniqueCustomRoles);
      }
      
      console.log('Final roles list:', allRoles);
      setRoles(allRoles);
      
    } catch (error) {
      console.error('Failed to fetch roles:', error);
      // Still show default roles even if API fails
      setRoles([{
        id: 'superadmin',
        name: 'Super Admin',
        description: 'Full access dashboard, bisa membuat role dan user',
        permissions: allModules.map(module => ({
          module: ModuleType[module],
          canCreate: true,
          canRead: true,
          canUpdate: true,
          canDelete: true,
        })),
        isDefault: true,
      }, {
        id: 'admin',
        name: 'Admin',
        description: 'Admin role with read access to all modules',
        permissions: allModules.map(module => ({
          module: ModuleType[module],
          canCreate: module === 'PENGATURAN_SISTEM' ? false : true,
          canRead: true, // Admin can read all modules including PENGATURAN_SISTEM
          canUpdate: module === 'PENGATURAN_SISTEM' ? false : true,
          canDelete: module === 'PENGATURAN_SISTEM' ? false : true,
        })),
        isDefault: false, // Changed to false so delete button can appear
      }]);
    } finally {
      setLoading(false);
    }
  };

  // Function to fetch all roles for superadmin (without organization restriction)
  const fetchAllRoles = async () => {
    setLoading(true);
    try {
      console.log('🔄 Fetching all roles for superadmin');
      
      const [memberRolesResponse, customRolesResponse] = await Promise.all([
        fetch('/api/role/members/all'),
        fetch('/api/role/all')
      ]);
      
      const memberRolesResult = await memberRolesResponse.json();
      const customRolesResult = await customRolesResponse.json();

      console.log('All member roles result:', memberRolesResult);
      console.log('All custom roles result:', customRolesResult);
      
      const roleMap = new Map<string, Role>();

      // Add default superadmin role
      const superAdminRole: Role = {
        id: 'superadmin',
        name: 'Super Admin',
        description: 'Full access dashboard, bisa membuat role dan user',
        permissions: allModules.map(module => ({
          module: ModuleType[module],
          canCreate: true,
          canRead: true,
          canUpdate: true,
          canDelete: true,
        })),
        isDefault: true,
        organizationName: 'System',
      };
      roleMap.set(superAdminRole.id, superAdminRole);

      // Process roles from members endpoint
      if (memberRolesResult.success && memberRolesResult.data) {
        memberRolesResult.data.forEach((role: Role) => {
          if (!roleMap.has(role.id)) {
            roleMap.set(role.id, role);
          }
        });
      }

      // Process all custom roles
      if (customRolesResult.success && customRolesResult.data) {
        customRolesResult.data.forEach((role: Role) => {
          if (!roleMap.has(role.id)) {
            roleMap.set(role.id, role);
          }
        });
      }
      
      const allRoles = Array.from(roleMap.values());
      console.log('Final all roles:', allRoles);
      setRoles(allRoles);
    } catch (error) {
      console.error('Error fetching all roles:', error);
      setRoles([{
        id: 'superadmin',
        name: 'Super Admin',
        description: 'Full access dashboard, bisa membuat role dan user',
        permissions: allModules.map(module => ({
          module: ModuleType[module],
          canCreate: true,
          canRead: true,
          canUpdate: true,
          canDelete: true,
        })),
        isDefault: true,
        organizationName: 'System',
      }]);
    } finally {
      setLoading(false);
    }
  };

  const fetchOrganizations = async () => {
    try {
      const response = await fetch('/api/organization');
      const result = await response.json();
      if (result.success) {
        setOrganizations(result.data);
      } else {
        console.error('Failed to fetch organizations:', result.error);
      }
    } catch (error) {
      console.error('Error fetching organizations:', error);
    }
  };

  useEffect(() => {
    console.log('🔍 RoleManagement useEffect triggered');
    console.log('Session:', session);
    console.log('Active Organization:', activeOrg);
    console.log('User role:', session?.user?.role);
    
    if (session?.user) {
      if (isSuperAdmin) {
        console.log('✅ Superadmin detected, fetching all roles and organizations');
        fetchAllRoles();
        fetchOrganizations();
      } else if (activeOrg?.id) {
        console.log('✅ Regular user with activeOrg, calling fetchRoles');
        fetchRoles();
      } else {
        console.log('⚠️ Regular user without activeOrg, showing limited default roles');
        // For non-superadmin users without active org, show limited roles
        const defaultRoles = [
          {
            id: 'admin',
            name: 'Admin',
            description: 'Admin role with limited permissions',
            permissions: allModules.map(module => ({
              module: ModuleType[module],
              canCreate: module === 'PENGATURAN_SISTEM' ? false : true,
              canRead: true,
              canUpdate: module === 'PENGATURAN_SISTEM' ? false : true,
              canDelete: module === 'PENGATURAN_SISTEM' ? false : true,
            })),
            isDefault: true,
          }
        ];
        setRoles(defaultRoles);
        setLoading(false);
      }
    } else {
      console.log('❌ No session or user available');
      setLoading(false);
    }
  }, [session, activeOrg]);

  const handleAdd = () => {
    setEditingRole(null);
    setIsModalOpen(true);
  };

  const handleEdit = (role: Role) => {
    setEditingRole(role);
    setIsModalOpen(true);
  };

  const handleDelete = async (roleId: string) => {
    const orgId = activeOrg?.id;
    if (!orgId) {
      console.log('No active organization found');
      return;
    }

    // Prevent deleting default roles
    if (['superadmin', 'admin'].includes(roleId)) {
      alert('Cannot delete default roles');
      return;
    }

    if (!confirm('Are you sure you want to delete this role?')) {
      return;
    }

    try {
      const response = await fetch(`/api/role/org/${orgId}/${roleId}`, {
        method: 'DELETE',
      });

      const result = await response.json();
      
      if (result.success) {
        // Refresh roles list
        fetchRoles();
        alert('Role deleted successfully');
      } else {
        alert('Failed to delete role: ' + (result.message || 'Unknown error'));
      }
    } catch (error) {
      console.error('Failed to delete role:', error);
      alert('Failed to delete role');
    }
  };

  const handleSubmit = async (data: Omit<Role, 'id'> & { organizationId?: string }) => {
    const { organizationId, ...roleData } = data;
    let orgId = isSuperAdmin ? organizationId : activeOrg?.id;

    if (!orgId) {
      console.error('No organization ID provided');
      alert('Organization is required.');
      return;
    }

    const url = editingRole ? `/api/role/${editingRole.id}` : '/api/role';
    const method = editingRole ? 'PUT' : 'POST';
    
    const requestData = { ...roleData, organizationId: orgId };
    console.log('=== ROLE SUBMISSION ===');
    console.log('Method:', method);
    console.log('URL:', url);
    console.log('Request data:', JSON.stringify(requestData, null, 2));
    console.log('Permissions being sent:', requestData.permissions);

    try {
      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestData),
      });
      const result = await response.json();
      
      console.log('=== SERVER RESPONSE ===');
      console.log('Response status:', response.status);
      console.log('Response data:', JSON.stringify(result, null, 2));
      
      if (result.success) {
        console.log('✅ Role saved successfully!');
        console.log('Saved role data:', result.data);
        if (isSuperAdmin) {
          fetchAllRoles();
        } else {
          fetchRoles();
        }
        setIsModalOpen(false);
      } else {
        console.error('❌ Failed to save role:', result.error);
        alert('Failed to save role: ' + (result.error || 'Unknown error'));
      }
    } catch (error) {
      console.error('❌ Network error while saving role:', error);
      alert('Failed to save role: ' + (error instanceof Error ? error.message : 'Unknown error'));
    }
  };

  return (
    <div className="space-y-6 p-4">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 mb-1">Role Management</h1>
          <div className="w-16 h-1 bg-blue-600"></div>
        </div>
        <Button onClick={handleAdd} className="bg-slate-800 hover:bg-slate-700 text-white">
          <Plus className="w-4 h-4 mr-2" />
          Tambah Role
        </Button>
      </div>
      <div className="border rounded-lg overflow-hidden bg-white shadow-sm">
        <Table>
          <TableHeader>
            <TableRow className="bg-slate-50 border-b-2 border-slate-200">
              <TableHead>No</TableHead>
              <TableHead>Role Name</TableHead>
              {isSuperAdmin && <TableHead>Organization</TableHead>}
              <TableHead>Description</TableHead>
              <TableHead>Aksi</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={4} className="text-center">Loading...</TableCell>
              </TableRow>
            ) : (
              roles.map((role, index) => (
                <TableRow key={role.id}>
                  <TableCell>{index + 1}</TableCell>
                  <TableCell>{role.name}</TableCell>
                  {isSuperAdmin && <TableCell>{role.organizationName || 'N/A'}</TableCell>}
                  <TableCell>{role.description}</TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm" onClick={() => handleEdit(role)}>
                        <Edit className="w-4 h-4" />
                      </Button>
                      {!role.isDefault && (
                        <Button variant="destructive" size="sm" onClick={() => handleDelete(role.id)}>
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
      <AddEditRoleModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSubmit={handleSubmit}
        initialData={editingRole}
        organizations={organizations}
        isSuperAdmin={isSuperAdmin}
      />
    </div>
  );
}
