import { useState, useEffect } from 'react';
import { useSession } from '../lib/auth-client';

export interface ESealData {
  id: string;
  noEseal: string;
  noImei: string;
  idVendor: string;
  merk?: string;
  model?: string;
  tipe?: string;
  status: string;
  alamatAsal: string;
  alamatTujuan: string;
  lokasiAsal: string;
  lokasiTujuan: string;
  latitudeAsal: string;
  longitudeAsal: string;
  latitudeTujuan: string;
  longitudeTujuan: string;
  noPolisi: string;
  ukKontainer: string;
  jnsKontainer: string;
  noKontainer: string;
  namaDriver: string;
  nomorTeleponDriver: string;
  nomorAJU?: string;
  statusValidasi: string;
  // Sync fields
  isSync: boolean;
  beacukaiResponseLog?: any;
  beacukaiId?: string;
  // Organization fields
  organizationId?: string;
  organization?: {
    id: string;
    name: string;
    slug: string;
  };
  createdAt: string;
  updatedAt: string;
  user: {
    id: string;
    name: string;
    email: string;
  };
  dokumen: Array<{
    id: string;
    jenisMuat: string;
    jumlahKontainer: string;
    kodeDokumen: string;
    kodeKantor: string;
    nomorAju: string;
    nomorDokumen: string;
    tanggalDokumen: string;
  }>;
}

export interface ESealResponse {
  success: boolean;
  data: {
    data: ESealData[];
    total: number;
    page: number;
    limit: number;
  };
}

export interface UseESealDataProps {
  page?: number;
  limit?: number;
  search?: string;
  organizationSlug?: string;
}

export function useESealData({ page = 1, limit = 10, search = '', organizationSlug }: UseESealDataProps = {}) {
  const { data: session } = useSession();
  const [data, setData] = useState<ESealData[]>([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        ...(search && { search }),
      });

      if (session?.user?.role !== 'superadmin' && organizationSlug) {
        params.append('organizationSlug', organizationSlug);
      }

      const response = await fetch(`/api/eseal?${params}`);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result: ESealResponse = await response.json();
      
      if (!result.success) {
        throw new Error('Failed to fetch E-Seal data');
      }

      setData(result.data.data);
      setTotal(result.data.total);
    } catch (err) {
      console.error('Error fetching E-Seal data:', err);
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
      setData([]);
      setTotal(0);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [page, limit, search, organizationSlug, session]);

  const refetch = () => {
    fetchData();
  };

  return {
    data,
    total,
    loading,
    error,
    refetch,
  };
}

// Hook untuk single E-Seal
export function useESeal(id: string) {
  const [data, setData] = useState<ESealData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/eseal/${id}`);
      
      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('E-Seal not found');
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch E-Seal data');
      }

      setData(result.data);
    } catch (err) {
      console.error('Error fetching E-Seal:', err);
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
      setData(null);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (id) {
      fetchData();
    }
  }, [id]);

  const refetch = () => {
    if (id) {
      fetchData();
    }
  };

  return {
    data,
    loading,
    error,
    refetch,
  };
}

// Hook untuk delete E-Seal
export function useDeleteESeal() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const deleteESeal = async (id: string): Promise<boolean> => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/eseal/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to delete E-Seal');
      }

      return true;
    } catch (err) {
      console.error('Error deleting E-Seal:', err);
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
      return false;
    } finally {
      setLoading(false);
    }
  };

  return {
    deleteESeal,
    loading,
    error,
  };
}
