export interface LogActivity {
    id: string;
    esealId: string;
    esealName: string;
    esealIMEI: string;
    idVendor: string;
    timestamp: string;
    activity: string;
    location: {
        latitude: string | null;
        longitude: string | null;
        address: string | null;
        altitude: string | null;
        speed: string | null;
    };
    deviceInfo: {
        battery: string | null;
        dayaAki: string | null;
        event: string | null;
        kota: string | null;
        provinsi: string | null;
    };
    status: string;
}
export interface LogsResponse {
    success: boolean;
    data: {
        logs: LogActivity[];
        total: number;
        page: number;
        limit: number;
        totalPages: number;
    };
}
export interface UseLogsDataProps {
    idVendor?: string;
    nomorESeal?: string;
    token?: string;
    page?: number;
    limit?: number;
}
export declare function useLogsData({ idVendor, nomorESeal, token, page, limit }?: UseLogsDataProps): {
    data: LogActivity[];
    total: number;
    totalPages: number;
    loading: boolean;
    error: string | null;
    refetch: () => void;
};
