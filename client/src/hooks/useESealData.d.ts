export interface ESealData {
    id: string;
    noEseal: string;
    noImei: string;
    idVendor: string;
    merk?: string;
    model?: string;
    tipe?: string;
    status: string;
    alamatAsal: string;
    alamatTujuan: string;
    lokasiAsal: string;
    lokasiTujuan: string;
    latitudeAsal: string;
    longitudeAsal: string;
    latitudeTujuan: string;
    longitudeTujuan: string;
    noPolisi: string;
    ukKontainer: string;
    jnsKontainer: string;
    noKontainer: string;
    namaDriver: string;
    nomorTeleponDriver: string;
    nomorAJU?: string;
    statusValidasi: string;
    isSync: boolean;
    beacukaiResponseLog?: any;
    beacukaiId?: string;
    organizationId?: string;
    organization?: {
        id: string;
        name: string;
        slug: string;
    };
    createdAt: string;
    updatedAt: string;
    user: {
        id: string;
        name: string;
        email: string;
    };
    dokumen: Array<{
        id: string;
        jenisMuat: string;
        jumlahKontainer: string;
        kodeDokumen: string;
        kodeKantor: string;
        nomorAju: string;
        nomorDokumen: string;
        tanggalDokumen: string;
    }>;
}
export interface ESealResponse {
    success: boolean;
    data: {
        data: ESealData[];
        total: number;
        page: number;
        limit: number;
    };
}
export interface UseESealDataProps {
    page?: number;
    limit?: number;
    search?: string;
    organizationSlug?: string;
}
export declare function useESealData({ page, limit, search, organizationSlug }?: UseESealDataProps): {
    data: ESealData[];
    total: number;
    loading: boolean;
    error: string | null;
    refetch: () => void;
};
export declare function useESeal(id: string): {
    data: ESealData | null;
    loading: boolean;
    error: string | null;
    refetch: () => void;
};
export declare function useDeleteESeal(): {
    deleteESeal: (id: string) => Promise<boolean>;
    loading: boolean;
    error: string | null;
};
