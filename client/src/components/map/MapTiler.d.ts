import "@maptiler/sdk/dist/maptiler-sdk.css";
import './MapTiler';
interface MapTilerProps {
    originLocation?: {
        lat: number;
        lng: number;
        address?: string;
    };
    destinationLocation?: {
        lat: number;
        lng: number;
        address?: string;
    };
    onOriginSelect?: (location: {
        lat: number;
        lng: number;
        address?: string;
    }) => void;
    onDestinationSelect?: (location: {
        lat: number;
        lng: number;
        address?: string;
    }) => void;
    height?: string;
    interactive?: boolean;
}
export default function MapTiler({ originLocation, destinationLocation, onOriginSelect, onDestinationSelect, height, interactive, }: MapTilerProps): import("react/jsx-runtime").JSX.Element;
export {};
