import 'leaflet/dist/leaflet.css';
interface LocationData {
    lat: number;
    lng: number;
    address?: string;
}
interface MapSelectorProps {
    originLocation?: LocationData;
    destinationLocation?: LocationData;
    onOriginSelect: (location: LocationData) => void;
    onDestinationSelect: (location: LocationData) => void;
    mode: 'origin' | 'destination';
    height?: string;
}
export declare function MapSelector({ originLocation, destinationLocation, onOriginSelect, onDestinationSelect, mode, height }: MapSelectorProps): import("react/jsx-runtime").JSX.Element;
export {};
