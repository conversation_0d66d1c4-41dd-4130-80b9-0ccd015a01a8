interface LocationData {
    lat: number;
    lng: number;
    address?: string;
}
interface MapWithRouteProps {
    originLocation?: LocationData;
    destinationLocation?: LocationData;
    onOriginSelect: (location: LocationData) => void;
    onDestinationSelect: (location: LocationData) => void;
    height?: string;
}
export declare function MapWithRoute({ originLocation, destinationLocation, onOriginSelect, onDestinationSelect, height }: MapWithRouteProps): import("react/jsx-runtime").JSX.Element;
export {};
