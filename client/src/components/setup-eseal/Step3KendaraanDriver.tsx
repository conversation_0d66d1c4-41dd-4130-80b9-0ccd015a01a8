import { Truck, User } from 'lucide-react';
import { Input } from '../ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import type { SetupESealFormData } from 'shared/src/types';
import { CONTAINER_TYPES, CONTAINER_SIZES } from 'shared/src/constants/beacukai';

interface Step3KendaraanDriverProps {
  formData: SetupESealFormData;
  setFormData: (data: SetupESealFormData) => void;
}

export function Step3KendaraanDriver({ formData, setFormData }: Step3KendaraanDriverProps) {
  return (
    <div>
      <div className="flex items-center mb-6">
        <Truck className="w-5 h-5 mr-2" />
        <h2 className="text-lg font-semibold text-gray-900">Kendaraan & Driver</h2>
      </div>

      <div className="space-y-6">
        {/* Form Fields - 2 Columns Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Column 1 - Left Side */}
          <div className="space-y-6">
            {/* Nomor Polisi */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Truck className="w-4 h-4 inline mr-1" />
                Nomor Polisi <span className="text-red-500">*</span>
              </label>
              <Input
                placeholder="Contoh: B 1234 ABC"
                value={formData.noPolisi}
                onChange={(e) => setFormData({...formData, noPolisi: e.target.value})}
                className="w-full"
              />
              <p className="text-xs text-gray-500 mt-1">Nomor polisi kendaraan pengangkut</p>
            </div>

            {/* Ukuran Kontainer */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Ukuran Kontainer <span className="text-red-500">*</span>
              </label>
              <Select
                value={formData.ukKontainer}
                onValueChange={(value) => setFormData({...formData, ukKontainer: value})}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Pilih ukuran kontainer" />
                </SelectTrigger>
                <SelectContent>
                  {CONTAINER_SIZES.map((size) => (
                    <SelectItem key={size.value} value={size.value}>
                      {size.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-xs text-gray-500 mt-1">Ukuran kontainer sesuai standar internasional</p>
            </div>

            {/* Jenis Kontainer */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Jenis Kontainer <span className="text-red-500">*</span>
              </label>
              <Select
                value={formData.jnsKontainer}
                onValueChange={(value) => setFormData({...formData, jnsKontainer: value})}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Pilih jenis kontainer" />
                </SelectTrigger>
                <SelectContent>
                  {CONTAINER_TYPES.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-xs text-gray-500 mt-1">Jenis kontainer sesuai dengan muatan</p>
            </div>
          </div>

          {/* Column 2 - Right Side */}
          <div className="space-y-6">
            {/* Nomor Kontainer */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Nomor Kontainer <span className="text-red-500">*</span>
              </label>
              <Input
                placeholder="Contoh: TCLU1234567"
                value={formData.noKontainer}
                onChange={(e) => setFormData({...formData, noKontainer: e.target.value})}
                className="w-full"
              />
              <p className="text-xs text-gray-500 mt-1">Format standar kontainer internasional (11 karakter)</p>
            </div>

            {/* Nama Driver */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <User className="w-4 h-4 inline mr-1" />
                Nama Driver <span className="text-red-500">*</span>
              </label>
              <Input
                placeholder="Masukkan nama lengkap driver"
                value={formData.namaDriver}
                onChange={(e) => setFormData({...formData, namaDriver: e.target.value})}
                className="w-full"
              />
              <p className="text-xs text-gray-500 mt-1">Nama lengkap sesuai SIM</p>
            </div>

            {/* No. Telepon Driver */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                No. Telepon Driver <span className="text-red-500">*</span>
              </label>
              <Input
                placeholder="Contoh: 08123456789"
                value={formData.nomorTeleponDriver}
                onChange={(e) => setFormData({...formData, nomorTeleponDriver: e.target.value})}
                className="w-full"
              />
              <p className="text-xs text-gray-500 mt-1">Format: 08xxxxxxxxxx atau +62xxxxxxxxxx</p>
            </div>
          </div>
        </div>

        {/* Info Box */}
        <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-start">
            <div className="w-5 h-5 bg-blue-100 rounded-full flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">
              <span className="text-xs font-medium text-blue-600">i</span>
            </div>
            <div className="text-sm text-blue-800">
              <p className="font-medium mb-1">Informasi Penting:</p>
              <ul className="text-xs space-y-1">
                <li>• Pastikan nomor polisi sesuai dengan STNK kendaraan</li>
                <li>• Nomor kontainer harus sesuai dengan dokumen pengiriman</li>
                <li>• Nomor telepon driver akan digunakan untuk komunikasi darurat</li>
                <li>• Data kendaraan akan divalidasi dengan sistem Beacukai</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Validation Status */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <div className="flex items-center mb-2">
            <div className="w-6 h-6 bg-gray-300 rounded-full flex items-center justify-center mr-2">
              <span className="text-xs font-medium text-gray-600">
                {formData.noPolisi && formData.ukKontainer && formData.jnsKontainer &&
                 formData.noKontainer && formData.namaDriver && formData.nomorTeleponDriver ? '✓' : '!'}
              </span>
            </div>
            <h4 className="text-sm font-medium text-gray-900">
              {formData.noPolisi && formData.ukKontainer && formData.jnsKontainer &&
               formData.noKontainer && formData.namaDriver && formData.nomorTeleponDriver
                ? 'Data kendaraan lengkap'
                : 'Data kendaraan belum lengkap'
              }
            </h4>
          </div>
          <p className="text-xs text-gray-600 ml-8">
            {formData.noPolisi && formData.ukKontainer && formData.jnsKontainer &&
             formData.noKontainer && formData.namaDriver && formData.nomorTeleponDriver
              ? 'Semua informasi kendaraan dan driver telah diisi.'
              : 'Lengkapi semua informasi kendaraan dan driver untuk melanjutkan.'
            }
          </p>
        </div>
      </div>
    </div>
  );
}
