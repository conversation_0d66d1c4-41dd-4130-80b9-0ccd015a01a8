import { <PERSON><PERSON><PERSON><PERSON>, FileCheck, Plus, X } from 'lucide-react';
import { Input } from '../ui/input';
import { Button } from '../ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import type { SetupESealFormData } from 'shared/src/types';

interface Step4ValidasiAJUProps {
  formData: SetupESealFormData;
  setFormData: (data: SetupESealFormData) => void;
}

export function Step4ValidasiAJU({ formData, setFormData }: Step4ValidasiAJUProps) {
  // Real AJU validation function
  const validateAJU = async (nomorAju: string) => {
    try {
      setFormData({...formData, statusValidasi: 'validating'});

      // Call real API: GET /api/beacukai/dokumen/get-dok-pabean?nomor_aju={nomor_aju}
      const response = await fetch(`/api/beacukai/dokumen/get-dok-pabean?nomor_aju=${nomorAju}`);
      const result = await response.json();

      if (result.success && result.data.status === 'success') {
        const ajuData = result.data.item;

        // Auto-populate data dari AJU (AJU adalah master data)
        const updates: Partial<typeof formData> = {
          statusValidasi: 'valid' as const,
        };

        // Auto-populate nomor kontainer dari AJU (WAJIB sesuai AJU)
        if (ajuData?.kontainer?.length > 0) {
          updates.noKontainer = ajuData.kontainer[0].nomorKontainer;

          // Show info if data was different
          if (formData.noKontainer && formData.noKontainer !== ajuData.kontainer[0].nomorKontainer) {
            console.log(`📝 Nomor kontainer diupdate sesuai AJU: ${formData.noKontainer} → ${ajuData.kontainer[0].nomorKontainer}`);
          }
        }

        setFormData({
          ...formData,
          ...updates,
          // Populate dokumen data
          dokumen: formData.dokumen.map((dok, index) => ({
            ...dok,
            nomorAju: nomorAju,
            // Update dengan data dari API jika tersedia
            ...(ajuData && index === 0 ? {
              kodeDokumen: ajuData.kodeDokumen || dok.kodeDokumen,
              kodeKantor: ajuData.kodeKantor || dok.kodeKantor,
              nomorDokumen: ajuData.nomorDaftar || dok.nomorDokumen,
              tanggalDokumen: ajuData.tanggalDaftar || dok.tanggalDokumen,
            } : {})
          }))
        });
      } else {
        setFormData({...formData, statusValidasi: 'invalid'});
      }
    } catch (error) {
      console.error('Error validating AJU:', error);
      setFormData({...formData, statusValidasi: 'invalid'});
    }
  };

  // Skip AJU validation with mock data
  const skipAJUValidation = () => {
    const mockAJU = '16021600008120160415000002';
    setFormData({
      ...formData,
      nomorAJU: mockAJU,
      statusValidasi: 'valid',
      dokumen: [{
        jenisMuat: 'FCL',
        jumlahKontainer: '1',
        kodeDokumen: 'PLP',
        kodeKantor: '050100',
        nomorAju: mockAJU,
        nomorDokumen: 'MOCK-DOC-001',
        tanggalDokumen: new Date().toISOString().split('T')[0]
      }]
    });
  };
  const addDokumen = () => {
    const newDokumen = {
      jenisMuat: '',
      jumlahKontainer: '',
      kodeDokumen: '',
      kodeKantor: '',
      nomorAju: formData.nomorAJU,
      nomorDokumen: '',
      tanggalDokumen: ''
    };
    setFormData({
      ...formData,
      dokumen: [...formData.dokumen, newDokumen]
    });
  };

  const removeDokumen = (index: number) => {
    const newDokumen = formData.dokumen.filter((_, i) => i !== index);
    setFormData({...formData, dokumen: newDokumen});
  };

  const updateDokumen = (index: number, field: string, value: string) => {
    const newDokumen = [...formData.dokumen];
    newDokumen[index] = { ...newDokumen[index], [field]: value };
    setFormData({...formData, dokumen: newDokumen});
  };

  return (
    <div>
      <div className="flex items-center mb-6">
        <CheckCircle className="w-5 h-5 mr-2" />
        <h2 className="text-lg font-semibold text-gray-900">Validasi AJU & Dokumen</h2>
      </div>

      <div className="space-y-6">
        {/* Validasi Nomor AJU Section */}
        <div>
          <h3 className="text-base font-medium text-gray-900 mb-2">Validasi Nomor AJU</h3>
          <p className="text-sm text-gray-600 mb-4">
            Masukkan nomor AJU untuk memvalidasi dokumen pengiriman
          </p>

          <div className="space-y-4">
            {/* Nomor AJU Input */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Nomor AJU <span className="text-red-500">*</span>
              </label>
              <div className="flex gap-3">
                <div className="flex-1">
                  <Input
                    placeholder="Contoh: 16021600008120160415000002"
                    value={formData.nomorAJU}
                    onChange={(e) => setFormData({...formData, nomorAJU: e.target.value})}
                    className="w-full"
                    maxLength={26}
                  />
                  <p className="text-xs text-gray-500 mt-1">Format: 26 digit nomor AJU</p>

                  {/* Helper untuk Testing */}
                  <div className="mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div className="flex items-start">
                      <div className="w-5 h-5 bg-yellow-100 rounded-full flex items-center justify-center mr-2 mt-0.5 flex-shrink-0">
                        <span className="text-xs font-medium text-yellow-600">💡</span>
                      </div>
                      <div className="text-sm flex-1">
                        <p className="font-medium text-yellow-800 mb-1">Testing Development:</p>
                        <p className="text-xs text-yellow-700 mb-2">
                          Gunakan nomor AJU contoh berikut (menggunakan mock data):
                        </p>
                        <div className="bg-yellow-100 p-2 rounded border font-mono text-xs text-yellow-800 mb-2">
                          16021600008120160415000002
                        </div>
                        <button
                          type="button"
                          onClick={() => setFormData({...formData, nomorAJU: '16021600008120160415000002'})}
                          className="text-xs text-yellow-700 hover:text-yellow-900 underline"
                        >
                          📋 Klik untuk menggunakan nomor contoh
                        </button>
                        <div className="mt-3 p-2 bg-blue-50 border border-blue-200 rounded text-xs">
                          <p className="text-blue-800 mb-2">
                            <strong>Catatan:</strong> Untuk AJU real, hubungi admin Beacukai untuk mendapatkan
                            nomor AJU yang valid di sistem development.
                          </p>
                          <p className="text-blue-800">
                            <strong>Alternatif:</strong> Gunakan tombol "Skip (Mock)" untuk melanjutkan dengan data mock
                            tanpa validasi AJU real.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="flex gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    className="px-6 bg-gray-100 hover:bg-gray-200 text-gray-700 border-gray-300"
                    onClick={() => validateAJU(formData.nomorAJU)}
                    disabled={!formData.nomorAJU || formData.statusValidasi === 'validating'}
                  >
                    {formData.statusValidasi === 'validating' ? 'Memvalidasi...' : 'Validasi'}
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    className="px-4 bg-blue-50 hover:bg-blue-100 text-blue-700 border-blue-300"
                    onClick={skipAJUValidation}
                    disabled={formData.statusValidasi === 'validating'}
                  >
                    Skip (Mock)
                  </Button>
                </div>
              </div>
            </div>

            {/* Validation Status */}
            {formData.statusValidasi !== 'pending' && (
              <div className="mt-4">
                {formData.statusValidasi === 'validating' && (
                  <div className="flex items-center p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-yellow-600 mr-3"></div>
                    <span className="text-sm text-yellow-800">Sedang memvalidasi nomor AJU...</span>
                  </div>
                )}

                {formData.statusValidasi === 'valid' && (
                  <div className="space-y-3">
                    <div className="flex items-center p-3 bg-green-50 border border-green-200 rounded-lg">
                      <CheckCircle className="w-4 h-4 text-green-600 mr-3" />
                      <div className="flex-1">
                        <span className="text-sm font-medium text-green-800">Nomor AJU Valid</span>
                        <p className="text-xs text-green-700 mt-1">
                          Dokumen pengiriman telah terverifikasi dan dapat digunakan untuk monitoring.
                        </p>
                      </div>
                    </div>

                    {/* Info Auto-Update */}
                    <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                      <div className="flex items-start">
                        <div className="w-4 h-4 bg-blue-500 rounded-full mr-3 flex items-center justify-center mt-0.5">
                          <span className="text-xs text-white font-bold">📝</span>
                        </div>
                        <div className="flex-1">
                          <span className="text-sm font-medium text-blue-800">Data Otomatis Diperbarui</span>
                          <p className="text-xs text-blue-700 mt-1">
                            Data kontainer dan dokumen telah diperbarui sesuai dengan informasi dari AJU.
                            AJU adalah sumber data resmi dari Bea Cukai yang tidak dapat diubah.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {formData.statusValidasi === 'invalid' && (
                  <div className="space-y-3">
                    <div className="flex items-center p-3 bg-red-50 border border-red-200 rounded-lg">
                      <div className="w-4 h-4 bg-red-500 rounded-full mr-3 flex items-center justify-center">
                        <span className="text-xs text-white font-bold">!</span>
                      </div>
                      <div className="flex-1">
                        <span className="text-sm font-medium text-red-800">Nomor AJU Tidak Valid</span>
                        <p className="text-xs text-red-700 mt-1">
                          Nomor AJU tidak ditemukan atau format tidak sesuai. Periksa kembali nomor yang dimasukkan.
                        </p>
                      </div>
                    </div>

                    {/* Saran untuk Testing */}
                    <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                      <div className="flex items-start">
                        <div className="w-4 h-4 bg-blue-500 rounded-full mr-3 flex items-center justify-center mt-0.5">
                          <span className="text-xs text-white font-bold">i</span>
                        </div>
                        <div className="flex-1">
                          <span className="text-sm font-medium text-blue-800">Untuk Testing Development:</span>
                          <p className="text-xs text-blue-700 mt-1 mb-2">
                            Pastikan menggunakan nomor AJU contoh dari dokumentasi Beacukai:
                          </p>
                          <div className="bg-blue-100 p-2 rounded border font-mono text-xs text-blue-800 mb-2">
                            16021600008120160415000002
                          </div>
                          <p className="text-xs text-blue-700">
                            Server development: <code className="bg-blue-100 px-1 rounded">apisdev-gw.beacukai.go.id</code>
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Dokumen Information Section */}
        {formData.statusValidasi === 'valid' && (
          <div>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-base font-medium text-gray-900">Informasi Dokumen</h3>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addDokumen}
                className="flex items-center"
              >
                <Plus className="w-4 h-4 mr-2" />
                Tambah Dokumen
              </Button>
            </div>

            <div className="space-y-4">
              {formData.dokumen.map((dok, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="text-sm font-medium text-gray-900">Dokumen {index + 1}</h4>
                    {formData.dokumen.length > 1 && (
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeDokumen(index)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Kode Dokumen */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Kode Dokumen <span className="text-red-500">*</span>
                      </label>
                      <Select
                        value={dok.kodeDokumen}
                        onValueChange={(value) => updateDokumen(index, 'kodeDokumen', value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Pilih kode dokumen" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="PLP">PLP - Persetujuan Lalu Lintas Pelabuhan</SelectItem>
                          <SelectItem value="BC11">BC 1.1 - Pemberitahuan Impor Barang</SelectItem>
                          <SelectItem value="BC23">BC 2.3 - Pemberitahuan Ekspor Barang</SelectItem>
                          <SelectItem value="BC16">BC 1.6 - Pemberitahuan Impor Sementara</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Kode Kantor */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Kode Kantor <span className="text-red-500">*</span>
                      </label>
                      <Input
                        placeholder="Contoh: 050100"
                        value={dok.kodeKantor}
                        onChange={(e) => updateDokumen(index, 'kodeKantor', e.target.value)}
                      />
                    </div>

                    {/* Nomor Dokumen */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Nomor Dokumen <span className="text-red-500">*</span>
                      </label>
                      <Input
                        placeholder="Nomor dokumen"
                        value={dok.nomorDokumen}
                        onChange={(e) => updateDokumen(index, 'nomorDokumen', e.target.value)}
                      />
                    </div>

                    {/* Tanggal Dokumen */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Tanggal Dokumen <span className="text-red-500">*</span>
                      </label>
                      <Input
                        type="date"
                        value={dok.tanggalDokumen}
                        onChange={(e) => updateDokumen(index, 'tanggalDokumen', e.target.value)}
                      />
                    </div>

                    {/* Jenis Muat */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Jenis Muat <span className="text-red-500">*</span>
                      </label>
                      <Select
                        value={dok.jenisMuat}
                        onValueChange={(value) => updateDokumen(index, 'jenisMuat', value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Pilih jenis muat" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="FCL">FCL - Full Container Load</SelectItem>
                          <SelectItem value="LCL">LCL - Less Container Load</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Jumlah Kontainer */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Jumlah Kontainer <span className="text-red-500">*</span>
                      </label>
                      <Input
                        type="number"
                        placeholder="1"
                        value={dok.jumlahKontainer}
                        onChange={(e) => updateDokumen(index, 'jumlahKontainer', e.target.value)}
                        min="1"
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Information Box */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-start">
            <div className="w-5 h-5 bg-blue-100 rounded-full flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">
              <span className="text-xs font-medium text-blue-600">i</span>
            </div>
            <div className="text-sm text-blue-800">
              <p className="font-medium mb-2">Tentang Validasi AJU:</p>
              <ul className="text-xs space-y-1">
                <li>• AJU (Angka Jaminan Utama) adalah nomor referensi dokumen pengiriman</li>
                <li>• Validasi diperlukan untuk memastikan kesesuaian dengan sistem bea cukai</li>
                <li>• Nomor AJU yang valid akan memungkinkan tracking real-time pengiriman</li>
                <li>• Informasi dokumen akan digunakan untuk integrasi dengan sistem Beacukai</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Empty State when no validation yet */}
        {formData.statusValidasi === 'pending' && (
          <div className="text-center py-8">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <FileCheck className="w-8 h-8 text-gray-400" />
            </div>
            <h4 className="text-sm font-medium text-gray-900 mb-2">Siap untuk Validasi</h4>
            <p className="text-xs text-gray-500 max-w-sm mx-auto">
              Masukkan nomor AJU dan klik tombol "Validasi" untuk memverifikasi dokumen pengiriman.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
