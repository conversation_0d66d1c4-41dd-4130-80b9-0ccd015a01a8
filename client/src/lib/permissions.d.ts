export declare const ac: {
    new<PERSON>ole<K extends "user" | "session" | "organization" | "member" | "invitation" | "team">(statements: import("better-auth/plugins/access").Subset<K, {
        readonly user: readonly ["create", "list", "update", "set-role", "ban", "impersonate", "delete", "set-password"];
        readonly session: readonly ["list", "revoke", "delete"];
        readonly organization: readonly ["update", "delete"];
        readonly member: readonly ["create", "update", "delete"];
        readonly invitation: readonly ["create", "cancel"];
        readonly team: readonly ["create", "update", "delete"];
    }>): {
        authorize<K_1 extends K>(request: K_1 extends infer T extends K_2 ? { [key in T]?: import("better-auth/plugins/access").Subset<K, {
            readonly user: readonly ["create", "list", "update", "set-role", "ban", "impersonate", "delete", "set-password"];
            readonly session: readonly ["list", "revoke", "delete"];
            readonly organization: readonly ["update", "delete"];
            readonly member: readonly ["create", "update", "delete"];
            readonly invitation: readonly ["create", "cancel"];
            readonly team: readonly ["create", "update", "delete"];
        }>[key] | {
            actions: import("better-auth/plugins/access").Subset<K, {
                readonly user: readonly ["create", "list", "update", "set-role", "ban", "impersonate", "delete", "set-password"];
                readonly session: readonly ["list", "revoke", "delete"];
                readonly organization: readonly ["update", "delete"];
                readonly member: readonly ["create", "update", "delete"];
                readonly invitation: readonly ["create", "cancel"];
                readonly team: readonly ["create", "update", "delete"];
            }>[key];
            connector: "OR" | "AND";
        } | undefined; } : never, connector?: "OR" | "AND"): import("better-auth/plugins/access").AuthorizeResponse;
        statements: import("better-auth/plugins/access").Subset<K, {
            readonly user: readonly ["create", "list", "update", "set-role", "ban", "impersonate", "delete", "set-password"];
            readonly session: readonly ["list", "revoke", "delete"];
            readonly organization: readonly ["update", "delete"];
            readonly member: readonly ["create", "update", "delete"];
            readonly invitation: readonly ["create", "cancel"];
            readonly team: readonly ["create", "update", "delete"];
        }>;
    };
    statements: {
        readonly user: readonly ["create", "list", "update", "set-role", "ban", "impersonate", "delete", "set-password"];
        readonly session: readonly ["list", "revoke", "delete"];
        readonly organization: readonly ["update", "delete"];
        readonly member: readonly ["create", "update", "delete"];
        readonly invitation: readonly ["create", "cancel"];
        readonly team: readonly ["create", "update", "delete"];
    };
};
export declare const superadmin: {
    authorize<K_1 extends "user" | "session" | "organization" | "member" | "invitation" | "team">(request: K_1 extends infer T extends K ? { [key in T]?: import("better-auth/plugins/access").Subset<"user" | "session" | "organization" | "member" | "invitation" | "team", {
        readonly user: readonly ["create", "list", "update", "set-role", "ban", "impersonate", "delete", "set-password"];
        readonly session: readonly ["list", "revoke", "delete"];
        readonly organization: readonly ["update", "delete"];
        readonly member: readonly ["create", "update", "delete"];
        readonly invitation: readonly ["create", "cancel"];
        readonly team: readonly ["create", "update", "delete"];
    }>[key] | {
        actions: import("better-auth/plugins/access").Subset<"user" | "session" | "organization" | "member" | "invitation" | "team", {
            readonly user: readonly ["create", "list", "update", "set-role", "ban", "impersonate", "delete", "set-password"];
            readonly session: readonly ["list", "revoke", "delete"];
            readonly organization: readonly ["update", "delete"];
            readonly member: readonly ["create", "update", "delete"];
            readonly invitation: readonly ["create", "cancel"];
            readonly team: readonly ["create", "update", "delete"];
        }>[key];
        connector: "OR" | "AND";
    } | undefined; } : never, connector?: "OR" | "AND"): import("better-auth/plugins/access").AuthorizeResponse;
    statements: import("better-auth/plugins/access").Subset<"user" | "session" | "organization" | "member" | "invitation" | "team", {
        readonly user: readonly ["create", "list", "update", "set-role", "ban", "impersonate", "delete", "set-password"];
        readonly session: readonly ["list", "revoke", "delete"];
        readonly organization: readonly ["update", "delete"];
        readonly member: readonly ["create", "update", "delete"];
        readonly invitation: readonly ["create", "cancel"];
        readonly team: readonly ["create", "update", "delete"];
    }>;
};
export declare const admin: {
    authorize<K_1 extends "user" | "organization" | "member" | "invitation" | "team">(request: K_1 extends infer T extends K ? { [key in T]?: import("better-auth/plugins/access").Subset<"user" | "organization" | "member" | "invitation" | "team", {
        readonly user: readonly ["create", "list", "update", "set-role", "ban", "impersonate", "delete", "set-password"];
        readonly session: readonly ["list", "revoke", "delete"];
        readonly organization: readonly ["update", "delete"];
        readonly member: readonly ["create", "update", "delete"];
        readonly invitation: readonly ["create", "cancel"];
        readonly team: readonly ["create", "update", "delete"];
    }>[key] | {
        actions: import("better-auth/plugins/access").Subset<"user" | "organization" | "member" | "invitation" | "team", {
            readonly user: readonly ["create", "list", "update", "set-role", "ban", "impersonate", "delete", "set-password"];
            readonly session: readonly ["list", "revoke", "delete"];
            readonly organization: readonly ["update", "delete"];
            readonly member: readonly ["create", "update", "delete"];
            readonly invitation: readonly ["create", "cancel"];
            readonly team: readonly ["create", "update", "delete"];
        }>[key];
        connector: "OR" | "AND";
    } | undefined; } : never, connector?: "OR" | "AND"): import("better-auth/plugins/access").AuthorizeResponse;
    statements: import("better-auth/plugins/access").Subset<"user" | "organization" | "member" | "invitation" | "team", {
        readonly user: readonly ["create", "list", "update", "set-role", "ban", "impersonate", "delete", "set-password"];
        readonly session: readonly ["list", "revoke", "delete"];
        readonly organization: readonly ["update", "delete"];
        readonly member: readonly ["create", "update", "delete"];
        readonly invitation: readonly ["create", "cancel"];
        readonly team: readonly ["create", "update", "delete"];
    }>;
};
export declare const member: {
    authorize<K_1 extends "organization" | "member" | "invitation" | "team">(request: K_1 extends infer T extends K ? { [key in T]?: import("better-auth/plugins/access").Subset<"organization" | "member" | "invitation" | "team", {
        readonly user: readonly ["create", "list", "update", "set-role", "ban", "impersonate", "delete", "set-password"];
        readonly session: readonly ["list", "revoke", "delete"];
        readonly organization: readonly ["update", "delete"];
        readonly member: readonly ["create", "update", "delete"];
        readonly invitation: readonly ["create", "cancel"];
        readonly team: readonly ["create", "update", "delete"];
    }>[key] | {
        actions: import("better-auth/plugins/access").Subset<"organization" | "member" | "invitation" | "team", {
            readonly user: readonly ["create", "list", "update", "set-role", "ban", "impersonate", "delete", "set-password"];
            readonly session: readonly ["list", "revoke", "delete"];
            readonly organization: readonly ["update", "delete"];
            readonly member: readonly ["create", "update", "delete"];
            readonly invitation: readonly ["create", "cancel"];
            readonly team: readonly ["create", "update", "delete"];
        }>[key];
        connector: "OR" | "AND";
    } | undefined; } : never, connector?: "OR" | "AND"): import("better-auth/plugins/access").AuthorizeResponse;
    statements: import("better-auth/plugins/access").Subset<"organization" | "member" | "invitation" | "team", {
        readonly user: readonly ["create", "list", "update", "set-role", "ban", "impersonate", "delete", "set-password"];
        readonly session: readonly ["list", "revoke", "delete"];
        readonly organization: readonly ["update", "delete"];
        readonly member: readonly ["create", "update", "delete"];
        readonly invitation: readonly ["create", "cancel"];
        readonly team: readonly ["create", "update", "delete"];
    }>;
};
export declare const roles: {
    superadmin: {
        authorize<K_1 extends "user" | "session" | "organization" | "member" | "invitation" | "team">(request: K_1 extends infer T extends K ? { [key in T]?: import("better-auth/plugins/access").Subset<"user" | "session" | "organization" | "member" | "invitation" | "team", {
            readonly user: readonly ["create", "list", "update", "set-role", "ban", "impersonate", "delete", "set-password"];
            readonly session: readonly ["list", "revoke", "delete"];
            readonly organization: readonly ["update", "delete"];
            readonly member: readonly ["create", "update", "delete"];
            readonly invitation: readonly ["create", "cancel"];
            readonly team: readonly ["create", "update", "delete"];
        }>[key] | {
            actions: import("better-auth/plugins/access").Subset<"user" | "session" | "organization" | "member" | "invitation" | "team", {
                readonly user: readonly ["create", "list", "update", "set-role", "ban", "impersonate", "delete", "set-password"];
                readonly session: readonly ["list", "revoke", "delete"];
                readonly organization: readonly ["update", "delete"];
                readonly member: readonly ["create", "update", "delete"];
                readonly invitation: readonly ["create", "cancel"];
                readonly team: readonly ["create", "update", "delete"];
            }>[key];
            connector: "OR" | "AND";
        } | undefined; } : never, connector?: "OR" | "AND"): import("better-auth/plugins/access").AuthorizeResponse;
        statements: import("better-auth/plugins/access").Subset<"user" | "session" | "organization" | "member" | "invitation" | "team", {
            readonly user: readonly ["create", "list", "update", "set-role", "ban", "impersonate", "delete", "set-password"];
            readonly session: readonly ["list", "revoke", "delete"];
            readonly organization: readonly ["update", "delete"];
            readonly member: readonly ["create", "update", "delete"];
            readonly invitation: readonly ["create", "cancel"];
            readonly team: readonly ["create", "update", "delete"];
        }>;
    };
    admin: {
        authorize<K_1 extends "user" | "organization" | "member" | "invitation" | "team">(request: K_1 extends infer T extends K ? { [key in T]?: import("better-auth/plugins/access").Subset<"user" | "organization" | "member" | "invitation" | "team", {
            readonly user: readonly ["create", "list", "update", "set-role", "ban", "impersonate", "delete", "set-password"];
            readonly session: readonly ["list", "revoke", "delete"];
            readonly organization: readonly ["update", "delete"];
            readonly member: readonly ["create", "update", "delete"];
            readonly invitation: readonly ["create", "cancel"];
            readonly team: readonly ["create", "update", "delete"];
        }>[key] | {
            actions: import("better-auth/plugins/access").Subset<"user" | "organization" | "member" | "invitation" | "team", {
                readonly user: readonly ["create", "list", "update", "set-role", "ban", "impersonate", "delete", "set-password"];
                readonly session: readonly ["list", "revoke", "delete"];
                readonly organization: readonly ["update", "delete"];
                readonly member: readonly ["create", "update", "delete"];
                readonly invitation: readonly ["create", "cancel"];
                readonly team: readonly ["create", "update", "delete"];
            }>[key];
            connector: "OR" | "AND";
        } | undefined; } : never, connector?: "OR" | "AND"): import("better-auth/plugins/access").AuthorizeResponse;
        statements: import("better-auth/plugins/access").Subset<"user" | "organization" | "member" | "invitation" | "team", {
            readonly user: readonly ["create", "list", "update", "set-role", "ban", "impersonate", "delete", "set-password"];
            readonly session: readonly ["list", "revoke", "delete"];
            readonly organization: readonly ["update", "delete"];
            readonly member: readonly ["create", "update", "delete"];
            readonly invitation: readonly ["create", "cancel"];
            readonly team: readonly ["create", "update", "delete"];
        }>;
    };
    member: {
        authorize<K_1 extends "organization" | "member" | "invitation" | "team">(request: K_1 extends infer T extends K ? { [key in T]?: import("better-auth/plugins/access").Subset<"organization" | "member" | "invitation" | "team", {
            readonly user: readonly ["create", "list", "update", "set-role", "ban", "impersonate", "delete", "set-password"];
            readonly session: readonly ["list", "revoke", "delete"];
            readonly organization: readonly ["update", "delete"];
            readonly member: readonly ["create", "update", "delete"];
            readonly invitation: readonly ["create", "cancel"];
            readonly team: readonly ["create", "update", "delete"];
        }>[key] | {
            actions: import("better-auth/plugins/access").Subset<"organization" | "member" | "invitation" | "team", {
                readonly user: readonly ["create", "list", "update", "set-role", "ban", "impersonate", "delete", "set-password"];
                readonly session: readonly ["list", "revoke", "delete"];
                readonly organization: readonly ["update", "delete"];
                readonly member: readonly ["create", "update", "delete"];
                readonly invitation: readonly ["create", "cancel"];
                readonly team: readonly ["create", "update", "delete"];
            }>[key];
            connector: "OR" | "AND";
        } | undefined; } : never, connector?: "OR" | "AND"): import("better-auth/plugins/access").AuthorizeResponse;
        statements: import("better-auth/plugins/access").Subset<"organization" | "member" | "invitation" | "team", {
            readonly user: readonly ["create", "list", "update", "set-role", "ban", "impersonate", "delete", "set-password"];
            readonly session: readonly ["list", "revoke", "delete"];
            readonly organization: readonly ["update", "delete"];
            readonly member: readonly ["create", "update", "delete"];
            readonly invitation: readonly ["create", "cancel"];
            readonly team: readonly ["create", "update", "delete"];
        }>;
    };
};
