# plugins: Organization
URL: /docs/plugins/organization
Source: https://raw.githubusercontent.com/better-auth/better-auth/refs/heads/main/docs/content/docs/plugins/organization.mdx

The organization plugin allows you to manage your organization's members and teams.
        
***

title: Organization
description: The organization plugin allows you to manage your organization's members and teams.
------------------------------------------------------------------------------------------------

Organizations simplifies user access and permissions management. Assign roles and permissions to streamline project management, team coordination, and partnerships.

## Installation

<Steps>
  <Step>
    ### Add the plugin to your **auth** config

    ```ts title="auth.ts"
    import { betterAuth } from "better-auth"
    import { organization } from "better-auth/plugins"

    export const auth = betterAuth({
        plugins: [ // [!code highlight]
            organization() // [!code highlight]
        ] // [!code highlight]
    })
    ```
  </Step>

  <Step>
    ### Migrate the database

    Run the migration or generate the schema to add the necessary fields and tables to the database.

    <Tabs items={["migrate", "generate"]}>
      <Tab value="migrate">
        ```bash
        npx @better-auth/cli migrate
        ```
      </Tab>

      <Tab value="generate">
        ```bash
        npx @better-auth/cli generate
        ```
      </Tab>
    </Tabs>

    See the [Schema](#schema) section to add the fields manually.
  </Step>

  <Step>
    ### Add the client plugin

    ```ts title="auth-client.ts"
    import { createAuthClient } from "better-auth/client"
    import { organizationClient } from "better-auth/client/plugins"

    export const authClient = createAuthClient({
        plugins: [ // [!code highlight]
            organizationClient() // [!code highlight]
        ] // [!code highlight]
    })
    ```
  </Step>
</Steps>

## Usage

Once you've installed the plugin, you can start using the organization plugin to manage your organization's members and teams. The client plugin will provide you methods under the `organization` namespace. And the server `api` will provide you with the necessary endpoints to manage your organization and gives you easier way to call the functions on your own backend.

## Organization

### Create an organization

<APIMethod path="/organization/create" method="POST" requireSession>
  ```ts
  const metadata = { someKey: "someValue" };

  type createOrganization = {
    /**
     * The organization name.
    */
    name: string = "My Organization"
    /**
     * The organization slug.
    */
    slug: string = "my-org"
    /**
     * The organization logo.
    */
    logo?: string = "https://example.com/logo.png"
    /**
    * The metadata of the organization.
    */
    metadata?: Record<string, any>
    /**
    * The user id of the organization creator. If not provided, the current user will be used. Should only be used by admins or when called by the server.
    * @serverOnly
    */
    userId?: string = "some_user_id"
    /**
    * Whether to keep the current active organization active after creating a new one.
    */
    keepCurrentActiveOrganization?: boolean = false
  }
  ```
</APIMethod>

#### Restrict who can create an organization

By default, any user can create an organization. To restrict this, set the `allowUserToCreateOrganization` option to a function that returns a boolean, or directly to `true` or `false`.

```ts title="auth.ts"
import { betterAuth } from "better-auth"
import { organization } from "better-auth/plugins"

const auth = betterAuth({
    //...
    plugins: [
        organization({
            allowUserToCreateOrganization: async (user) => { // [!code highlight]
                const subscription = await getSubscription(user.id) // [!code highlight]
                return subscription.plan === "pro" // [!code highlight]
            } // [!code highlight]
        })
    ]
})
```

#### Check if organization slug is taken

To check if an organization slug is taken or not you can use the `checkSlug` function provided by the client. The function takes an object with the following properties:

<APIMethod path="/organization/check-slug" method="POST">
  ```ts
  type checkOrganizationSlug = {
      /**
       * The organization slug to check.  
       */
      slug: string = "my-org"
  }
  ```
</APIMethod>

#### Organization Creation Hooks

You can customize the organization creation process using hooks that run before and after an organization is created.

```ts title="auth.ts"
import { betterAuth } from "better-auth"
import { organization } from "better-auth/plugins"

export const auth = betterAuth({
    plugins: [
        organization({
            organizationCreation: {
                disabled: false, // Set to true to disable organization creation
                beforeCreate: async ({ organization, user }, request) => {
                    // Run custom logic before organization is created
                    // Optionally modify the organization data
                    return {
                        data: {
                            ...organization,
                            metadata: {
                                customField: "value"
                            }
                        }
                    }
                },
                afterCreate: async ({ organization, member, user }, request) => {
                    // Run custom logic after organization is created
                    // e.g., create default resources, send notifications
                    await setupDefaultResources(organization.id)
                }
            }
        })
    ]
})
```

The `beforeCreate` hook runs before an organization is created. It receives:

* `organization`: The organization data (without ID)
* `user`: The user creating the organization
* `request`: The HTTP request object (optional)

Return an object with `data` property to modify the organization data that will be created.

The `afterCreate` hook runs after an organization is successfully created. It receives:

* `organization`: The created organization (with ID)
* `member`: The member record for the creator
* `user`: The user who created the organization
* `request`: The HTTP request object (optional)

### List User's Organizations

To list the organizations that a user is a member of, you can use `useListOrganizations` hook. It implements a reactive way to get the organizations that the user is a member of.

<Tabs items={["React", "Vue", "Svelte"]} defaultValue="React">
  <Tab value="React">
    ```tsx title="client.tsx"
    import { authClient } from "@/lib/auth-client"

    function App(){
        const { data: organizations } = authClient.useListOrganizations()
        return (
            <div>
                {organizations.map(org => <p>{org.name}</p>)}
            </div>
        )
    }
    ```
  </Tab>

  <Tab value="Svelte">
    ```svelte title="page.svelte"
    <script lang="ts">
      import { authClient } from "$lib/auth-client";
      const organizations = authClient.useListOrganizations();
    </script>

    <h1>Organizations</h1>s

    {#if $organizations.isPending}
      <p>Loading...</p>
    {:else if $organizations.data === null}
      <p>No organizations found.</p>
    {:else}
      <ul>
        {#each $organizations.data as organization}
          <li>{organization.name}</li>
        {/each}
      </ul>
    {/if}
    ```
  </Tab>

  <Tab value="Vue">
    ```vue title="organization.vue"
    <script lang="ts">;
    export default {
        setup() {
            const organizations = authClient.useListOrganizations()
            return { organizations };
        }
    };
    </script>

    <template>
        <div>
            <h1>Organizations</h1>
            <div v-if="organizations.isPending">Loading...</div>
            <div v-else-if="organizations.data === null">No organizations found.</div>
            <ul v-else>
                <li v-for="organization in organizations.data" :key="organization.id">
                    {{ organization.name }}
                </li>
            </ul>
        </div>
    </template>
    ```
  </Tab>
</Tabs>

Or alternatively, you can call `organization.list` if you don't want to use a hook.

<APIMethod path="/organization/list" method="GET">
  ```ts
  type listOrganizations = {
  }
  ```
</APIMethod>

### Active Organization

Active organization is the workspace the user is currently working on. By default when the user is signed in the active organization is set to `null`. You can set the active organization to the user session.

<Callout type="info">
  It's not always you want to persist the active organization in the session. You can manage the active organization in the client side only. For example, multiple tabs can have different active organizations.
</Callout>

#### Set Active Organization

You can set the active organization by calling the `organization.setActive` function. It'll set the active organization for the user session.

<Callout>
  In some applications, you may want the ability to un-set an active organization.
  In this case, you can call this endpoint with `organizationId` set to `null`.
</Callout>

<APIMethod path="/organization/set-active" method="POST">
  ```ts
  type setActiveOrganization = {
      /**
       * The organization id to set as active. It can be null to unset the active organization.  
       */
      organizationId?: string | null = "org-id"
      /**
       * The organization slug to set as active. It can be null to unset the active organization if organizationId is not provided.  
       */
      organizationSlug?: string = "org-slug"
  }
  ```
</APIMethod>

To set active organization when a session is created you can use [database hooks](/docs/concepts/database#database-hooks).

```ts title="auth.ts"
export const auth = betterAuth({
  databaseHooks: {
      session: {
          create: {
              before: async(session)=>{
                  const organization = await getActiveOrganization(session.userId)
                  return {
                    data: {
                      ...session,
                      activeOrganizationId: organization.id
                    }
                  }
              }
          }
      }
  }
})
```

#### Use Active Organization

To retrieve the active organization for the user, you can call the `useActiveOrganization` hook. It returns the active organization for the user. Whenever the active organization changes, the hook will re-evaluate and return the new active organization.

<Tabs items={['React', 'Vue', 'Svelte']}>
  <Tab value="React">
    ```tsx title="client.tsx"
    import { authClient } from "@/lib/auth-client"

    function App(){
        const { data: activeOrganization } = authClient.useActiveOrganization()
        return (
            <div>
                {activeOrganization ? <p>{activeOrganization.name}</p> : null}
            </div>
        )
    }
    ```
  </Tab>

  <Tab value="Svelte">
    ```tsx title="client.tsx"
    <script lang="ts">
    import { authClient } from "$lib/auth-client";
    const activeOrganization = authClient.useActiveOrganization();
    </script>

    <h2>Active Organization</h2>

    {#if $activeOrganization.isPending}
    <p>Loading...</p>
    {:else if $activeOrganization.data === null}
    <p>No active organization found.</p>
    {:else}
    <p>{$activeOrganization.data.name}</p>
    {/if}
    ```
  </Tab>

  <Tab value="Vue">
    ```vue title="organization.vue"
    <script lang="ts">;
    export default {
        setup() {
            const activeOrganization = authClient.useActiveOrganization();
            return { activeOrganization };
        }
    };
    </script>

    <template>
        <div>
            <h2>Active organization</h2>
            <div v-if="activeOrganization.isPending">Loading...</div>
            <div v-else-if="activeOrganization.data === null">No active organization.</div>
            <div v-else>
                {{ activeOrganization.data.name }}
            </div>
        </div>
    </template>
    ```
  </Tab>
</Tabs>

### Get Full Organization

To get the full details of an organization, you can use the `getFullOrganization` function.
By default, if you don't pass any properties, it will use the active organization.

<APIMethod path="/organization/get-full-organization" method="GET" requireSession>
  ```ts
  type getFullOrganization = {
      /**
       * The organization id to get. By default, it will use the active organization.  
       */
      organizationId?: string = "org-id"
      /**
       * The organization slug to get.  
       */
      organizationSlug?: string = "org-slug"
      /**
       * The limit of members to get. By default, it uses the membershipLimit option which defaults to 100.
       */
      membersLimit?: number = 100
  }
  ```
</APIMethod>

### Update Organization

To update organization info, you can use `organization.update`

<APIMethod path="/organization/update" method="POST" requireSession>
  ```ts
  type updateOrganization = {
      /**
       * A partial list of data to update the organization. 
       */
      data: {
          /**
           * The name of the organization. 
           */
          name?: string = "updated-name"
          /**
           * The slug of the organization. 
           */
          slug?: string = "updated-slug"
          /**
           * The logo of the organization. 
           */
          logo?: string = "new-logo.url"
          /**
           * The metadata of the organization. 
           */
          metadata?: Record<string, any> | null = { customerId: "test" }
      }
      /**
       * The organization ID. to update.
       */
      organizationId?: string = "org-id"
  }
  ```
</APIMethod>

### Delete Organization

To remove user owned organization, you can use `organization.delete`

<APIMethod path="/organization/delete" method="POST" requireSession>
  ```ts
  type deleteOrganization = {
      /*
      * The organization id to delete.
      */
      organizationId: string = "org-id"
  }
  ```
</APIMethod>

If the user has the necessary permissions (by default: role is owner) in the specified organization, all members, invitations and organization information will be removed.

You can configure how organization deletion is handled through `organizationDeletion` option:

```ts
const auth = betterAuth({
  plugins: [
    organization({
      organizationDeletion: {
        disabled: true, //to disable it altogether
        beforeDelete: async (data, request) => {
          // a callback to run before deleting org
        },
        afterDelete: async (data, request) => {
          // a callback to run after deleting org
        },
      },
    }),
  ],
});
```

## Invitations

To add a member to an organization, we first need to send an invitation to the user. The user will receive an email/sms with the invitation link. Once the user accepts the invitation, they will be added to the organization.

### Setup Invitation Email

For member invitation to work we first need to provide `sendInvitationEmail` to the `better-auth` instance. This function is responsible for sending the invitation email to the user.

You'll need to construct and send the invitation link to the user. The link should include the invitation ID, which will be used with the acceptInvitation function when the user clicks on it.

```ts title="auth.ts"
import { betterAuth } from "better-auth"
import { organization } from "better-auth/plugins"
import { sendOrganizationInvitation } from "./email"
export const auth = betterAuth({
	plugins: [
		organization({
			async sendInvitationEmail(data) {
        const inviteLink = `https://example.com/accept-invitation/${data.id}`
				sendOrganizationInvitation({
					  email: data.email,
						invitedByUsername: data.inviter.user.name,
						invitedByEmail: data.inviter.user.email,
						teamName: data.organization.name,
						inviteLink
				})
			},
		}),
	],
});
```

### Send Invitation

To invite users to an organization, you can use the `invite` function provided by the client. The `invite` function takes an object with the following properties:

<APIMethod path="/organization/invite-member" method="POST">
  ```ts
  type createInvitation = {
      /**
       * The email address of the user to invite.  
       */
      email: string = "<EMAIL>"
      /**
       * The role(s) to assign to the user. It can be `admin`, `member`, or `guest`.  
       */
      role: string | string[] = "member"
      /**
       * The organization ID to invite the user to. Defaults to the active organization.  
       */
      organizationId?: string = "org-id"
      /**
       * Resend the invitation email, if the user is already invited.  
       */
      resend?: boolean = true
      /**
       * The team ID to invite the user to.  
       */
      teamId?: string = "team-id"
  }
  ```
</APIMethod>

<Callout>
  * If the user is already a member of the organization, the invitation will be canceled.
  * If the user is already invited to the organization, unless `resend` is set to `true`, the invitation will not be sent again.
  * If `cancelPendingInvitationsOnReInvite` is set to `true`, the invitation will be canceled if the user is already invited to the organization and a new invitation is sent.
</Callout>

### Accept Invitation

When a user receives an invitation email, they can click on the invitation link to accept the invitation. The invitation link should include the invitation ID, which will be used to accept the invitation.

Make sure to call the `acceptInvitation` function after the user is logged in.

<APIMethod path="/organization/accept-invitation" method="POST">
  ```ts
  type acceptInvitation = {
      /**
       * The ID of the invitation to accept.  
       */
      invitationId: string = "invitation-id"
  }
  ```
</APIMethod>

### Cancel Invitation

If a user has sent out an invitation, you can use this method to cancel it.

If you're looking for how a user can reject an invitation, you can find that [here](#reject-invitation).

<APIMethod path="/organization/cancel-invitation" method="POST" noResult>
  ```ts
  type cancelInvitation = {
      /**
       * The ID of the invitation to cancel.  
       */
      invitationId: string = "invitation-id"
  }
  ```
</APIMethod>

### Reject Invitation

If this user has received an invitation, but wants to decline it, this method will allow you to do so by rejecting it.

<APIMethod path="/organization/reject-invitation" method="POST" noResult>
  ```ts
  type rejectInvitation = {
      /**
       * The ID of the invitation to reject.  
       */
      invitationId: string = "invitation-id"
  }
  ```
</APIMethod>

### Get Invitation

To get an invitation you can use the `organization.getInvitation` function provided by the client. You need to provide the invitation id as a query parameter.

<APIMethod path="/organization/get-invitation" method="GET" requireSession>
  ```ts
  type getInvitation = {
      /**
       * The ID of the invitation to get.  
       */
      id: string = "invitation-id"
  }
  ```
</APIMethod>

### List Invitations

To list all invitations for a given organization you can use the `listInvitations` function provided by the client.

<APIMethod path="/organization/list-invitations" method="GET">
  ```ts
  type listInvitations = {
      /**
       * An optional ID of the organization to list invitations for. If not provided, will default to the users active organization. 
       */
      organizationId?: string = "organization-id"
  }
  ```
</APIMethod>

### List user invitations

To list all invitations for a given user you can use the `listUserInvitations` function provided by the client.

```ts title="auth-client.ts"
const invitations = await authClient.organization.listUserInvitations()
```

On the server, you can pass the user ID as a query parameter.

```ts title="api.ts"
const invitations = await auth.api.listUserInvitations({
    query: {
        email: "<EMAIL>"
    }
})
```

<Callout type="warn">
  The `email` query parameter is only available on the server to query for invitations for a specific user.
</Callout>

## Members

### List Members

To list all members of an organization you can use the `listMembers` function.

<APIMethod path="/organization/list-members" method="GET">
  ```ts
  type listMembers = {
      /**
       * An optional organization ID to list members for. If not provided, will default to the user's active organization. 
       */
      organizationId?: string = "organization-id"
      /**
       * The limit of members to return.
       */
      limit?: number = 100
      /**
       * The offset to start from.
       */
      offset?: number = 0
      /**
       * The field to sort by.
       */
      sortBy?: string = "createdAt"
      /**
       * The direction to sort by.
       */
      sortDirection?: "asc" | "desc" = "desc"
      /**
       * The field to filter by.
       */
      filterField?: string = "createdAt"
      /**
       * The operator to filter by.
       */
      filterOperator?: "eq" | "ne" | "gt" | "gte" | "lt" | "lte" | "in" | "nin" | "contains" = "eq"
      /**
       * The value to filter by.
       */
      filterValue?: string = "value"
      /**
  }
  ```
</APIMethod>

### Remove Member

To remove you can use `organization.removeMember`

<APIMethod path="/organization/remove-member" method="POST">
  ```ts
  type removeMember = {
      /**
       * The ID or email of the member to remove. 
       */
      memberIdOrEmail: string = "<EMAIL>"
      /**
       * The ID of the organization to remove the member from. If not provided, the active organization will be used. 
       */
      organizationId?: string = "org-id"
  }
  ```
</APIMethod>

### Update Member Role

To update the role of a member in an organization, you can use the `organization.updateMemberRole`. If the user has the permission to update the role of the member, the role will be updated.

<APIMethod path="/organization/update-member-role" method="POST" noResult>
  ```ts
  type updateMemberRole = {
      /**
       * The new role to be applied. This can be a string or array of strings representing the roles. 
       */
      role: string | string[] = ["admin", "sale"]
      /**
       * The member id to apply the role update to. 
       */
      memberId: string = "member-id"
      /**
       * An optional organization ID which the member is a part of to apply the role update. If not provided, you must provide session headers to get the active organization. 
       */
      organizationId?: string = "organization-id"
  }
  ```
</APIMethod>

### Get Active Member

To get the current member of the active organization you can use the `organization.getActiveMember` function. This function will return the user's member details in their active organization.

<APIMethod path="/organization/get-active-member" method="GET" requireSession resultVariable="member">
  ```ts
  type getActiveMember = {
  }
  ```
</APIMethod>

### Add Member

If you want to add a member directly to an organization without sending an invitation, you can use the `addMember` function which can only be invoked on the server.

<APIMethod path="/organization/add-member" method="POST" isServerOnly>
  ```ts
  type addMember = {
      /**
       * The user Id which represents the user to be added as a member. If `null` is provided, then it's expected to provide session headers. 
       */
      userId?: string | null = "user-id"
      /**
       * The role(s) to assign to the new member. 
       */
      role: string | string[] = ["admin", "sale"]
      /**
       * An optional organization ID to pass. If not provided, will default to the user's active organization. 
       */
      organizationId?: string = "org-id"
      /**
       * An optional team ID to add the member to. 
       */
      teamId?: string = "team-id"
  }
  ```
</APIMethod>

### Leave Organization

To leave organization you can use `organization.leave` function. This function will remove the current user from the organization.

<APIMethod path="/organization/leave" method="POST" requireSession noResult>
  ```ts
  type leaveOrganization = {
      /**
       * The organization Id for the member to leave. 
       */
      organizationId: string = "organization-id"
  }
  ```
</APIMethod>

## Access Control

The organization plugin providers a very flexible access control system. You can control the access of the user based on the role they have in the organization. You can define your own set of permissions based on the role of the user.

### Roles

By default, there are three roles in the organization:

`owner`: The user who created the organization by default. The owner has full control over the organization and can perform any action.

`admin`: Users with the admin role have full control over the organization except for deleting the organization or changing the owner.

`member`: Users with the member role have limited control over the organization. They can create projects, invite users, and manage projects they have created.

<Callout>
  A user can have multiple roles. Multiple roles are stored as string separated by comma (",").
</Callout>

### Permissions

By default, there are three resources, and these have two to three actions.

**organization**:

`update` `delete`

**member**:

`create` `update` `delete`

**invitation**:

`create` `cancel`

The owner has full control over all the resources and actions. The admin has full control over all the resources except for deleting the organization or changing the owner. The member has no control over any of those actions other than reading the data.

### Custom Permissions

The plugin provides an easy way to define your own set of permissions for each role.

<Steps>
  <Step>
    #### Create Access Control

    You first need to create access controller by calling `createAccessControl` function and passing the statement object. The statement object should have the resource name as the key and the array of actions as the value.

    ```ts title="permissions.ts"
    import { createAccessControl } from "better-auth/plugins/access";

    /**
     * make sure to use `as const` so typescript can infer the type correctly
     */
    const statement = { // [!code highlight]
        project: ["create", "share", "update", "delete"], // [!code highlight]
    } as const; // [!code highlight]

    const ac = createAccessControl(statement); // [!code highlight]
    ```
  </Step>

  <Step>
    #### Create Roles

    Once you have created the access controller you can create roles with the permissions you have defined.

    ```ts title="permissions.ts"
    import { createAccessControl } from "better-auth/plugins/access";

    const statement = {
        project: ["create", "share", "update", "delete"],
    } as const;

    const ac = createAccessControl(statement);

    const member = ac.newRole({ // [!code highlight]
        project: ["create"], // [!code highlight]
    }); // [!code highlight]

    const admin = ac.newRole({ // [!code highlight]
        project: ["create", "update"], // [!code highlight]
    }); // [!code highlight]

    const owner = ac.newRole({ // [!code highlight]
        project: ["create", "update", "delete"], // [!code highlight]
    }); // [!code highlight]

    const myCustomRole = ac.newRole({ // [!code highlight]
        project: ["create", "update", "delete"], // [!code highlight]
        organization: ["update"], // [!code highlight]
    }); // [!code highlight]
    ```

    When you create custom roles for existing roles, the predefined permissions for those roles will be overridden. To add the existing permissions to the custom role, you need to import `defaultStatements` and merge it with your new statement, plus merge the roles' permissions set with the default roles.

    ```ts title="permissions.ts"
    import { createAccessControl } from "better-auth/plugins/access";
    import { defaultStatements, adminAc } from 'better-auth/plugins/organization/access'

    const statement = {
        ...defaultStatements, // [!code highlight]
        project: ["create", "share", "update", "delete"],
    } as const;

    const ac = createAccessControl(statement);

    const admin = ac.newRole({
        project: ["create", "update"],
        ...adminAc.statements, // [!code highlight]
    });
    ```
  </Step>

  <Step>
    #### Pass Roles to the Plugin

    Once you have created the roles you can pass them to the organization plugin both on the client and the server.

    ```ts title="auth.ts"
    import { betterAuth } from "better-auth"
    import { organization } from "better-auth/plugins"
    import { ac, owner, admin, member } from "@/auth/permissions"

    export const auth = betterAuth({
        plugins: [
            organization({
                ac,
                roles: {
                    owner,
                    admin,
                    member,
                    myCustomRole
                }
            }),
        ],
    });
    ```

    You also need to pass the access controller and the roles to the client plugin.

    ```ts title="auth-client"
    import { createAuthClient } from "better-auth/client"
    import { organizationClient } from "better-auth/client/plugins"
    import { ac, owner, admin, member, myCustomRole } from "@/auth/permissions"

    export const authClient = createAuthClient({
        plugins: [
            organizationClient({
                ac,
                roles: {
                    owner,
                    admin,
                    member,
                    myCustomRole
                }
            })
      ]
    })
    ```
  </Step>
</Steps>

### Access Control Usage

**Has Permission**:

You can use the `hasPermission` action provided by the `api` to check the permission of the user.

```ts title="api.ts"
import { auth } from "@/auth";

await auth.api.hasPermission({
  headers: await headers(),
    body: {
      permissions: {
        project: ["create"] // This must match the structure in your access control
      }
    }
});

// You can also check multiple resource permissions at the same time
await auth.api.hasPermission({
  headers: await headers(),
    body: {
      permissions: {
        project: ["create"], // This must match the structure in your access control
        sale: ["create"]
      }
    }
});
```

If you want to check the permission of the user on the client from the server you can use the `hasPermission` function provided by the client.

```ts title="auth-client.ts"
const canCreateProject = await authClient.organization.hasPermission({
    permissions: {
        project: ["create"]
    }
})

// You can also check multiple resource permissions at the same time
const canCreateProjectAndCreateSale = await authClient.organization.hasPermission({
    permissions: {
        project: ["create"],
        sale: ["create"]
    }
})
```

**Check Role Permission**:

Once you have defined the roles and permissions to avoid checking the permission from the server you can use the `checkRolePermission` function provided by the client.

```ts title="auth-client.ts"
const canCreateProject = authClient.organization.checkRolePermission({
	permissions: {
		organization: ["delete"],
	},
	role: "admin",
});

// You can also check multiple resource permissions at the same time
const canCreateProjectAndCreateSale = authClient.organization.checkRolePermission({
	permissions: {
		organization: ["delete"],
    member: ["delete"]
	},
	role: "admin",
});
```

## Teams

Teams allow you to group members within an organization. The teams feature provides additional organization structure and can be used to manage permissions at a more granular level.

### Enabling Teams

To enable teams, pass the `teams` configuration option to both server and client plugins:

```ts title="auth.ts"
import { betterAuth } from "better-auth"
import { organization } from "better-auth/plugins"

export const auth = betterAuth({
    plugins: [
        organization({
            teams: {
                enabled: true,
                maximumTeams: 10, // Optional: limit teams per organization
                allowRemovingAllTeams: false // Optional: prevent removing the last team
            }
        })
    ]
})
```

```ts title="auth-client.ts"
import { createAuthClient } from "better-auth/client"
import { organizationClient } from "better-auth/client/plugins"

export const authClient = createAuthClient({
    plugins: [
        organizationClient({
            teams: {
                enabled: true
            }
        })
    ]
})
```

### Managing Teams

#### Create Team

Create a new team within an organization:

<APIMethod path="/organization/create-team" method="POST">
  ```ts
  type createTeam = {
      /**
       * The name of the team. 
       */
      name: string = "my-team"
      /**
       * The organization ID which the team will be created in. Defaults to the active organization. 
       */
      organizationId?: string = "organization-id"
  }
  ```
</APIMethod>

#### List Teams

Get all teams in an organization:

<APIMethod path="/organization/list-teams" method="GET" requireSession>
  ```ts
  type listOrganizationTeams = {
      /**
       * The organization ID which the teams are under to list. Defaults to the users active organization. 
       */
      organizationId?: string = "organziation-id"
  }
  ```
</APIMethod>

#### Update Team

Update a team's details:

<APIMethod path="/organization/update-team" method="POST" requireSession>
  ```ts
  type updateTeam = {
      /**
       * The ID of the team to be updated. 
       */
      teamId: string = "team-id"
      /**
       * A partial object containing options for you to update.
       */
      data: {
          /**
           * The name of the team to be updated.
           */
          name?: string = "My new team name"
          /**
           * The organization ID which the team falls under.
           */
          organizationId?: string = "My new organization ID for this team"
          /**
           * The timestamp of when the team was created.
           */
          createdAt?: Date = new Date()
          /**
           * The timestamp of when the team was last updated.
           */
          updatedAt?: Date = new Date()
      }
  }
  ```
</APIMethod>

#### Remove Team

Delete a team from an organization:

<APIMethod path="/organization/remove-team" method="POST">
  ```ts
  type removeTeam = {
      /**
       * The team ID of the team to remove. 
       */
      teamId: string = "team-id"
      /**
       * The organization ID which the team falls under. If not provided, it will default to the user's active organization. 
       */
      organizationId?: string = "organization-id"
  }
  ```
</APIMethod>

#### Set Active Team

Sets the given team as the current active team. If `teamId` is `null` the current active team is unset.

<APIMethod path="/organization/set-active-team" method="POST">
  ```ts
  type setActiveTeam = {
      /**
       * The team ID of the team to set as the current active team.
       */
      teamId?: string = "team-id"
  }
  ```
</APIMethod>

#### List User Teams

List all teams that the current user is a part of.

<APIMethod path="/organization/list-user-teams" method="GET">
  ```ts
  type listUserTeams = {
  }
  ```
</APIMethod>

#### List Team Members

List the members of the given team.

<APIMethod path="/organization/list-team-members" method="POST">
  ```ts
  type listTeamMembers = {
      /**
       * The team whose members we should return. If this is not provided the members of the current active team get returned.
       */
      teamId?: string = "team-id"
  }
  ```
</APIMethod>

#### Add Team Member

Add a member to a team.

<APIMethod path="/organization/add-team-member" method="POST">
  ```ts
  type addTeamMember = {
      /**
       * The team the user should be a member of.
       */
      teamId: string = "team-id"
      /**
       * The user Id which represents the user to be added as a member.
       */
      userId: string = "user-id"
  }
  ```
</APIMethod>

#### Remove Team Member

Remove a member from a team.

<APIMethod path="/organization/remove-team-member" method="POST">
  ```ts
  type removeTeamMember = {
      /**
       * The team the user should be removed from.
       */
      teamId: string = "team-id"
      /**
       * The user which should be removed from the team.
       */
      userId: string = "user-id"
  }
  ```
</APIMethod>

### Team Permissions

Teams follow the organization's permission system. To manage teams, users need the following permissions:

* `team:create` - Create new teams
* `team:update` - Update team details
* `team:delete` - Remove teams

By default:

* Organization owners and admins can manage teams
* Regular members cannot create, update, or delete teams

### Team Configuration Options

The teams feature supports several configuration options:

* `maximumTeams`: Limit the number of teams per organization
  ```ts
  teams: {
    enabled: true,
    maximumTeams: 10 // Fixed number
    // OR
    maximumTeams: async ({ organizationId, session }, request) => {
      // Dynamic limit based on organization plan
      const plan = await getPlan(organizationId)
      return plan === 'pro' ? 20 : 5
    },
    maximumMembersPerTeam: 10 // Fixed number
    // OR
    maximumMembersPerTeam: async ({ teamId, session, organizationId }, request) => {
      // Dynamic limit based on team plan
      const plan = await getPlan(organizationId, teamId)
      return plan === 'pro' ? 50 : 10
    },
  }
  ```

* `allowRemovingAllTeams`: Control whether the last team can be removed
  ```ts
  teams: {
    enabled: true,
    allowRemovingAllTeams: false // Prevent removing the last team
  }
  ```

### Team Members

When inviting members to an organization, you can specify a team:

```ts
await authClient.organization.inviteMember({
    email: "<EMAIL>",
    role: "member",
    teamId: "team-id"
})
```

The invited member will be added to the specified team upon accepting the invitation.

### Database Schema

When teams are enabled, new `team` and `teamMember` tables are added to the database.

Table Name: `team`

<DatabaseTable
  fields={[
  {
    name: "id",
    type: "string",
    description: "Unique identifier for each team",
    isPrimaryKey: true
  },
  {
    name: "name",
    type: "string",
    description: "The name of the team"
  },
  {
    name: "organizationId",
    type: "string",
    description: "The ID of the organization",
    isForeignKey: true
  },
  {
    name: "createdAt",
    type: "Date",
    description: "Timestamp of when the team was created"
  },
  {
    name: "updatedAt",
    type: "Date",
    isOptional: true,
    description: "Timestamp of when the team was created"
  },
]}
/>

Table Name: `teamMember`

<DatabaseTable
  fields={[
  {
    name: "id",
    type: "string",
    description: "Unique identifier for each team member",
    isPrimaryKey: true
  },
  {
    name: "teamId",
    type: "string",
    description: "Unique identifier for each team",
    isForeignKey: true
  },
  {
    name: "userId",
    type: "string",
    description: "The ID of the user",
    isForeignKey: true
  },
  {
    name: "createdAt",
    type: "Date",
    description: "Timestamp of when the team member was created"
  },
]}
/>

## Schema

The organization plugin adds the following tables to the database:

### Organization

Table Name: `organization`

<DatabaseTable
  fields={[
  {
    name: "id",
    type: "string",
    description: "Unique identifier for each organization",
    isPrimaryKey: true
  },
  {
    name: "name",
    type: "string",
    description: "The name of the organization"
  },
  {
    name: "slug",
    type: "string",
    description: "The slug of the organization"
  },
  {
    name: "logo",
    type: "string",
    description: "The logo of the organization",
    isOptional: true
  },
  {
    name: "metadata",
    type: "string",
    description: "Additional metadata for the organization",
    isOptional: true
  },
  {
    name: "createdAt",
    type: "Date",
    description: "Timestamp of when the organization was created"
  },
]}
/>

### Member

Table Name: `member`

<DatabaseTable
  fields={[
  {
    name: "id",
    type: "string",
    description: "Unique identifier for each member",
    isPrimaryKey: true
  },
  {
    name: "userId",
    type: "string",
    description: "The ID of the user",
    isForeignKey: true
  },
  {
    name: "organizationId",
    type: "string",
    description: "The ID of the organization",
    isForeignKey: true
  },
  {
    name: "role",
    type: "string",
    description: "The role of the user in the organization"
  },
  {
    name: "createdAt",
    type: "Date",
    description: "Timestamp of when the member was added to the organization"
  },
]}
/>

### Invitation

Table Name: `invitation`

<DatabaseTable
  fields={[
  {
    name: "id",
    type: "string",
    description: "Unique identifier for each invitation",
    isPrimaryKey: true
  },
  {
    name: "email",
    type: "string",
    description: "The email address of the user"
  },
  {
    name: "inviterId",
    type: "string",
    description: "The ID of the inviter",
    isForeignKey: true
  },
  {
    name: "organizationId",
    type: "string",
    description: "The ID of the organization",
    isForeignKey: true
  },
  {
    name: "role",
    type: "string",
    description: "The role of the user in the organization"
  },
  {
    name: "status",
    type: "string",
    description: "The status of the invitation"
  },
  {
    name: "expiresAt",
    type: "Date",
    description: "Timestamp of when the invitation expires"
  },
]}
/>

If teams are enabled, you need to add the following fields to the invitation table:

<DatabaseTable
  fields={[
  {
    name: "teamId",
    type: "string",
    description: "The ID of the team",
    isOptional: true
  },
]}
/>

### Session

Table Name: `session`

You need to add two more fields to the session table to store the active organization ID and the active team ID.

<DatabaseTable
  fields={[
  {
    name: "activeOrganizationId",
    type: "string",
    description: "The ID of the active organization",
    isOptional: true
  },
  {
    name: "activeTeamId",
    type: "string",
    description: "The ID of the active team",
    isOptional: true
  },
]}
/>

### Teams (optional)

Table Name: `team`

<DatabaseTable
  fields={[
  {
    name: "id",
    type: "string",
    description: "Unique identifier for each team",
    isPrimaryKey: true
  },
  {
    name: "name",
    type: "string",
    description: "The name of the team"
  },
  {
    name: "organizationId",
    type: "string",
    description: "The ID of the organization",
    isForeignKey: true
  },
  {
    name: "createdAt",
    type: "Date",
    description: "Timestamp of when the team was created"
  },
  {
    name: "updatedAt",
    type: "Date",
    isOptional: true,
    description: "Timestamp of when the team was created"
  },
]}
/>

Table Name: `teamMember`

<DatabaseTable
  fields={[
  {
    name: "id",
    type: "string",
    description: "Unique identifier for each team member",
    isPrimaryKey: true
  },
  {
    name: "teamId",
    type: "string",
    description: "Unique identifier for each team",
    isForeignKey: true
  },
  {
    name: "userId",
    type: "string",
    description: "The ID of the user",
    isForeignKey: true
  },
  {
    name: "createdAt",
    type: "Date",
    description: "Timestamp of when the team member was created"
  },
]}
/>

Table Name: `invitation`

<DatabaseTable
  fields={[
  {
    name: "teamId",
    type: "string",
    description: "The ID of the team",
    isOptional: true
  },
]}
/>

### Customizing the Schema

To change the schema table name or fields, you can pass `schema` option to the organization plugin.

```ts title="auth.ts"
const auth = betterAuth({
  plugins: [
    organization({
      schema: {
        organization: {
          modelName: "organizations",  //map the organization table to organizations
          fields: {
            name: "title" //map the name field to title
          },
          additionalFields: {
            // Add a new field to the organization table
            myCustomField: {
              type: "string",
              input: true,
              required: false
            }
          }
        }
      }
    })
  ]
})
```

#### Additional Fields

Starting with [Better Auth v1.3](https://github.com/better-auth/better-auth/releases/tag/v1.3.0), you can easily add custom fields to the `organization`, `invitation`, `member`, and `team` tables.

When you add extra fields to a model, the relevant API endpoints will automatically accept and return these new properties. For instance, if you add a custom field to the `organization` table, the `createOrganization` endpoint will include this field in its request and response payloads as needed.

```ts title="auth.ts"
const auth = betterAuth({
  plugins: [
    organization({
      schema: {
        organization: {
          additionalFields: {
            myCustomField: { // [!code highlight]
              type: "string", // [!code highlight]
              input: true, // [!code highlight]
              required: false // [!code highlight]
            } // [!code highlight]
          }
        }
      }
    })
  ]
})
```

For inferring the additional fields, you can use the `inferOrgAdditionalFields` function. This function will infer the additional fields from the auth object type.

```ts title="auth-client.ts"
import { createAuthClient } from "better-auth/client";
import { inferOrgAdditionalFields, organizationClient } from "better-auth/client/plugins";
import type { auth } from "@/auth" // import the auth object type only

const client = createAuthClient({
    plugins: [organizationClient({
        schema: inferOrgAdditionalFields<typeof auth>()
    })]
})
```

if you can't import the auth object type, you can use the `inferOrgAdditionalFields` function without the generic. This function will infer the additional fields from the schema object.

```ts title="auth-client.ts"

const client = createAuthClient({
    plugins: [organizationClient({
        schema: inferOrgAdditionalFields({
          organization: { // [!code highlight]
            additionalFields: {
              newField: { // [!code highlight]
                type: "string", // [!code highlight]
              }, // [!code highlight]
            },
          },
        })
    })]
})

//example usage
await client.organization.create({
    name: "Test",
    slug: "test",
    newField: "123", //this should be allowed
    //@ts-expect-error - this field is not available
    unavalibleField: "123", //this should be not allowed
})
```

## Options

**allowUserToCreateOrganization**: `boolean` | `((user: User) => Promise<boolean> | boolean)` - A function that determines whether a user can create an organization. By default, it's `true`. You can set it to `false` to restrict users from creating organizations.

**organizationLimit**: `number` | `((user: User) => Promise<boolean> | boolean)` - The maximum number of organizations allowed for a user. By default, it's `5`. You can set it to any number you want or a function that returns a boolean.

**creatorRole**: `admin | owner` - The role of the user who creates the organization. By default, it's `owner`. You can set it to `admin`.

**membershipLimit**: `number` - The maximum number of members allowed in an organization. By default, it's `100`. You can set it to any number you want.

**sendInvitationEmail**: `async (data) => Promise<void>` - A function that sends an invitation email to the user.

**invitationExpiresIn** : `number` - How long the invitation link is valid for in seconds. By default, it's 48 hours (2 days).

**cancelPendingInvitationsOnReInvite**: `boolean` - Whether to cancel pending invitations if the user is already invited to the organization. By default, it's `false`.

**invitationLimit**: `number` | `((user: User) => Promise<boolean> | boolean)` - The maximum number of invitations allowed for a user. By default, it's `100`. You can set it to any number you want or a function that returns a boolean.

