Ringkasan Poin A: Background

Pedoman Integrasi Aplikasi (PIA) e-Seal Monitoring System (eMS) merupakan referensi untuk integrasi API antara eMS dan server vendor e-seal. Tujuannya:

Memudahkan integrasi sistem yang standar dan terstruktur.
Mendukung pengiriman data transaksi secara real-time.
Mengurangi human-error dari entri data manual.
Menyajikan data cepat, tepat, dan akurat.
Isi dokumen: Bisnis Proses Integrasi, Teknis Integrasi eMS, Contoh Elemen Data, dan <PERSON>toh JSON.
Ringkasan Poin B: Global Design

API Services eMS memungkinkan pertukaran data antara Direktorat Jenderal Bea dan Cukai dengan vendor e-seal menggunakan protokol HTTP (JSON). Fungsi utama:

Vendor mengirim data e-seal ke eMS.
Vendor meminta data dokumen kepabeanan berdasarkan parameter.
Vendor mengirim data tracking e-seal (sekali di awal perjalanan).
Vendor mengirim posisi dan status e-seal secara periodik.
Data digunakan untuk aplikasi monitoring berbasis web dan mobile oleh pegawai Bea Cukai.
Poin C: Process Specification (Full Copy)

Pertukaran data (host to host) JSON antara sistem eMS dengan sistem milik vendor e-seal menggunakan REST API sebagai protokol komunikasi dan format data JSON.

Berikut ini daftar Method dalam services e-Seal Monitoring System (eMS).


No	Fungsi	URL Development	URL Production	Endpoint	Method
1	Pengiriman data e-seal dari sistem vendor e-seal ke sistem eMS	https://apisdev-gw.beacukai.go.id/tracking-eseal		https://apis-gw.beacukai.go.id/tracking-eseal		eseal/add	POST
2	Permintaan data dokumen kepabeanan dari sistem vendor e-seal ke sistem eMS	https://apisdev-gw.beacukai.go.id/dokumen-eseal-service	https://apis-gw.beacukai.go.id/dokumen-eseal-service	eseal/get-dok-pabean/	GET
3	Pengiriman data tracking dari sistem vendor e-seal ke sistem eMS	https://apisdev-gw.beacukai.go.id/tracking-eseal		https://apis-gw.beacukai.go.id/tracking-eseal		tracking/start	POST
4	Pengiriman data posisi e-seal dari sistem vendor e-seal ke sistem eMS	https://apisdev-gw.beacukai.go.id/position-eseal	https://apis-gw.beacukai.go.id/position-eseal	eseal/update-position	POST
5	Pengiriman data tracking dari sistem vendor e-seal ke sistem eMS	https://apisdev-gw.beacukai.go.id/tracking-eseal		https://apis-gw.beacukai.go.id/tracking-eseal		tracking/stop	POST
6	Pengiriman data perubahan status e-seal dari sistem vendor e-seal ke sistem eMS	https://apisdev-gw.beacukai.go.id/tracking-eseal		https://apis-gw.beacukai.go.id/tracking-eseal		eseal/update-status-device	POST
7	Permintaan data status tracking dari sistem vendor e-seal ke sistem eMS	https://apisdev-gw.beacukai.go.id/tracking-eseal		https://apis-gw.beacukai.go.id/tracking-eseal		tracking/status	GET
Dan berikut ini adalah keterangan method beserta uraian lengkap spesifikasi proses fungsi – fungsi yang ada pada services di atas. Parameter yang menggunakan huruf italic merupakan parameter yang bersifat mandatory.

Method Name: eseal/add

Author: Tim Teknis eMS

Method Type: POST

Method Header: Bearer Token; Content-Type:application/json

Description: Pengiriman data e-seal dari sistem vendor e-seal ke sistem eMS.

Input Parameters (Request Body)


Parameters	Type	Length	Repeat	Description
idVendor	an	-	1	ID Vendor
merk	an	-	1	Merk E-seal
model	an	-	1	Model E-seal
noImei	an	-	1	Nomor IMEI
tipe	an	-	1	Tipe E-seal
token	an	-	1	Token Vendor
Return


Parameters	Type	Length	Repeat	Description
status	an	-	1	Status aksi. Nilai yang mungkin: success, error
message	an	-	1	Keterangan dari status
item	an	-	1	Data yang berhasil dimasukkan. Ket.: hanya muncul jika aksi sukses.
Server: Sistem eMS

Client: Sistem Vendor

Contoh Format JSON (Parameter):

json

Collapse

Wrap

Copy
{
  "idVendor": "",
  "merk": "",
  "model": "",
  "noImei": "",
  "tipe": "",
  "token": ""
}
Contoh Format JSON (Return):

language-上手

Collapse

Wrap

Copy
```json
{
  "status": "success",
  "message": "Berhasil menambahkan data ke tabel td_eseal",
  "item": {
    "idEseal": "",
    "merk": "",
    "model": "",
    "tipe": "",
    "idVendor": "",
    "noImei": "",
    "status": "",
    "wkRekam": "",
    "wkUpdate": ""
  }
}
Method Name: eseal/get-dok-pabean

Author: Tim Teknis eMS

Method Type: GET

Method Header: Bearer Token; Content-Type:application/json

Description: Permintaan data dokumen kepabeanan dari sistem vendor e-seal ke sistem eMS.

Input Parameters (URL Parameters)


Parameters	Type	Length	Repeat	Description
nomor_aju	an	26	1	Nomor Aju
Return


Parameters	Type	Length	Repeat	Description
status	an	-	1	Status aksi. Nilai yang mungkin: success, error
message	an	-	1	Keterangan dari status
items	an	-	n	Data dokumen kepabeanan. Ket.: hanya muncul jika aksi sukses.
Server: Sistem eMS

Client: Sistem Vendor

Contoh Request URL Parameter:

?nomor_aju=16021600008120160415000002

Contoh Format JSON (Return):

json

Collapse

Wrap

Copy
{
  "status": "success",
  "message": "Berhasil mendapatkan dokumen pabean",
  "item": {
    "nomorAju": "",
    "kodeDokumen": "",
    "nomorDaftar": "",
    "tanggalDaftar": "",
    "kodeKantor": "",
    "namaKantor": "",
    "kodeTps": "",
    "namaGudang": "",
    "idPengusaha": "",
    "namaPengusaha": "",
    "uraian": "",
    "kontainer": [
      {
        "nomorKontainer": "",
        "nomorSegel": ""
      }
    ]
  }
}
Method Name: tracking/start

Author: Tim Teknis eMS

Method Type: POST

Method Header: Bearer Token; Content-Type:application/json

Description: Pengiriman data tracking dari sistem vendor e-seal ke sistem eMS.

Input Parameters (Request Body)


Parameters	Type	Length	Repeat	Description
Detil Data Kontainer			1	Berulang (1) Row
alamatAsal	an	-	1	Alamat Asal
alamatTujuan	an	-	1	Alamat Tujuan
idVendor	an	-	1	ID Vendor
jnsKontainer	an	-	1	Jenis Kontainer menggunakan referensi pada Lampiran 3
latitudeAsal	an	-	1	Latitude Asal
latitudeTujuan	an	-	1	Latitude Tujuan
lokasiAsal	an	-	1	Lokasi Asal
lokasiTujuan	an	-	1	Lokasi Tujuan
longitudeAsal	an	-	1	Longitude Asal
longitudeTujuan	an	-	1	Longitude Tujuan
noImei	an	-	1	Nomor IMEI e-Seal
noEseal	an	-	1	Nomor e-Seal bersifat unik per tracking yang dapat diperoleh melalui get-dok-pabean sesuai kontainer yang digunakan. Jika pada get-dok-pabean nomor segel bernilai null maka dapat diisi secara mandiri dengan format kodeKantor-kodeDokumen-nomor
noKontainer	an	-	1	Nomor Kontainer
noPolisi	an	-	1	Nomor Polisi
token	an	-	1	Token Vendor
ukKontainer	an	-	1	Ukuran Kontainer menggunakan referensi pada Lampiran 4
namaDriver	an	-	1	Nama Driver
nomorTeleponDriver	an	-	1	Nomor Telepon Driver
dokumen	n			Data dokumen
jenisMuat	an	-	1	Jenis Muat
jumlahKontainer	an	-	1	Jumlah Kontainer
kodeDokumen	an	-	1	Kode Dokumen. Untuk dokumen PLP dapat diisikan “PLP”
kodeKantor	an	-	1	Kode Kantor
nomorAju	26	-	1	Nomor Aju
nomorDokumen	an	-	1	Nomor Dokumen
tanggalDokumen	Format: yyyy-MM-dd	-	1	Tanggal Dokumen
Return


Parameters	Type	Length	Repeat	Description
status	an	-	1	Status aksi. Nilai yang mungkin: success, error
message	an	-	1	Keterangan dari status
item	an	-	1	Data yang berhasil dimasukkan. Ket.: hanya muncul jika aksi sukses.
Server: Sistem eMS

Client: Sistem Vendor

Contoh Format JSON (Parameter):

json

Collapse

Wrap

Copy
{
  "alamatAsal": "",
  "alamatTujuan": "",
  "idVendor": "",
  "jnsKontainer": "",
  "latitudeAsal": "",
  "latitudeTujuan": "",
  "lokasiAsal": "",
  "lokasiTujuan": "",
  "longitudeAsal": "",
  "longitudeTujuan": "",
  "noImei": "",
  "noEseal": "",
  "noKontainer": "",
  "noPolisi": "",
  "token": "",
  "ukKontainer": "",
  "namaDriver": "",
  "nomorTeleponDriver": "",
  "dokumen": [
    {
      "jenisMuat": "",
      "jumlahKontainer": "",
      "kodeDokumen": "",
      "kodeKantor": "",
      "nomorAju": "",
      "nomorDokumen": "",
      "tanggalDokumen": ""
    }
  ]
}
Method Name: tracking/stop

Author: Tim Teknis eMS

Method Type: POST

Method Header: Bearer Token; Content-Type:application/json

Description: Pengiriman data tracking dari sistem vendor e-seal ke sistem eMS.

Input Parameters (Request Body)


Parameters	Type	Length	Repeat	Description
Detil Data Kontainer			1	Berulang (1) Row
alamatStop	an	26	1	Alamat Saat Tracking Selesai
idVendor	an	75	1	ID Vendor
latitudeStop	an	-	1	Latitude Stop
longitudeStop	an	-	1	Longitude Stop
noImei	an	-	1	Nomor IMEI
noEseal	an	-	1	Nomor e-Seal yang digunakan saat tracking start
token	an	-	1	Token Vendor
Return


Parameters	Type	Length	Repeat	Description
status	an	-	1	Status aksi. Nilai yang mungkin: success, error
message	an	-	1	Keterangan dari status
item	an	-	1	Data yang berhasil dimasukkan. Ket.: hanya muncul jika aksi sukses.
Server: Sistem eMS

Client: Sistem Vendor

Contoh Format JSON (Parameter):

json

Collapse

Wrap

Copy
{
  "alamatStop": "",
  "idVendor": "",
  "latitudeStop": "",
  "longitudeStop": "",
  "noImei": "",
  "noEseal": "",
  "token": ""
}
Method Name: eseal/update-position

Author: Tim Teknis eMS

Method Type: POST

Method Header: Bearer Token; Content-Type:application/json

Description: Menerima lokasi dan status e-seal secara periodik dari sistem vendor e-seal ke sistem eMS.

Input Parameters (Request Body)


Parameters	Type	Length	Repeat	Description
address	an	1000	1	Address
altitude	an	50	1	Posisi Altitude
battery	an	-	1	Daya Baterai
dayaAki	an	-	1	Daya Aki
event	an	-	1	Event e-Seal menggunakan referensi pada Lampiran 2
idVendor	an	-	1	ID Vendor
kota	an	-	1	Kota
latitude	an	-	1	Posisi Latitude
longitude	an	-	1	Posisi Longitude
noImei	an	-	1	Nomor IMEI
noEseal	an	-	1	Nomor e-Seal yang digunakan saat tracking start
provinsi	an	-	1	Provinsi
speed	an	-	1	Kecepatan Kontainer
token	an	-	1	Token Vendor
Return


Parameters	Type	Length	Repeat	Description
status	an	-	1	Status aksi. Nilai yang mungkin: success, error
message	an	-	1	Keterangan dari status
Server: Sistem eMS

Client: Sistem Vendor

Contoh Format JSON (Parameter):

json

Collapse

Wrap

Copy
{
  "address": "",
  "altitude": "",
  "battery": "",
  "dayaAki": "",
  "event": "",
  "idVendor": "",
  "kota": "",
  "latitude": "",
  "longitude": "",
  "noImei": "",
  "noEseal": "",
  "provinsi": "",
  "speed": "",
  "token": ""
}
Contoh Format JSON (Return):

json

Collapse

Wrap

Copy
{
  "status": "success",
  "message": "Berhasil menerima data posisi"
}
Method Name: eseal/update-status-device

Author: Tim Teknis eMS

Method Type: POST

Method Header: Bearer Token; Content-Type:application/json

Description: Pengiriman data perubahan status e-seal dari sistem vendor e-seal ke sistem eMS.

Input Parameters (Request Body)


Parameters	Type	Length	Repeat	Description
idVendor	an	-	1	ID Vendor
noImei	an	-	1	Nomor IMEI
status	an	-	1	Status E-seal menggunakan referensi pada Lampiran 5
token	an	-	1	Token Vendor
Return


Parameters	Type	Length	Repeat	Description
status	an	-	1	Status aksi. Nilai yang mungkin: success, error
message	an	-	1	Keterangan dari status
item	an	-	1	Data yang berhasil dimasukkan. Ket.: hanya muncul jika aksi sukses.
Server: Sistem eMS

Client: Sistem Vendor

Contoh Format JSON (Parameter):

json

Collapse

Wrap

Copy
{
  "idVendor": "",
  "noImei": "",
  "status": "",
  "token": ""
}
Contoh Format JSON (Return):

json

Collapse

Wrap

Copy
{
  "status": "success",
  "message": "Berhasil mengubah data di tabel td_eseal"
}
Method Name: tracking/status

Author: Tim Teknis eMS

Method Type: GET

Method Header: Bearer Token; Content-Type:application/json

Description: Permintaan data dokumen kepabeanan dari sistem vendor e-seal ke sistem eMS.

Input Parameters (URL Parameters)


Parameters	Type	Length	Repeat	Description
idVendor	an	26	1	Nomor Aju
noEseal	an	-	1	Nomor E-Seal
token	an	-	1	Token
Return


Parameters	Type	Length	Repeat	Description
status	an	-	1	Status aksi. Nilai yang mungkin: success, error
message	an	-	1	Keterangan dari status
items	an	-	n	Data dokumen kepabeanan. Ket.: hanya muncul jika aksi sukses.
Server: Sistem eMS

Client: Sistem Vendor

Contoh Request URL Parameter:

?idVendor=373c041c-f9b8-491f-a4f6-7cc47a0569d3&noEseal=ESEALTEST123098&token=919253c8-d0e1-4780-89d0-e91f77e89855

Contoh Format JSON (Return):

json

Collapse

Wrap

Copy
{
  "status": "success",
  "message": "Berhasil mendapatkan data tracking untuk nomor eseal ESEALTEST-123098",
  "item": {
    "start": "success",
    "updatePosition": 0,
    "stop": "success"
  }
}