#!/bin/bash

# Deployment script untuk E-Seal Monitor
# Usage: ./deploy.sh [tag]

TAG=${1:-latest}
IMAGE_NAME="eseal-monitor"

echo "🚀 Deploying E-Seal Monitor..."
echo "📦 Image: $IMAGE_NAME:$TAG"

# 1. Stop existing container if running
echo "🛑 Stopping existing container..."
docker stop eseal-monitor 2>/dev/null || true
docker rm eseal-monitor 2>/dev/null || true

# 2. Run new container
echo "▶️ Starting new container..."
docker run -d \
  --name eseal-monitor \
  --platform linux/amd64 \
  -p 3000:3000 \
  --restart unless-stopped \
  -e NODE_ENV=production \
  $IMAGE_NAME:$TAG

# 3. Check if container is running
echo "🔍 Checking container status..."
sleep 3
if docker ps | grep -q eseal-monitor; then
  echo "✅ Container is running successfully!"
  echo "🌐 Application available at: http://localhost:3000"
  echo "📊 Container logs:"
  docker logs eseal-monitor --tail 10
else
  echo "❌ Container failed to start!"
  echo "📊 Container logs:"
  docker logs eseal-monitor
  exit 1
fi
